{"name": "vfile", "version": "6.0.3", "description": "Virtual file format for text processing", "license": "MIT", "keywords": ["vfile", "virtual", "file", "text", "processing", "message", "warning", "error", "remark", "retext", "rehype"], "repository": "vfile/vfile", "bugs": "https://github.com/vfile/vfile/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> Sorhus <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": "./index.js", "imports": {"#minpath": {"node": "./lib/minpath.js", "default": "./lib/minpath.browser.js"}, "#minproc": {"node": "./lib/minproc.js", "default": "./lib/minproc.browser.js"}, "#minurl": {"node": "./lib/minurl.js", "default": "./lib/minurl.browser.js"}}, "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/unist": "^3.0.0", "vfile-message": "^4.0.0"}, "devDependencies": {"@types/node": "^22.0.0", "c8": "^10.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^10.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.59.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm", ["remark-lint-no-html", false]]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"overrides": [{"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/array-type": ["error", {"default": "generic"}], "@typescript-eslint/ban-types": ["error", {"extendDefaults": true}], "@typescript-eslint/consistent-type-definitions": ["error", "interface"]}}], "prettier": true, "rules": {"logical-assignment-operators": "off", "unicorn/prefer-at": "off", "unicorn/prefer-string-replace-all": "off"}}}