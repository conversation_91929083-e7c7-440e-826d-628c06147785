[{"/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/index.tsx": "1", "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/reportWebVitals.ts": "2", "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/App.tsx": "3", "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/pages/ImageDetailPage.tsx": "4", "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/contexts/ImageContext.tsx": "5", "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/pages/HomePage.tsx": "6", "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/AnnotationViewer.tsx": "7", "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/AnnotationEditor.tsx": "8", "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/ImageUpload.tsx": "9", "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/CollectionTree.tsx": "10"}, {"size": 554, "mtime": 1754897235843, "results": "11", "hashOfConfig": "12"}, {"size": 425, "mtime": 1754897235843, "results": "13", "hashOfConfig": "12"}, {"size": 612, "mtime": 1754897271096, "results": "14", "hashOfConfig": "12"}, {"size": 7270, "mtime": 1754898242334, "results": "15", "hashOfConfig": "12"}, {"size": 5924, "mtime": 1754898127796, "results": "16", "hashOfConfig": "12"}, {"size": 2796, "mtime": 1754898154087, "results": "17", "hashOfConfig": "12"}, {"size": 666, "mtime": 1754897433579, "results": "18", "hashOfConfig": "12"}, {"size": 3082, "mtime": 1754897562492, "results": "19", "hashOfConfig": "12"}, {"size": 2521, "mtime": 1754897417531, "results": "20", "hashOfConfig": "12"}, {"size": 3773, "mtime": 1754897388938, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "215381", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/index.tsx", [], [], "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/reportWebVitals.ts", [], [], "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/App.tsx", [], [], "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/pages/ImageDetailPage.tsx", ["52"], [], "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/contexts/ImageContext.tsx", [], [], "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/pages/HomePage.tsx", [], [], "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/AnnotationViewer.tsx", [], [], "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/AnnotationEditor.tsx", [], [], "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/ImageUpload.tsx", [], [], "/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/CollectionTree.tsx", [], [], {"ruleId": "53", "severity": 1, "message": "54", "line": 37, "column": 11, "nodeType": "55", "messageId": "56", "endLine": 37, "endColumn": 24}, "@typescript-eslint/no-unused-vars", "'containerRect' is assigned a value but never used.", "Identifier", "unusedVar"]