{"ast": null, "code": "/**\n * @import {\n *   Code,\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { asciiAlphanumeric, asciiAlpha, markdownLineEndingOrSpace, markdownLineEnding, markdownSpace } from 'micromark-util-character';\nimport { htmlBlockNames, htmlRawNames } from 'micromark-util-html-tag-name';\nimport { codes, constants, types } from 'micromark-util-symbol';\nimport { blankLine } from './blank-line.js';\n\n/** @type {Construct} */\nexport const htmlFlow = {\n  concrete: true,\n  name: 'htmlFlow',\n  resolveTo: resolveToHtmlFlow,\n  tokenize: tokenizeHtmlFlow\n};\n\n/** @type {Construct} */\nconst blankLineBefore = {\n  partial: true,\n  tokenize: tokenizeBlankLineBefore\n};\nconst nonLazyContinuationStart = {\n  partial: true,\n  tokenize: tokenizeNonLazyContinuationStart\n};\n\n/** @type {Resolver} */\nfunction resolveToHtmlFlow(events) {\n  let index = events.length;\n  while (index--) {\n    if (events[index][0] === 'enter' && events[index][1].type === types.htmlFlow) {\n      break;\n    }\n  }\n  if (index > 1 && events[index - 2][1].type === types.linePrefix) {\n    // Add the prefix start to the HTML token.\n    events[index][1].start = events[index - 2][1].start;\n    // Add the prefix start to the HTML line token.\n    events[index + 1][1].start = events[index - 2][1].start;\n    // Remove the line prefix.\n    events.splice(index - 2, 2);\n  }\n  return events;\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHtmlFlow(effects, ok, nok) {\n  const self = this;\n  /** @type {number} */\n  let marker;\n  /** @type {boolean} */\n  let closingTag;\n  /** @type {string} */\n  let buffer;\n  /** @type {number} */\n  let index;\n  /** @type {Code} */\n  let markerB;\n  return start;\n\n  /**\n   * Start of HTML (flow).\n   *\n   * ```markdown\n   * > | <x />\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse indent like `markdown-rs`.\n    return before(code);\n  }\n\n  /**\n   * At `<`, after optional whitespace.\n   *\n   * ```markdown\n   * > | <x />\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(code === codes.lessThan, 'expected `<`');\n    effects.enter(types.htmlFlow);\n    effects.enter(types.htmlFlowData);\n    effects.consume(code);\n    return open;\n  }\n\n  /**\n   * After `<`, at tag name or other stuff.\n   *\n   * ```markdown\n   * > | <x />\n   *      ^\n   * > | <!doctype>\n   *      ^\n   * > | <!--xxx-->\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.exclamationMark) {\n      effects.consume(code);\n      return declarationOpen;\n    }\n    if (code === codes.slash) {\n      effects.consume(code);\n      closingTag = true;\n      return tagCloseStart;\n    }\n    if (code === codes.questionMark) {\n      effects.consume(code);\n      marker = constants.htmlInstruction;\n      // To do:\n      // tokenizer.concrete = true\n      // To do: use `markdown-rs` style interrupt.\n      // While we’re in an instruction instead of a declaration, we’re on a `?`\n      // right now, so we do need to search for `>`, similar to declarations.\n      return self.interrupt ? ok : continuationDeclarationInside;\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      assert(code !== null); // Always the case.\n      effects.consume(code);\n      buffer = String.fromCharCode(code);\n      return tagName;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After `<!`, at declaration, comment, or CDATA.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *       ^\n   * > | <!--xxx-->\n   *       ^\n   * > | <![CDATA[>&<]]>\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declarationOpen(code) {\n    if (code === codes.dash) {\n      effects.consume(code);\n      marker = constants.htmlComment;\n      return commentOpenInside;\n    }\n    if (code === codes.leftSquareBracket) {\n      effects.consume(code);\n      marker = constants.htmlCdata;\n      index = 0;\n      return cdataOpenInside;\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code);\n      marker = constants.htmlDeclaration;\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuationDeclarationInside;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After `<!-`, inside a comment, at another `-`.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentOpenInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code);\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuationDeclarationInside;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After `<![`, inside CDATA, expecting `CDATA[`.\n   *\n   * ```markdown\n   * > | <![CDATA[>&<]]>\n   *        ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataOpenInside(code) {\n    const value = constants.cdataOpeningString;\n    if (code === value.charCodeAt(index++)) {\n      effects.consume(code);\n      if (index === value.length) {\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok : continuation;\n      }\n      return cdataOpenInside;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After `</`, in closing tag, at tag name.\n   *\n   * ```markdown\n   * > | </x>\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseStart(code) {\n    if (asciiAlpha(code)) {\n      assert(code !== null); // Always the case.\n      effects.consume(code);\n      buffer = String.fromCharCode(code);\n      return tagName;\n    }\n    return nok(code);\n  }\n\n  /**\n   * In tag name.\n   *\n   * ```markdown\n   * > | <ab>\n   *      ^^\n   * > | </ab>\n   *       ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagName(code) {\n    if (code === codes.eof || code === codes.slash || code === codes.greaterThan || markdownLineEndingOrSpace(code)) {\n      const slash = code === codes.slash;\n      const name = buffer.toLowerCase();\n      if (!slash && !closingTag && htmlRawNames.includes(name)) {\n        marker = constants.htmlRaw;\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok(code) : continuation(code);\n      }\n      if (htmlBlockNames.includes(buffer.toLowerCase())) {\n        marker = constants.htmlBasic;\n        if (slash) {\n          effects.consume(code);\n          return basicSelfClosing;\n        }\n\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok(code) : continuation(code);\n      }\n      marker = constants.htmlComplete;\n      // Do not support complete HTML when interrupting.\n      return self.interrupt && !self.parser.lazy[self.now().line] ? nok(code) : closingTag ? completeClosingTagAfter(code) : completeAttributeNameBefore(code);\n    }\n\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code);\n      buffer += String.fromCharCode(code);\n      return tagName;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After closing slash of a basic tag name.\n   *\n   * ```markdown\n   * > | <div/>\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function basicSelfClosing(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code);\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuation;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After closing slash of a complete tag name.\n   *\n   * ```markdown\n   * > | <x/>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeClosingTagAfter(code) {\n    if (markdownSpace(code)) {\n      effects.consume(code);\n      return completeClosingTagAfter;\n    }\n    return completeEnd(code);\n  }\n\n  /**\n   * At an attribute name.\n   *\n   * At first, this state is used after a complete tag name, after whitespace,\n   * where it expects optional attributes or the end of the tag.\n   * It is also reused after attributes, when expecting more optional\n   * attributes.\n   *\n   * ```markdown\n   * > | <a />\n   *        ^\n   * > | <a :b>\n   *        ^\n   * > | <a _b>\n   *        ^\n   * > | <a b>\n   *        ^\n   * > | <a >\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeNameBefore(code) {\n    if (code === codes.slash) {\n      effects.consume(code);\n      return completeEnd;\n    }\n\n    // ASCII alphanumerical and `:` and `_`.\n    if (code === codes.colon || code === codes.underscore || asciiAlpha(code)) {\n      effects.consume(code);\n      return completeAttributeName;\n    }\n    if (markdownSpace(code)) {\n      effects.consume(code);\n      return completeAttributeNameBefore;\n    }\n    return completeEnd(code);\n  }\n\n  /**\n   * In attribute name.\n   *\n   * ```markdown\n   * > | <a :b>\n   *         ^\n   * > | <a _b>\n   *         ^\n   * > | <a b>\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeName(code) {\n    // ASCII alphanumerical and `-`, `.`, `:`, and `_`.\n    if (code === codes.dash || code === codes.dot || code === codes.colon || code === codes.underscore || asciiAlphanumeric(code)) {\n      effects.consume(code);\n      return completeAttributeName;\n    }\n    return completeAttributeNameAfter(code);\n  }\n\n  /**\n   * After attribute name, at an optional initializer, the end of the tag, or\n   * whitespace.\n   *\n   * ```markdown\n   * > | <a b>\n   *         ^\n   * > | <a b=c>\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeNameAfter(code) {\n    if (code === codes.equalsTo) {\n      effects.consume(code);\n      return completeAttributeValueBefore;\n    }\n    if (markdownSpace(code)) {\n      effects.consume(code);\n      return completeAttributeNameAfter;\n    }\n    return completeAttributeNameBefore(code);\n  }\n\n  /**\n   * Before unquoted, double quoted, or single quoted attribute value, allowing\n   * whitespace.\n   *\n   * ```markdown\n   * > | <a b=c>\n   *          ^\n   * > | <a b=\"c\">\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueBefore(code) {\n    if (code === codes.eof || code === codes.lessThan || code === codes.equalsTo || code === codes.greaterThan || code === codes.graveAccent) {\n      return nok(code);\n    }\n    if (code === codes.quotationMark || code === codes.apostrophe) {\n      effects.consume(code);\n      markerB = code;\n      return completeAttributeValueQuoted;\n    }\n    if (markdownSpace(code)) {\n      effects.consume(code);\n      return completeAttributeValueBefore;\n    }\n    return completeAttributeValueUnquoted(code);\n  }\n\n  /**\n   * In double or single quoted attribute value.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *           ^\n   * > | <a b='c'>\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueQuoted(code) {\n    if (code === markerB) {\n      effects.consume(code);\n      markerB = null;\n      return completeAttributeValueQuotedAfter;\n    }\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return nok(code);\n    }\n    effects.consume(code);\n    return completeAttributeValueQuoted;\n  }\n\n  /**\n   * In unquoted attribute value.\n   *\n   * ```markdown\n   * > | <a b=c>\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueUnquoted(code) {\n    if (code === codes.eof || code === codes.quotationMark || code === codes.apostrophe || code === codes.slash || code === codes.lessThan || code === codes.equalsTo || code === codes.greaterThan || code === codes.graveAccent || markdownLineEndingOrSpace(code)) {\n      return completeAttributeNameAfter(code);\n    }\n    effects.consume(code);\n    return completeAttributeValueUnquoted;\n  }\n\n  /**\n   * After double or single quoted attribute value, before whitespace or the\n   * end of the tag.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueQuotedAfter(code) {\n    if (code === codes.slash || code === codes.greaterThan || markdownSpace(code)) {\n      return completeAttributeNameBefore(code);\n    }\n    return nok(code);\n  }\n\n  /**\n   * In certain circumstances of a complete tag where only an `>` is allowed.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeEnd(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code);\n      return completeAfter;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After `>` in a complete tag.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAfter(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return continuation(code);\n    }\n    if (markdownSpace(code)) {\n      effects.consume(code);\n      return completeAfter;\n    }\n    return nok(code);\n  }\n\n  /**\n   * In continuation of any HTML kind.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuation(code) {\n    if (code === codes.dash && marker === constants.htmlComment) {\n      effects.consume(code);\n      return continuationCommentInside;\n    }\n    if (code === codes.lessThan && marker === constants.htmlRaw) {\n      effects.consume(code);\n      return continuationRawTagOpen;\n    }\n    if (code === codes.greaterThan && marker === constants.htmlDeclaration) {\n      effects.consume(code);\n      return continuationClose;\n    }\n    if (code === codes.questionMark && marker === constants.htmlInstruction) {\n      effects.consume(code);\n      return continuationDeclarationInside;\n    }\n    if (code === codes.rightSquareBracket && marker === constants.htmlCdata) {\n      effects.consume(code);\n      return continuationCdataInside;\n    }\n    if (markdownLineEnding(code) && (marker === constants.htmlBasic || marker === constants.htmlComplete)) {\n      effects.exit(types.htmlFlowData);\n      return effects.check(blankLineBefore, continuationAfter, continuationStart)(code);\n    }\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.htmlFlowData);\n      return continuationStart(code);\n    }\n    effects.consume(code);\n    return continuation;\n  }\n\n  /**\n   * In continuation, at eol.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   *   | asd\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationStart(code) {\n    return effects.check(nonLazyContinuationStart, continuationStartNonLazy, continuationAfter)(code);\n  }\n\n  /**\n   * In continuation, at eol, before non-lazy content.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   *   | asd\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationStartNonLazy(code) {\n    assert(markdownLineEnding(code));\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return continuationBefore;\n  }\n\n  /**\n   * In continuation, before non-lazy content.\n   *\n   * ```markdown\n   *   | <x>\n   * > | asd\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return continuationStart(code);\n    }\n    effects.enter(types.htmlFlowData);\n    return continuation(code);\n  }\n\n  /**\n   * In comment continuation, after one `-`, expecting another.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationCommentInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code);\n      return continuationDeclarationInside;\n    }\n    return continuation(code);\n  }\n\n  /**\n   * In raw continuation, after `<`, at `/`.\n   *\n   * ```markdown\n   * > | <script>console.log(1)</script>\n   *                            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationRawTagOpen(code) {\n    if (code === codes.slash) {\n      effects.consume(code);\n      buffer = '';\n      return continuationRawEndTag;\n    }\n    return continuation(code);\n  }\n\n  /**\n   * In raw continuation, after `</`, in a raw tag name.\n   *\n   * ```markdown\n   * > | <script>console.log(1)</script>\n   *                             ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationRawEndTag(code) {\n    if (code === codes.greaterThan) {\n      const name = buffer.toLowerCase();\n      if (htmlRawNames.includes(name)) {\n        effects.consume(code);\n        return continuationClose;\n      }\n      return continuation(code);\n    }\n    if (asciiAlpha(code) && buffer.length < constants.htmlRawSizeMax) {\n      assert(code !== null); // Always the case.\n      effects.consume(code);\n      buffer += String.fromCharCode(code);\n      return continuationRawEndTag;\n    }\n    return continuation(code);\n  }\n\n  /**\n   * In cdata continuation, after `]`, expecting `]>`.\n   *\n   * ```markdown\n   * > | <![CDATA[>&<]]>\n   *                  ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationCdataInside(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code);\n      return continuationDeclarationInside;\n    }\n    return continuation(code);\n  }\n\n  /**\n   * In declaration or instruction continuation, at `>`.\n   *\n   * ```markdown\n   * > | <!-->\n   *         ^\n   * > | <?>\n   *       ^\n   * > | <!q>\n   *        ^\n   * > | <!--ab-->\n   *             ^\n   * > | <![CDATA[>&<]]>\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationDeclarationInside(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code);\n      return continuationClose;\n    }\n\n    // More dashes.\n    if (code === codes.dash && marker === constants.htmlComment) {\n      effects.consume(code);\n      return continuationDeclarationInside;\n    }\n    return continuation(code);\n  }\n\n  /**\n   * In closed continuation: everything we get until the eol/eof is part of it.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationClose(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.htmlFlowData);\n      return continuationAfter(code);\n    }\n    effects.consume(code);\n    return continuationClose;\n  }\n\n  /**\n   * Done.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationAfter(code) {\n    effects.exit(types.htmlFlow);\n    // // Feel free to interrupt.\n    // tokenizer.interrupt = false\n    // // No longer concrete.\n    // tokenizer.concrete = false\n    return ok(code);\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuationStart(effects, ok, nok) {\n  const self = this;\n  return start;\n\n  /**\n   * At eol, before continuation.\n   *\n   * ```markdown\n   * > | * ```js\n   *            ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding);\n      effects.consume(code);\n      effects.exit(types.lineEnding);\n      return after;\n    }\n    return nok(code);\n  }\n\n  /**\n   * A continuation.\n   *\n   * ```markdown\n   *   | * ```js\n   * > | b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code);\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLineBefore(effects, ok, nok) {\n  return start;\n\n  /**\n   * Before eol, expecting blank line.\n   *\n   * ```markdown\n   * > | <div>\n   *          ^\n   *   |\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(markdownLineEnding(code), 'expected a line ending');\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return effects.attempt(blankLine, ok, nok);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "asciiAlphanumeric", "asciiAlpha", "markdownLineEndingOrSpace", "markdownLineEnding", "markdownSpace", "htmlBlockNames", "htmlRawNames", "codes", "constants", "types", "blankLine", "htmlFlow", "concrete", "name", "resolveTo", "resolveToHtmlFlow", "tokenize", "tokenizeHtmlFlow", "blankLineBefore", "partial", "tokenizeBlankLineBefore", "nonLazyContinuationStart", "tokenizeNonLazyContinuationStart", "events", "index", "length", "type", "linePrefix", "start", "splice", "effects", "nok", "self", "marker", "closingTag", "buffer", "markerB", "code", "before", "lessThan", "enter", "htmlFlowData", "consume", "open", "exclamationMark", "declarationOpen", "slash", "tagCloseStart", "questionMark", "htmlInstruction", "interrupt", "continuationDeclarationInside", "String", "fromCharCode", "tagName", "dash", "htmlComment", "commentOpenInside", "leftSquareBracket", "htmlCdata", "cdataOpenInside", "htmlDeclaration", "value", "cdataOpeningString", "charCodeAt", "continuation", "eof", "greaterThan", "toLowerCase", "includes", "htmlRaw", "htmlBasic", "basicSelfClosing", "htmlComplete", "parser", "lazy", "now", "line", "completeClosingTagAfter", "completeAttributeNameBefore", "completeEnd", "colon", "underscore", "completeAttributeName", "dot", "completeAttributeNameAfter", "equalsTo", "completeAttributeValueBefore", "graveAccent", "quotationMark", "apostrophe", "completeAttributeValueQuoted", "completeAttributeValueUnquoted", "completeAttributeValueQuotedAfter", "completeAfter", "continuationCommentInside", "continuationRawTagOpen", "continuationClose", "rightSquareBracket", "continuationCdataInside", "exit", "check", "continuationAfter", "continuationStart", "continuationStartNonLazy", "lineEnding", "continuationBefore", "continuationRawEndTag", "htmlRawSizeMax", "after", "attempt"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark-core-commonmark/dev/lib/html-flow.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {\n  asciiAlphanumeric,\n  asciiAlpha,\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {htmlBlockNames, htmlRawNames} from 'micromark-util-html-tag-name'\nimport {codes, constants, types} from 'micromark-util-symbol'\nimport {blankLine} from './blank-line.js'\n\n/** @type {Construct} */\nexport const htmlFlow = {\n  concrete: true,\n  name: 'htmlFlow',\n  resolveTo: resolveToHtmlFlow,\n  tokenize: tokenizeHtmlFlow\n}\n\n/** @type {Construct} */\nconst blankLineBefore = {partial: true, tokenize: tokenizeBlankLineBefore}\nconst nonLazyContinuationStart = {\n  partial: true,\n  tokenize: tokenizeNonLazyContinuationStart\n}\n\n/** @type {Resolver} */\nfunction resolveToHtmlFlow(events) {\n  let index = events.length\n\n  while (index--) {\n    if (\n      events[index][0] === 'enter' &&\n      events[index][1].type === types.htmlFlow\n    ) {\n      break\n    }\n  }\n\n  if (index > 1 && events[index - 2][1].type === types.linePrefix) {\n    // Add the prefix start to the HTML token.\n    events[index][1].start = events[index - 2][1].start\n    // Add the prefix start to the HTML line token.\n    events[index + 1][1].start = events[index - 2][1].start\n    // Remove the line prefix.\n    events.splice(index - 2, 2)\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHtmlFlow(effects, ok, nok) {\n  const self = this\n  /** @type {number} */\n  let marker\n  /** @type {boolean} */\n  let closingTag\n  /** @type {string} */\n  let buffer\n  /** @type {number} */\n  let index\n  /** @type {Code} */\n  let markerB\n\n  return start\n\n  /**\n   * Start of HTML (flow).\n   *\n   * ```markdown\n   * > | <x />\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse indent like `markdown-rs`.\n    return before(code)\n  }\n\n  /**\n   * At `<`, after optional whitespace.\n   *\n   * ```markdown\n   * > | <x />\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(code === codes.lessThan, 'expected `<`')\n    effects.enter(types.htmlFlow)\n    effects.enter(types.htmlFlowData)\n    effects.consume(code)\n    return open\n  }\n\n  /**\n   * After `<`, at tag name or other stuff.\n   *\n   * ```markdown\n   * > | <x />\n   *      ^\n   * > | <!doctype>\n   *      ^\n   * > | <!--xxx-->\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.exclamationMark) {\n      effects.consume(code)\n      return declarationOpen\n    }\n\n    if (code === codes.slash) {\n      effects.consume(code)\n      closingTag = true\n      return tagCloseStart\n    }\n\n    if (code === codes.questionMark) {\n      effects.consume(code)\n      marker = constants.htmlInstruction\n      // To do:\n      // tokenizer.concrete = true\n      // To do: use `markdown-rs` style interrupt.\n      // While we’re in an instruction instead of a declaration, we’re on a `?`\n      // right now, so we do need to search for `>`, similar to declarations.\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      assert(code !== null) // Always the case.\n      effects.consume(code)\n      buffer = String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!`, at declaration, comment, or CDATA.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *       ^\n   * > | <!--xxx-->\n   *       ^\n   * > | <![CDATA[>&<]]>\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declarationOpen(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      marker = constants.htmlComment\n      return commentOpenInside\n    }\n\n    if (code === codes.leftSquareBracket) {\n      effects.consume(code)\n      marker = constants.htmlCdata\n      index = 0\n      return cdataOpenInside\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      marker = constants.htmlDeclaration\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!-`, inside a comment, at another `-`.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentOpenInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<![`, inside CDATA, expecting `CDATA[`.\n   *\n   * ```markdown\n   * > | <![CDATA[>&<]]>\n   *        ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataOpenInside(code) {\n    const value = constants.cdataOpeningString\n\n    if (code === value.charCodeAt(index++)) {\n      effects.consume(code)\n\n      if (index === value.length) {\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok : continuation\n      }\n\n      return cdataOpenInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `</`, in closing tag, at tag name.\n   *\n   * ```markdown\n   * > | </x>\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseStart(code) {\n    if (asciiAlpha(code)) {\n      assert(code !== null) // Always the case.\n      effects.consume(code)\n      buffer = String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In tag name.\n   *\n   * ```markdown\n   * > | <ab>\n   *      ^^\n   * > | </ab>\n   *       ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagName(code) {\n    if (\n      code === codes.eof ||\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      const slash = code === codes.slash\n      const name = buffer.toLowerCase()\n\n      if (!slash && !closingTag && htmlRawNames.includes(name)) {\n        marker = constants.htmlRaw\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok(code) : continuation(code)\n      }\n\n      if (htmlBlockNames.includes(buffer.toLowerCase())) {\n        marker = constants.htmlBasic\n\n        if (slash) {\n          effects.consume(code)\n          return basicSelfClosing\n        }\n\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok(code) : continuation(code)\n      }\n\n      marker = constants.htmlComplete\n      // Do not support complete HTML when interrupting.\n      return self.interrupt && !self.parser.lazy[self.now().line]\n        ? nok(code)\n        : closingTag\n          ? completeClosingTagAfter(code)\n          : completeAttributeNameBefore(code)\n    }\n\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      buffer += String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After closing slash of a basic tag name.\n   *\n   * ```markdown\n   * > | <div/>\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function basicSelfClosing(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuation\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After closing slash of a complete tag name.\n   *\n   * ```markdown\n   * > | <x/>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeClosingTagAfter(code) {\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeClosingTagAfter\n    }\n\n    return completeEnd(code)\n  }\n\n  /**\n   * At an attribute name.\n   *\n   * At first, this state is used after a complete tag name, after whitespace,\n   * where it expects optional attributes or the end of the tag.\n   * It is also reused after attributes, when expecting more optional\n   * attributes.\n   *\n   * ```markdown\n   * > | <a />\n   *        ^\n   * > | <a :b>\n   *        ^\n   * > | <a _b>\n   *        ^\n   * > | <a b>\n   *        ^\n   * > | <a >\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeNameBefore(code) {\n    if (code === codes.slash) {\n      effects.consume(code)\n      return completeEnd\n    }\n\n    // ASCII alphanumerical and `:` and `_`.\n    if (code === codes.colon || code === codes.underscore || asciiAlpha(code)) {\n      effects.consume(code)\n      return completeAttributeName\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeNameBefore\n    }\n\n    return completeEnd(code)\n  }\n\n  /**\n   * In attribute name.\n   *\n   * ```markdown\n   * > | <a :b>\n   *         ^\n   * > | <a _b>\n   *         ^\n   * > | <a b>\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeName(code) {\n    // ASCII alphanumerical and `-`, `.`, `:`, and `_`.\n    if (\n      code === codes.dash ||\n      code === codes.dot ||\n      code === codes.colon ||\n      code === codes.underscore ||\n      asciiAlphanumeric(code)\n    ) {\n      effects.consume(code)\n      return completeAttributeName\n    }\n\n    return completeAttributeNameAfter(code)\n  }\n\n  /**\n   * After attribute name, at an optional initializer, the end of the tag, or\n   * whitespace.\n   *\n   * ```markdown\n   * > | <a b>\n   *         ^\n   * > | <a b=c>\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeNameAfter(code) {\n    if (code === codes.equalsTo) {\n      effects.consume(code)\n      return completeAttributeValueBefore\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeNameAfter\n    }\n\n    return completeAttributeNameBefore(code)\n  }\n\n  /**\n   * Before unquoted, double quoted, or single quoted attribute value, allowing\n   * whitespace.\n   *\n   * ```markdown\n   * > | <a b=c>\n   *          ^\n   * > | <a b=\"c\">\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueBefore(code) {\n    if (\n      code === codes.eof ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.greaterThan ||\n      code === codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.quotationMark || code === codes.apostrophe) {\n      effects.consume(code)\n      markerB = code\n      return completeAttributeValueQuoted\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeValueBefore\n    }\n\n    return completeAttributeValueUnquoted(code)\n  }\n\n  /**\n   * In double or single quoted attribute value.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *           ^\n   * > | <a b='c'>\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueQuoted(code) {\n    if (code === markerB) {\n      effects.consume(code)\n      markerB = null\n      return completeAttributeValueQuotedAfter\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return completeAttributeValueQuoted\n  }\n\n  /**\n   * In unquoted attribute value.\n   *\n   * ```markdown\n   * > | <a b=c>\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueUnquoted(code) {\n    if (\n      code === codes.eof ||\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.slash ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.greaterThan ||\n      code === codes.graveAccent ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return completeAttributeNameAfter(code)\n    }\n\n    effects.consume(code)\n    return completeAttributeValueUnquoted\n  }\n\n  /**\n   * After double or single quoted attribute value, before whitespace or the\n   * end of the tag.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueQuotedAfter(code) {\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownSpace(code)\n    ) {\n      return completeAttributeNameBefore(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In certain circumstances of a complete tag where only an `>` is allowed.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeEnd(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      return completeAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `>` in a complete tag.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAfter(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return continuation(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In continuation of any HTML kind.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuation(code) {\n    if (code === codes.dash && marker === constants.htmlComment) {\n      effects.consume(code)\n      return continuationCommentInside\n    }\n\n    if (code === codes.lessThan && marker === constants.htmlRaw) {\n      effects.consume(code)\n      return continuationRawTagOpen\n    }\n\n    if (code === codes.greaterThan && marker === constants.htmlDeclaration) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    if (code === codes.questionMark && marker === constants.htmlInstruction) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    if (code === codes.rightSquareBracket && marker === constants.htmlCdata) {\n      effects.consume(code)\n      return continuationCdataInside\n    }\n\n    if (\n      markdownLineEnding(code) &&\n      (marker === constants.htmlBasic || marker === constants.htmlComplete)\n    ) {\n      effects.exit(types.htmlFlowData)\n      return effects.check(\n        blankLineBefore,\n        continuationAfter,\n        continuationStart\n      )(code)\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.htmlFlowData)\n      return continuationStart(code)\n    }\n\n    effects.consume(code)\n    return continuation\n  }\n\n  /**\n   * In continuation, at eol.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   *   | asd\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationStart(code) {\n    return effects.check(\n      nonLazyContinuationStart,\n      continuationStartNonLazy,\n      continuationAfter\n    )(code)\n  }\n\n  /**\n   * In continuation, at eol, before non-lazy content.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   *   | asd\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationStartNonLazy(code) {\n    assert(markdownLineEnding(code))\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return continuationBefore\n  }\n\n  /**\n   * In continuation, before non-lazy content.\n   *\n   * ```markdown\n   *   | <x>\n   * > | asd\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return continuationStart(code)\n    }\n\n    effects.enter(types.htmlFlowData)\n    return continuation(code)\n  }\n\n  /**\n   * In comment continuation, after one `-`, expecting another.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationCommentInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In raw continuation, after `<`, at `/`.\n   *\n   * ```markdown\n   * > | <script>console.log(1)</script>\n   *                            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationRawTagOpen(code) {\n    if (code === codes.slash) {\n      effects.consume(code)\n      buffer = ''\n      return continuationRawEndTag\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In raw continuation, after `</`, in a raw tag name.\n   *\n   * ```markdown\n   * > | <script>console.log(1)</script>\n   *                             ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationRawEndTag(code) {\n    if (code === codes.greaterThan) {\n      const name = buffer.toLowerCase()\n\n      if (htmlRawNames.includes(name)) {\n        effects.consume(code)\n        return continuationClose\n      }\n\n      return continuation(code)\n    }\n\n    if (asciiAlpha(code) && buffer.length < constants.htmlRawSizeMax) {\n      assert(code !== null) // Always the case.\n      effects.consume(code)\n      buffer += String.fromCharCode(code)\n      return continuationRawEndTag\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In cdata continuation, after `]`, expecting `]>`.\n   *\n   * ```markdown\n   * > | <![CDATA[>&<]]>\n   *                  ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationCdataInside(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In declaration or instruction continuation, at `>`.\n   *\n   * ```markdown\n   * > | <!-->\n   *         ^\n   * > | <?>\n   *       ^\n   * > | <!q>\n   *        ^\n   * > | <!--ab-->\n   *             ^\n   * > | <![CDATA[>&<]]>\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationDeclarationInside(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    // More dashes.\n    if (code === codes.dash && marker === constants.htmlComment) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In closed continuation: everything we get until the eol/eof is part of it.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationClose(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.htmlFlowData)\n      return continuationAfter(code)\n    }\n\n    effects.consume(code)\n    return continuationClose\n  }\n\n  /**\n   * Done.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationAfter(code) {\n    effects.exit(types.htmlFlow)\n    // // Feel free to interrupt.\n    // tokenizer.interrupt = false\n    // // No longer concrete.\n    // tokenizer.concrete = false\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuationStart(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * At eol, before continuation.\n   *\n   * ```markdown\n   * > | * ```js\n   *            ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * A continuation.\n   *\n   * ```markdown\n   *   | * ```js\n   * > | b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLineBefore(effects, ok, nok) {\n  return start\n\n  /**\n   * Before eol, expecting blank line.\n   *\n   * ```markdown\n   * > | <div>\n   *          ^\n   *   |\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(markdownLineEnding(code), 'expected a line ending')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return effects.attempt(blankLine, ok, nok)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SACEC,iBAAiB,EACjBC,UAAU,EACVC,yBAAyB,EACzBC,kBAAkB,EAClBC,aAAa,QACR,0BAA0B;AACjC,SAAQC,cAAc,EAAEC,YAAY,QAAO,8BAA8B;AACzE,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;AAC7D,SAAQC,SAAS,QAAO,iBAAiB;;AAEzC;AACA,OAAO,MAAMC,QAAQ,GAAG;EACtBC,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEC,iBAAiB;EAC5BC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA,MAAMC,eAAe,GAAG;EAACC,OAAO,EAAE,IAAI;EAAEH,QAAQ,EAAEI;AAAuB,CAAC;AAC1E,MAAMC,wBAAwB,GAAG;EAC/BF,OAAO,EAAE,IAAI;EACbH,QAAQ,EAAEM;AACZ,CAAC;;AAED;AACA,SAASP,iBAAiBA,CAACQ,MAAM,EAAE;EACjC,IAAIC,KAAK,GAAGD,MAAM,CAACE,MAAM;EAEzB,OAAOD,KAAK,EAAE,EAAE;IACd,IACED,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,IAC5BD,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACE,IAAI,KAAKjB,KAAK,CAACE,QAAQ,EACxC;MACA;IACF;EACF;EAEA,IAAIa,KAAK,GAAG,CAAC,IAAID,MAAM,CAACC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACE,IAAI,KAAKjB,KAAK,CAACkB,UAAU,EAAE;IAC/D;IACAJ,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACI,KAAK,GAAGL,MAAM,CAACC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,KAAK;IACnD;IACAL,MAAM,CAACC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,KAAK,GAAGL,MAAM,CAACC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,KAAK;IACvD;IACAL,MAAM,CAACM,MAAM,CAACL,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;EAC7B;EAEA,OAAOD,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASN,gBAAgBA,CAACa,OAAO,EAAEhC,EAAE,EAAEiC,GAAG,EAAE;EAC1C,MAAMC,IAAI,GAAG,IAAI;EACjB;EACA,IAAIC,MAAM;EACV;EACA,IAAIC,UAAU;EACd;EACA,IAAIC,MAAM;EACV;EACA,IAAIX,KAAK;EACT;EACA,IAAIY,OAAO;EAEX,OAAOR,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACS,IAAI,EAAE;IACnB;IACA,OAAOC,MAAM,CAACD,IAAI,CAAC;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,MAAMA,CAACD,IAAI,EAAE;IACpBtC,MAAM,CAACsC,IAAI,KAAK9B,KAAK,CAACgC,QAAQ,EAAE,cAAc,CAAC;IAC/CT,OAAO,CAACU,KAAK,CAAC/B,KAAK,CAACE,QAAQ,CAAC;IAC7BmB,OAAO,CAACU,KAAK,CAAC/B,KAAK,CAACgC,YAAY,CAAC;IACjCX,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;IACrB,OAAOM,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,IAAIA,CAACN,IAAI,EAAE;IAClB,IAAIA,IAAI,KAAK9B,KAAK,CAACqC,eAAe,EAAE;MAClCd,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAOQ,eAAe;IACxB;IAEA,IAAIR,IAAI,KAAK9B,KAAK,CAACuC,KAAK,EAAE;MACxBhB,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrBH,UAAU,GAAG,IAAI;MACjB,OAAOa,aAAa;IACtB;IAEA,IAAIV,IAAI,KAAK9B,KAAK,CAACyC,YAAY,EAAE;MAC/BlB,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrBJ,MAAM,GAAGzB,SAAS,CAACyC,eAAe;MAClC;MACA;MACA;MACA;MACA;MACA,OAAOjB,IAAI,CAACkB,SAAS,GAAGpD,EAAE,GAAGqD,6BAA6B;IAC5D;;IAEA;IACA,IAAIlD,UAAU,CAACoC,IAAI,CAAC,EAAE;MACpBtC,MAAM,CAACsC,IAAI,KAAK,IAAI,CAAC,EAAC;MACtBP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrBF,MAAM,GAAGiB,MAAM,CAACC,YAAY,CAAChB,IAAI,CAAC;MAClC,OAAOiB,OAAO;IAChB;IAEA,OAAOvB,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASQ,eAAeA,CAACR,IAAI,EAAE;IAC7B,IAAIA,IAAI,KAAK9B,KAAK,CAACgD,IAAI,EAAE;MACvBzB,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrBJ,MAAM,GAAGzB,SAAS,CAACgD,WAAW;MAC9B,OAAOC,iBAAiB;IAC1B;IAEA,IAAIpB,IAAI,KAAK9B,KAAK,CAACmD,iBAAiB,EAAE;MACpC5B,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrBJ,MAAM,GAAGzB,SAAS,CAACmD,SAAS;MAC5BnC,KAAK,GAAG,CAAC;MACT,OAAOoC,eAAe;IACxB;;IAEA;IACA,IAAI3D,UAAU,CAACoC,IAAI,CAAC,EAAE;MACpBP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrBJ,MAAM,GAAGzB,SAAS,CAACqD,eAAe;MAClC;MACA;MACA,OAAO7B,IAAI,CAACkB,SAAS,GAAGpD,EAAE,GAAGqD,6BAA6B;IAC5D;IAEA,OAAOpB,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASoB,iBAAiBA,CAACpB,IAAI,EAAE;IAC/B,IAAIA,IAAI,KAAK9B,KAAK,CAACgD,IAAI,EAAE;MACvBzB,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB;MACA;MACA,OAAOL,IAAI,CAACkB,SAAS,GAAGpD,EAAE,GAAGqD,6BAA6B;IAC5D;IAEA,OAAOpB,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASuB,eAAeA,CAACvB,IAAI,EAAE;IAC7B,MAAMyB,KAAK,GAAGtD,SAAS,CAACuD,kBAAkB;IAE1C,IAAI1B,IAAI,KAAKyB,KAAK,CAACE,UAAU,CAACxC,KAAK,EAAE,CAAC,EAAE;MACtCM,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MAErB,IAAIb,KAAK,KAAKsC,KAAK,CAACrC,MAAM,EAAE;QAC1B;QACA;QACA,OAAOO,IAAI,CAACkB,SAAS,GAAGpD,EAAE,GAAGmE,YAAY;MAC3C;MAEA,OAAOL,eAAe;IACxB;IAEA,OAAO7B,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASU,aAAaA,CAACV,IAAI,EAAE;IAC3B,IAAIpC,UAAU,CAACoC,IAAI,CAAC,EAAE;MACpBtC,MAAM,CAACsC,IAAI,KAAK,IAAI,CAAC,EAAC;MACtBP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrBF,MAAM,GAAGiB,MAAM,CAACC,YAAY,CAAChB,IAAI,CAAC;MAClC,OAAOiB,OAAO;IAChB;IAEA,OAAOvB,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiB,OAAOA,CAACjB,IAAI,EAAE;IACrB,IACEA,IAAI,KAAK9B,KAAK,CAAC2D,GAAG,IAClB7B,IAAI,KAAK9B,KAAK,CAACuC,KAAK,IACpBT,IAAI,KAAK9B,KAAK,CAAC4D,WAAW,IAC1BjE,yBAAyB,CAACmC,IAAI,CAAC,EAC/B;MACA,MAAMS,KAAK,GAAGT,IAAI,KAAK9B,KAAK,CAACuC,KAAK;MAClC,MAAMjC,IAAI,GAAGsB,MAAM,CAACiC,WAAW,CAAC,CAAC;MAEjC,IAAI,CAACtB,KAAK,IAAI,CAACZ,UAAU,IAAI5B,YAAY,CAAC+D,QAAQ,CAACxD,IAAI,CAAC,EAAE;QACxDoB,MAAM,GAAGzB,SAAS,CAAC8D,OAAO;QAC1B;QACA;QACA,OAAOtC,IAAI,CAACkB,SAAS,GAAGpD,EAAE,CAACuC,IAAI,CAAC,GAAG4B,YAAY,CAAC5B,IAAI,CAAC;MACvD;MAEA,IAAIhC,cAAc,CAACgE,QAAQ,CAAClC,MAAM,CAACiC,WAAW,CAAC,CAAC,CAAC,EAAE;QACjDnC,MAAM,GAAGzB,SAAS,CAAC+D,SAAS;QAE5B,IAAIzB,KAAK,EAAE;UACThB,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;UACrB,OAAOmC,gBAAgB;QACzB;;QAEA;QACA;QACA,OAAOxC,IAAI,CAACkB,SAAS,GAAGpD,EAAE,CAACuC,IAAI,CAAC,GAAG4B,YAAY,CAAC5B,IAAI,CAAC;MACvD;MAEAJ,MAAM,GAAGzB,SAAS,CAACiE,YAAY;MAC/B;MACA,OAAOzC,IAAI,CAACkB,SAAS,IAAI,CAAClB,IAAI,CAAC0C,MAAM,CAACC,IAAI,CAAC3C,IAAI,CAAC4C,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,GACvD9C,GAAG,CAACM,IAAI,CAAC,GACTH,UAAU,GACR4C,uBAAuB,CAACzC,IAAI,CAAC,GAC7B0C,2BAA2B,CAAC1C,IAAI,CAAC;IACzC;;IAEA;IACA,IAAIA,IAAI,KAAK9B,KAAK,CAACgD,IAAI,IAAIvD,iBAAiB,CAACqC,IAAI,CAAC,EAAE;MAClDP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrBF,MAAM,IAAIiB,MAAM,CAACC,YAAY,CAAChB,IAAI,CAAC;MACnC,OAAOiB,OAAO;IAChB;IAEA,OAAOvB,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASmC,gBAAgBA,CAACnC,IAAI,EAAE;IAC9B,IAAIA,IAAI,KAAK9B,KAAK,CAAC4D,WAAW,EAAE;MAC9BrC,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB;MACA;MACA,OAAOL,IAAI,CAACkB,SAAS,GAAGpD,EAAE,GAAGmE,YAAY;IAC3C;IAEA,OAAOlC,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASyC,uBAAuBA,CAACzC,IAAI,EAAE;IACrC,IAAIjC,aAAa,CAACiC,IAAI,CAAC,EAAE;MACvBP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAOyC,uBAAuB;IAChC;IAEA,OAAOE,WAAW,CAAC3C,IAAI,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS0C,2BAA2BA,CAAC1C,IAAI,EAAE;IACzC,IAAIA,IAAI,KAAK9B,KAAK,CAACuC,KAAK,EAAE;MACxBhB,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAO2C,WAAW;IACpB;;IAEA;IACA,IAAI3C,IAAI,KAAK9B,KAAK,CAAC0E,KAAK,IAAI5C,IAAI,KAAK9B,KAAK,CAAC2E,UAAU,IAAIjF,UAAU,CAACoC,IAAI,CAAC,EAAE;MACzEP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAO8C,qBAAqB;IAC9B;IAEA,IAAI/E,aAAa,CAACiC,IAAI,CAAC,EAAE;MACvBP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAO0C,2BAA2B;IACpC;IAEA,OAAOC,WAAW,CAAC3C,IAAI,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS8C,qBAAqBA,CAAC9C,IAAI,EAAE;IACnC;IACA,IACEA,IAAI,KAAK9B,KAAK,CAACgD,IAAI,IACnBlB,IAAI,KAAK9B,KAAK,CAAC6E,GAAG,IAClB/C,IAAI,KAAK9B,KAAK,CAAC0E,KAAK,IACpB5C,IAAI,KAAK9B,KAAK,CAAC2E,UAAU,IACzBlF,iBAAiB,CAACqC,IAAI,CAAC,EACvB;MACAP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAO8C,qBAAqB;IAC9B;IAEA,OAAOE,0BAA0B,CAAChD,IAAI,CAAC;EACzC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASgD,0BAA0BA,CAAChD,IAAI,EAAE;IACxC,IAAIA,IAAI,KAAK9B,KAAK,CAAC+E,QAAQ,EAAE;MAC3BxD,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAOkD,4BAA4B;IACrC;IAEA,IAAInF,aAAa,CAACiC,IAAI,CAAC,EAAE;MACvBP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAOgD,0BAA0B;IACnC;IAEA,OAAON,2BAA2B,CAAC1C,IAAI,CAAC;EAC1C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASkD,4BAA4BA,CAAClD,IAAI,EAAE;IAC1C,IACEA,IAAI,KAAK9B,KAAK,CAAC2D,GAAG,IAClB7B,IAAI,KAAK9B,KAAK,CAACgC,QAAQ,IACvBF,IAAI,KAAK9B,KAAK,CAAC+E,QAAQ,IACvBjD,IAAI,KAAK9B,KAAK,CAAC4D,WAAW,IAC1B9B,IAAI,KAAK9B,KAAK,CAACiF,WAAW,EAC1B;MACA,OAAOzD,GAAG,CAACM,IAAI,CAAC;IAClB;IAEA,IAAIA,IAAI,KAAK9B,KAAK,CAACkF,aAAa,IAAIpD,IAAI,KAAK9B,KAAK,CAACmF,UAAU,EAAE;MAC7D5D,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrBD,OAAO,GAAGC,IAAI;MACd,OAAOsD,4BAA4B;IACrC;IAEA,IAAIvF,aAAa,CAACiC,IAAI,CAAC,EAAE;MACvBP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAOkD,4BAA4B;IACrC;IAEA,OAAOK,8BAA8B,CAACvD,IAAI,CAAC;EAC7C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsD,4BAA4BA,CAACtD,IAAI,EAAE;IAC1C,IAAIA,IAAI,KAAKD,OAAO,EAAE;MACpBN,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrBD,OAAO,GAAG,IAAI;MACd,OAAOyD,iCAAiC;IAC1C;IAEA,IAAIxD,IAAI,KAAK9B,KAAK,CAAC2D,GAAG,IAAI/D,kBAAkB,CAACkC,IAAI,CAAC,EAAE;MAClD,OAAON,GAAG,CAACM,IAAI,CAAC;IAClB;IAEAP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;IACrB,OAAOsD,4BAA4B;EACrC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,8BAA8BA,CAACvD,IAAI,EAAE;IAC5C,IACEA,IAAI,KAAK9B,KAAK,CAAC2D,GAAG,IAClB7B,IAAI,KAAK9B,KAAK,CAACkF,aAAa,IAC5BpD,IAAI,KAAK9B,KAAK,CAACmF,UAAU,IACzBrD,IAAI,KAAK9B,KAAK,CAACuC,KAAK,IACpBT,IAAI,KAAK9B,KAAK,CAACgC,QAAQ,IACvBF,IAAI,KAAK9B,KAAK,CAAC+E,QAAQ,IACvBjD,IAAI,KAAK9B,KAAK,CAAC4D,WAAW,IAC1B9B,IAAI,KAAK9B,KAAK,CAACiF,WAAW,IAC1BtF,yBAAyB,CAACmC,IAAI,CAAC,EAC/B;MACA,OAAOgD,0BAA0B,CAAChD,IAAI,CAAC;IACzC;IAEAP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;IACrB,OAAOuD,8BAA8B;EACvC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,iCAAiCA,CAACxD,IAAI,EAAE;IAC/C,IACEA,IAAI,KAAK9B,KAAK,CAACuC,KAAK,IACpBT,IAAI,KAAK9B,KAAK,CAAC4D,WAAW,IAC1B/D,aAAa,CAACiC,IAAI,CAAC,EACnB;MACA,OAAO0C,2BAA2B,CAAC1C,IAAI,CAAC;IAC1C;IAEA,OAAON,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS2C,WAAWA,CAAC3C,IAAI,EAAE;IACzB,IAAIA,IAAI,KAAK9B,KAAK,CAAC4D,WAAW,EAAE;MAC9BrC,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAOyD,aAAa;IACtB;IAEA,OAAO/D,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASyD,aAAaA,CAACzD,IAAI,EAAE;IAC3B,IAAIA,IAAI,KAAK9B,KAAK,CAAC2D,GAAG,IAAI/D,kBAAkB,CAACkC,IAAI,CAAC,EAAE;MAClD;MACA;MACA,OAAO4B,YAAY,CAAC5B,IAAI,CAAC;IAC3B;IAEA,IAAIjC,aAAa,CAACiC,IAAI,CAAC,EAAE;MACvBP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAOyD,aAAa;IACtB;IAEA,OAAO/D,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS4B,YAAYA,CAAC5B,IAAI,EAAE;IAC1B,IAAIA,IAAI,KAAK9B,KAAK,CAACgD,IAAI,IAAItB,MAAM,KAAKzB,SAAS,CAACgD,WAAW,EAAE;MAC3D1B,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAO0D,yBAAyB;IAClC;IAEA,IAAI1D,IAAI,KAAK9B,KAAK,CAACgC,QAAQ,IAAIN,MAAM,KAAKzB,SAAS,CAAC8D,OAAO,EAAE;MAC3DxC,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAO2D,sBAAsB;IAC/B;IAEA,IAAI3D,IAAI,KAAK9B,KAAK,CAAC4D,WAAW,IAAIlC,MAAM,KAAKzB,SAAS,CAACqD,eAAe,EAAE;MACtE/B,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAO4D,iBAAiB;IAC1B;IAEA,IAAI5D,IAAI,KAAK9B,KAAK,CAACyC,YAAY,IAAIf,MAAM,KAAKzB,SAAS,CAACyC,eAAe,EAAE;MACvEnB,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAOc,6BAA6B;IACtC;IAEA,IAAId,IAAI,KAAK9B,KAAK,CAAC2F,kBAAkB,IAAIjE,MAAM,KAAKzB,SAAS,CAACmD,SAAS,EAAE;MACvE7B,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAO8D,uBAAuB;IAChC;IAEA,IACEhG,kBAAkB,CAACkC,IAAI,CAAC,KACvBJ,MAAM,KAAKzB,SAAS,CAAC+D,SAAS,IAAItC,MAAM,KAAKzB,SAAS,CAACiE,YAAY,CAAC,EACrE;MACA3C,OAAO,CAACsE,IAAI,CAAC3F,KAAK,CAACgC,YAAY,CAAC;MAChC,OAAOX,OAAO,CAACuE,KAAK,CAClBnF,eAAe,EACfoF,iBAAiB,EACjBC,iBACF,CAAC,CAAClE,IAAI,CAAC;IACT;IAEA,IAAIA,IAAI,KAAK9B,KAAK,CAAC2D,GAAG,IAAI/D,kBAAkB,CAACkC,IAAI,CAAC,EAAE;MAClDP,OAAO,CAACsE,IAAI,CAAC3F,KAAK,CAACgC,YAAY,CAAC;MAChC,OAAO8D,iBAAiB,CAAClE,IAAI,CAAC;IAChC;IAEAP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;IACrB,OAAO4B,YAAY;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsC,iBAAiBA,CAAClE,IAAI,EAAE;IAC/B,OAAOP,OAAO,CAACuE,KAAK,CAClBhF,wBAAwB,EACxBmF,wBAAwB,EACxBF,iBACF,CAAC,CAACjE,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASmE,wBAAwBA,CAACnE,IAAI,EAAE;IACtCtC,MAAM,CAACI,kBAAkB,CAACkC,IAAI,CAAC,CAAC;IAChCP,OAAO,CAACU,KAAK,CAAC/B,KAAK,CAACgG,UAAU,CAAC;IAC/B3E,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;IACrBP,OAAO,CAACsE,IAAI,CAAC3F,KAAK,CAACgG,UAAU,CAAC;IAC9B,OAAOC,kBAAkB;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,kBAAkBA,CAACrE,IAAI,EAAE;IAChC,IAAIA,IAAI,KAAK9B,KAAK,CAAC2D,GAAG,IAAI/D,kBAAkB,CAACkC,IAAI,CAAC,EAAE;MAClD,OAAOkE,iBAAiB,CAAClE,IAAI,CAAC;IAChC;IAEAP,OAAO,CAACU,KAAK,CAAC/B,KAAK,CAACgC,YAAY,CAAC;IACjC,OAAOwB,YAAY,CAAC5B,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS0D,yBAAyBA,CAAC1D,IAAI,EAAE;IACvC,IAAIA,IAAI,KAAK9B,KAAK,CAACgD,IAAI,EAAE;MACvBzB,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAOc,6BAA6B;IACtC;IAEA,OAAOc,YAAY,CAAC5B,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS2D,sBAAsBA,CAAC3D,IAAI,EAAE;IACpC,IAAIA,IAAI,KAAK9B,KAAK,CAACuC,KAAK,EAAE;MACxBhB,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrBF,MAAM,GAAG,EAAE;MACX,OAAOwE,qBAAqB;IAC9B;IAEA,OAAO1C,YAAY,CAAC5B,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsE,qBAAqBA,CAACtE,IAAI,EAAE;IACnC,IAAIA,IAAI,KAAK9B,KAAK,CAAC4D,WAAW,EAAE;MAC9B,MAAMtD,IAAI,GAAGsB,MAAM,CAACiC,WAAW,CAAC,CAAC;MAEjC,IAAI9D,YAAY,CAAC+D,QAAQ,CAACxD,IAAI,CAAC,EAAE;QAC/BiB,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;QACrB,OAAO4D,iBAAiB;MAC1B;MAEA,OAAOhC,YAAY,CAAC5B,IAAI,CAAC;IAC3B;IAEA,IAAIpC,UAAU,CAACoC,IAAI,CAAC,IAAIF,MAAM,CAACV,MAAM,GAAGjB,SAAS,CAACoG,cAAc,EAAE;MAChE7G,MAAM,CAACsC,IAAI,KAAK,IAAI,CAAC,EAAC;MACtBP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrBF,MAAM,IAAIiB,MAAM,CAACC,YAAY,CAAChB,IAAI,CAAC;MACnC,OAAOsE,qBAAqB;IAC9B;IAEA,OAAO1C,YAAY,CAAC5B,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS8D,uBAAuBA,CAAC9D,IAAI,EAAE;IACrC,IAAIA,IAAI,KAAK9B,KAAK,CAAC2F,kBAAkB,EAAE;MACrCpE,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAOc,6BAA6B;IACtC;IAEA,OAAOc,YAAY,CAAC5B,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASc,6BAA6BA,CAACd,IAAI,EAAE;IAC3C,IAAIA,IAAI,KAAK9B,KAAK,CAAC4D,WAAW,EAAE;MAC9BrC,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAO4D,iBAAiB;IAC1B;;IAEA;IACA,IAAI5D,IAAI,KAAK9B,KAAK,CAACgD,IAAI,IAAItB,MAAM,KAAKzB,SAAS,CAACgD,WAAW,EAAE;MAC3D1B,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrB,OAAOc,6BAA6B;IACtC;IAEA,OAAOc,YAAY,CAAC5B,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS4D,iBAAiBA,CAAC5D,IAAI,EAAE;IAC/B,IAAIA,IAAI,KAAK9B,KAAK,CAAC2D,GAAG,IAAI/D,kBAAkB,CAACkC,IAAI,CAAC,EAAE;MAClDP,OAAO,CAACsE,IAAI,CAAC3F,KAAK,CAACgC,YAAY,CAAC;MAChC,OAAO6D,iBAAiB,CAACjE,IAAI,CAAC;IAChC;IAEAP,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;IACrB,OAAO4D,iBAAiB;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASK,iBAAiBA,CAACjE,IAAI,EAAE;IAC/BP,OAAO,CAACsE,IAAI,CAAC3F,KAAK,CAACE,QAAQ,CAAC;IAC5B;IACA;IACA;IACA;IACA,OAAOb,EAAE,CAACuC,IAAI,CAAC;EACjB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASf,gCAAgCA,CAACQ,OAAO,EAAEhC,EAAE,EAAEiC,GAAG,EAAE;EAC1D,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOJ,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACS,IAAI,EAAE;IACnB,IAAIlC,kBAAkB,CAACkC,IAAI,CAAC,EAAE;MAC5BP,OAAO,CAACU,KAAK,CAAC/B,KAAK,CAACgG,UAAU,CAAC;MAC/B3E,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;MACrBP,OAAO,CAACsE,IAAI,CAAC3F,KAAK,CAACgG,UAAU,CAAC;MAC9B,OAAOI,KAAK;IACd;IAEA,OAAO9E,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASwE,KAAKA,CAACxE,IAAI,EAAE;IACnB,OAAOL,IAAI,CAAC0C,MAAM,CAACC,IAAI,CAAC3C,IAAI,CAAC4C,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG9C,GAAG,CAACM,IAAI,CAAC,GAAGvC,EAAE,CAACuC,IAAI,CAAC;EACjE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASjB,uBAAuBA,CAACU,OAAO,EAAEhC,EAAE,EAAEiC,GAAG,EAAE;EACjD,OAAOH,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACS,IAAI,EAAE;IACnBtC,MAAM,CAACI,kBAAkB,CAACkC,IAAI,CAAC,EAAE,wBAAwB,CAAC;IAC1DP,OAAO,CAACU,KAAK,CAAC/B,KAAK,CAACgG,UAAU,CAAC;IAC/B3E,OAAO,CAACY,OAAO,CAACL,IAAI,CAAC;IACrBP,OAAO,CAACsE,IAAI,CAAC3F,KAAK,CAACgG,UAAU,CAAC;IAC9B,OAAO3E,OAAO,CAACgF,OAAO,CAACpG,SAAS,EAAEZ,EAAE,EAAEiC,GAAG,CAAC;EAC5C;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}