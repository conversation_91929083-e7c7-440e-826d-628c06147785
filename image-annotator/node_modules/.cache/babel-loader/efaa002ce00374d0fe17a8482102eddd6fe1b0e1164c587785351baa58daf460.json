{"ast": null, "code": "import REGEX from './regex.js';\nfunction validate(uuid) {\n  return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;", "map": {"version": 3, "names": ["REGEX", "validate", "uuid", "test"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/uuid/dist/esm-browser/validate.js"], "sourcesContent": ["import REGEX from './regex.js';\nfunction validate(uuid) {\n    return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;AAC9B,SAASC,QAAQA,CAACC,IAAI,EAAE;EACpB,OAAO,OAAOA,IAAI,KAAK,QAAQ,IAAIF,KAAK,CAACG,IAAI,CAACD,IAAI,CAAC;AACvD;AACA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}