{"ast": null, "code": "/**\n * @import {Event} from 'micromark-util-types'\n */\n\nimport { subtokenize } from 'micromark-util-subtokenize';\n\n/**\n * @param {Array<Event>} events\n *   Events.\n * @returns {Array<Event>}\n *   Events.\n */\nexport function postprocess(events) {\n  while (!subtokenize(events)) {\n    // Empty\n  }\n  return events;\n}", "map": {"version": 3, "names": ["subtokenize", "postprocess", "events"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark/dev/lib/postprocess.js"], "sourcesContent": ["/**\n * @import {Event} from 'micromark-util-types'\n */\n\nimport {subtokenize} from 'micromark-util-subtokenize'\n\n/**\n * @param {Array<Event>} events\n *   Events.\n * @returns {Array<Event>}\n *   Events.\n */\nexport function postprocess(events) {\n  while (!subtokenize(events)) {\n    // Empty\n  }\n\n  return events\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAAQA,WAAW,QAAO,4BAA4B;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAE;EAClC,OAAO,CAACF,WAAW,CAACE,MAAM,CAAC,EAAE;IAC3B;EAAA;EAGF,OAAOA,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}