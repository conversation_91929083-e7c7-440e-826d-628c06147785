{"ast": null, "code": "/**\n * @import {Chunk, Event, Token} from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { splice } from 'micromark-util-chunked';\nimport { codes, types } from 'micromark-util-symbol';\nimport { SpliceBuffer } from './lib/splice-buffer.js';\n\n// Hidden API exposed for testing.\nexport { SpliceBuffer } from './lib/splice-buffer.js';\n\n/**\n * Tokenize subcontent.\n *\n * @param {Array<Event>} eventsArray\n *   List of events.\n * @returns {boolean}\n *   Whether subtokens were found.\n */\n// eslint-disable-next-line complexity\nexport function subtokenize(eventsArray) {\n  /** @type {Record<string, number>} */\n  const jumps = {};\n  let index = -1;\n  /** @type {Event} */\n  let event;\n  /** @type {number | undefined} */\n  let lineIndex;\n  /** @type {number} */\n  let otherIndex;\n  /** @type {Event} */\n  let otherEvent;\n  /** @type {Array<Event>} */\n  let parameters;\n  /** @type {Array<Event>} */\n  let subevents;\n  /** @type {boolean | undefined} */\n  let more;\n  const events = new SpliceBuffer(eventsArray);\n  while (++index < events.length) {\n    while (index in jumps) {\n      index = jumps[index];\n    }\n    event = events.get(index);\n\n    // Add a hook for the GFM tasklist extension, which needs to know if text\n    // is in the first content of a list item.\n    if (index && event[1].type === types.chunkFlow && events.get(index - 1)[1].type === types.listItemPrefix) {\n      assert(event[1]._tokenizer, 'expected `_tokenizer` on subtokens');\n      subevents = event[1]._tokenizer.events;\n      otherIndex = 0;\n      if (otherIndex < subevents.length && subevents[otherIndex][1].type === types.lineEndingBlank) {\n        otherIndex += 2;\n      }\n      if (otherIndex < subevents.length && subevents[otherIndex][1].type === types.content) {\n        while (++otherIndex < subevents.length) {\n          if (subevents[otherIndex][1].type === types.content) {\n            break;\n          }\n          if (subevents[otherIndex][1].type === types.chunkText) {\n            subevents[otherIndex][1]._isInFirstContentOfListItem = true;\n            otherIndex++;\n          }\n        }\n      }\n    }\n\n    // Enter.\n    if (event[0] === 'enter') {\n      if (event[1].contentType) {\n        Object.assign(jumps, subcontent(events, index));\n        index = jumps[index];\n        more = true;\n      }\n    }\n    // Exit.\n    else if (event[1]._container) {\n      otherIndex = index;\n      lineIndex = undefined;\n      while (otherIndex--) {\n        otherEvent = events.get(otherIndex);\n        if (otherEvent[1].type === types.lineEnding || otherEvent[1].type === types.lineEndingBlank) {\n          if (otherEvent[0] === 'enter') {\n            if (lineIndex) {\n              events.get(lineIndex)[1].type = types.lineEndingBlank;\n            }\n            otherEvent[1].type = types.lineEnding;\n            lineIndex = otherIndex;\n          }\n        } else if (otherEvent[1].type === types.linePrefix || otherEvent[1].type === types.listItemIndent) {\n          // Move past.\n        } else {\n          break;\n        }\n      }\n      if (lineIndex) {\n        // Fix position.\n        event[1].end = {\n          ...events.get(lineIndex)[1].start\n        };\n\n        // Switch container exit w/ line endings.\n        parameters = events.slice(lineIndex, index);\n        parameters.unshift(event);\n        events.splice(lineIndex, index - lineIndex + 1, parameters);\n      }\n    }\n  }\n\n  // The changes to the `events` buffer must be copied back into the eventsArray\n  splice(eventsArray, 0, Number.POSITIVE_INFINITY, events.slice(0));\n  return !more;\n}\n\n/**\n * Tokenize embedded tokens.\n *\n * @param {SpliceBuffer<Event>} events\n *   Events.\n * @param {number} eventIndex\n *   Index.\n * @returns {Record<string, number>}\n *   Gaps.\n */\nfunction subcontent(events, eventIndex) {\n  const token = events.get(eventIndex)[1];\n  const context = events.get(eventIndex)[2];\n  let startPosition = eventIndex - 1;\n  /** @type {Array<number>} */\n  const startPositions = [];\n  assert(token.contentType, 'expected `contentType` on subtokens');\n  let tokenizer = token._tokenizer;\n  if (!tokenizer) {\n    tokenizer = context.parser[token.contentType](token.start);\n    if (token._contentTypeTextTrailing) {\n      tokenizer._contentTypeTextTrailing = true;\n    }\n  }\n  const childEvents = tokenizer.events;\n  /** @type {Array<[number, number]>} */\n  const jumps = [];\n  /** @type {Record<string, number>} */\n  const gaps = {};\n  /** @type {Array<Chunk>} */\n  let stream;\n  /** @type {Token | undefined} */\n  let previous;\n  let index = -1;\n  /** @type {Token | undefined} */\n  let current = token;\n  let adjust = 0;\n  let start = 0;\n  const breaks = [start];\n\n  // Loop forward through the linked tokens to pass them in order to the\n  // subtokenizer.\n  while (current) {\n    // Find the position of the event for this token.\n    while (events.get(++startPosition)[1] !== current) {\n      // Empty.\n    }\n    assert(!previous || current.previous === previous, 'expected previous to match');\n    assert(!previous || previous.next === current, 'expected next to match');\n    startPositions.push(startPosition);\n    if (!current._tokenizer) {\n      stream = context.sliceStream(current);\n      if (!current.next) {\n        stream.push(codes.eof);\n      }\n      if (previous) {\n        tokenizer.defineSkip(current.start);\n      }\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = true;\n      }\n      tokenizer.write(stream);\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = undefined;\n      }\n    }\n\n    // Unravel the next token.\n    previous = current;\n    current = current.next;\n  }\n\n  // Now, loop back through all events (and linked tokens), to figure out which\n  // parts belong where.\n  current = token;\n  while (++index < childEvents.length) {\n    if (\n    // Find a void token that includes a break.\n    childEvents[index][0] === 'exit' && childEvents[index - 1][0] === 'enter' && childEvents[index][1].type === childEvents[index - 1][1].type && childEvents[index][1].start.line !== childEvents[index][1].end.line) {\n      assert(current, 'expected a current token');\n      start = index + 1;\n      breaks.push(start);\n      // Help GC.\n      current._tokenizer = undefined;\n      current.previous = undefined;\n      current = current.next;\n    }\n  }\n\n  // Help GC.\n  tokenizer.events = [];\n\n  // If there’s one more token (which is the cases for lines that end in an\n  // EOF), that’s perfect: the last point we found starts it.\n  // If there isn’t then make sure any remaining content is added to it.\n  if (current) {\n    // Help GC.\n    current._tokenizer = undefined;\n    current.previous = undefined;\n    assert(!current.next, 'expected no next token');\n  } else {\n    breaks.pop();\n  }\n\n  // Now splice the events from the subtokenizer into the current events,\n  // moving back to front so that splice indices aren’t affected.\n  index = breaks.length;\n  while (index--) {\n    const slice = childEvents.slice(breaks[index], breaks[index + 1]);\n    const start = startPositions.pop();\n    assert(start !== undefined, 'expected a start position when splicing');\n    jumps.push([start, start + slice.length - 1]);\n    events.splice(start, 2, slice);\n  }\n  jumps.reverse();\n  index = -1;\n  while (++index < jumps.length) {\n    gaps[adjust + jumps[index][0]] = adjust + jumps[index][1];\n    adjust += jumps[index][1] - jumps[index][0] - 1;\n  }\n  return gaps;\n}", "map": {"version": 3, "names": ["ok", "assert", "splice", "codes", "types", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtokenize", "eventsArray", "jumps", "index", "event", "lineIndex", "otherIndex", "otherEvent", "parameters", "subevents", "more", "events", "length", "get", "type", "chunkFlow", "listItemPrefix", "_tokenizer", "lineEndingBlank", "content", "chunkText", "_isInFirstContentOfListItem", "contentType", "Object", "assign", "subcontent", "_container", "undefined", "lineEnding", "linePrefix", "listItemIndent", "end", "start", "slice", "unshift", "Number", "POSITIVE_INFINITY", "eventIndex", "token", "context", "startPosition", "startPositions", "tokenizer", "parser", "_contentTypeTextTrailing", "childEvents", "gaps", "stream", "previous", "current", "adjust", "breaks", "next", "push", "sliceStream", "eof", "defineSkip", "_gfmTasklistFirstContentOfListItem", "write", "line", "pop", "reverse"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark-util-subtokenize/dev/index.js"], "sourcesContent": ["/**\n * @import {Chunk, Event, Token} from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {splice} from 'micromark-util-chunked'\nimport {codes, types} from 'micromark-util-symbol'\nimport {<PERSON>plice<PERSON>uffer} from './lib/splice-buffer.js'\n\n// Hidden API exposed for testing.\nexport {SpliceBuffer} from './lib/splice-buffer.js'\n\n/**\n * Tokenize subcontent.\n *\n * @param {Array<Event>} eventsArray\n *   List of events.\n * @returns {boolean}\n *   Whether subtokens were found.\n */\n// eslint-disable-next-line complexity\nexport function subtokenize(eventsArray) {\n  /** @type {Record<string, number>} */\n  const jumps = {}\n  let index = -1\n  /** @type {Event} */\n  let event\n  /** @type {number | undefined} */\n  let lineIndex\n  /** @type {number} */\n  let otherIndex\n  /** @type {Event} */\n  let otherEvent\n  /** @type {Array<Event>} */\n  let parameters\n  /** @type {Array<Event>} */\n  let subevents\n  /** @type {boolean | undefined} */\n  let more\n  const events = new SpliceBuffer(eventsArray)\n\n  while (++index < events.length) {\n    while (index in jumps) {\n      index = jumps[index]\n    }\n\n    event = events.get(index)\n\n    // Add a hook for the GFM tasklist extension, which needs to know if text\n    // is in the first content of a list item.\n    if (\n      index &&\n      event[1].type === types.chunkFlow &&\n      events.get(index - 1)[1].type === types.listItemPrefix\n    ) {\n      assert(event[1]._tokenizer, 'expected `_tokenizer` on subtokens')\n      subevents = event[1]._tokenizer.events\n      otherIndex = 0\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === types.lineEndingBlank\n      ) {\n        otherIndex += 2\n      }\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === types.content\n      ) {\n        while (++otherIndex < subevents.length) {\n          if (subevents[otherIndex][1].type === types.content) {\n            break\n          }\n\n          if (subevents[otherIndex][1].type === types.chunkText) {\n            subevents[otherIndex][1]._isInFirstContentOfListItem = true\n            otherIndex++\n          }\n        }\n      }\n    }\n\n    // Enter.\n    if (event[0] === 'enter') {\n      if (event[1].contentType) {\n        Object.assign(jumps, subcontent(events, index))\n        index = jumps[index]\n        more = true\n      }\n    }\n    // Exit.\n    else if (event[1]._container) {\n      otherIndex = index\n      lineIndex = undefined\n\n      while (otherIndex--) {\n        otherEvent = events.get(otherIndex)\n\n        if (\n          otherEvent[1].type === types.lineEnding ||\n          otherEvent[1].type === types.lineEndingBlank\n        ) {\n          if (otherEvent[0] === 'enter') {\n            if (lineIndex) {\n              events.get(lineIndex)[1].type = types.lineEndingBlank\n            }\n\n            otherEvent[1].type = types.lineEnding\n            lineIndex = otherIndex\n          }\n        } else if (\n          otherEvent[1].type === types.linePrefix ||\n          otherEvent[1].type === types.listItemIndent\n        ) {\n          // Move past.\n        } else {\n          break\n        }\n      }\n\n      if (lineIndex) {\n        // Fix position.\n        event[1].end = {...events.get(lineIndex)[1].start}\n\n        // Switch container exit w/ line endings.\n        parameters = events.slice(lineIndex, index)\n        parameters.unshift(event)\n        events.splice(lineIndex, index - lineIndex + 1, parameters)\n      }\n    }\n  }\n\n  // The changes to the `events` buffer must be copied back into the eventsArray\n  splice(eventsArray, 0, Number.POSITIVE_INFINITY, events.slice(0))\n  return !more\n}\n\n/**\n * Tokenize embedded tokens.\n *\n * @param {SpliceBuffer<Event>} events\n *   Events.\n * @param {number} eventIndex\n *   Index.\n * @returns {Record<string, number>}\n *   Gaps.\n */\nfunction subcontent(events, eventIndex) {\n  const token = events.get(eventIndex)[1]\n  const context = events.get(eventIndex)[2]\n  let startPosition = eventIndex - 1\n  /** @type {Array<number>} */\n  const startPositions = []\n  assert(token.contentType, 'expected `contentType` on subtokens')\n\n  let tokenizer = token._tokenizer\n\n  if (!tokenizer) {\n    tokenizer = context.parser[token.contentType](token.start)\n\n    if (token._contentTypeTextTrailing) {\n      tokenizer._contentTypeTextTrailing = true\n    }\n  }\n\n  const childEvents = tokenizer.events\n  /** @type {Array<[number, number]>} */\n  const jumps = []\n  /** @type {Record<string, number>} */\n  const gaps = {}\n  /** @type {Array<Chunk>} */\n  let stream\n  /** @type {Token | undefined} */\n  let previous\n  let index = -1\n  /** @type {Token | undefined} */\n  let current = token\n  let adjust = 0\n  let start = 0\n  const breaks = [start]\n\n  // Loop forward through the linked tokens to pass them in order to the\n  // subtokenizer.\n  while (current) {\n    // Find the position of the event for this token.\n    while (events.get(++startPosition)[1] !== current) {\n      // Empty.\n    }\n\n    assert(\n      !previous || current.previous === previous,\n      'expected previous to match'\n    )\n    assert(!previous || previous.next === current, 'expected next to match')\n\n    startPositions.push(startPosition)\n\n    if (!current._tokenizer) {\n      stream = context.sliceStream(current)\n\n      if (!current.next) {\n        stream.push(codes.eof)\n      }\n\n      if (previous) {\n        tokenizer.defineSkip(current.start)\n      }\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = true\n      }\n\n      tokenizer.write(stream)\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = undefined\n      }\n    }\n\n    // Unravel the next token.\n    previous = current\n    current = current.next\n  }\n\n  // Now, loop back through all events (and linked tokens), to figure out which\n  // parts belong where.\n  current = token\n\n  while (++index < childEvents.length) {\n    if (\n      // Find a void token that includes a break.\n      childEvents[index][0] === 'exit' &&\n      childEvents[index - 1][0] === 'enter' &&\n      childEvents[index][1].type === childEvents[index - 1][1].type &&\n      childEvents[index][1].start.line !== childEvents[index][1].end.line\n    ) {\n      assert(current, 'expected a current token')\n      start = index + 1\n      breaks.push(start)\n      // Help GC.\n      current._tokenizer = undefined\n      current.previous = undefined\n      current = current.next\n    }\n  }\n\n  // Help GC.\n  tokenizer.events = []\n\n  // If there’s one more token (which is the cases for lines that end in an\n  // EOF), that’s perfect: the last point we found starts it.\n  // If there isn’t then make sure any remaining content is added to it.\n  if (current) {\n    // Help GC.\n    current._tokenizer = undefined\n    current.previous = undefined\n    assert(!current.next, 'expected no next token')\n  } else {\n    breaks.pop()\n  }\n\n  // Now splice the events from the subtokenizer into the current events,\n  // moving back to front so that splice indices aren’t affected.\n  index = breaks.length\n\n  while (index--) {\n    const slice = childEvents.slice(breaks[index], breaks[index + 1])\n    const start = startPositions.pop()\n    assert(start !== undefined, 'expected a start position when splicing')\n    jumps.push([start, start + slice.length - 1])\n    events.splice(start, 2, slice)\n  }\n\n  jumps.reverse()\n  index = -1\n\n  while (++index < jumps.length) {\n    gaps[adjust + jumps[index][0]] = adjust + jumps[index][1]\n    adjust += jumps[index][1] - jumps[index][0] - 1\n  }\n\n  return gaps\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,MAAM,QAAO,wBAAwB;AAC7C,SAAQC,KAAK,EAAEC,KAAK,QAAO,uBAAuB;AAClD,SAAQC,YAAY,QAAO,wBAAwB;;AAEnD;AACA,SAAQA,YAAY,QAAO,wBAAwB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,WAAW,EAAE;EACvC;EACA,MAAMC,KAAK,GAAG,CAAC,CAAC;EAChB,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,KAAK;EACT;EACA,IAAIC,SAAS;EACb;EACA,IAAIC,UAAU;EACd;EACA,IAAIC,UAAU;EACd;EACA,IAAIC,UAAU;EACd;EACA,IAAIC,SAAS;EACb;EACA,IAAIC,IAAI;EACR,MAAMC,MAAM,GAAG,IAAIZ,YAAY,CAACE,WAAW,CAAC;EAE5C,OAAO,EAAEE,KAAK,GAAGQ,MAAM,CAACC,MAAM,EAAE;IAC9B,OAAOT,KAAK,IAAID,KAAK,EAAE;MACrBC,KAAK,GAAGD,KAAK,CAACC,KAAK,CAAC;IACtB;IAEAC,KAAK,GAAGO,MAAM,CAACE,GAAG,CAACV,KAAK,CAAC;;IAEzB;IACA;IACA,IACEA,KAAK,IACLC,KAAK,CAAC,CAAC,CAAC,CAACU,IAAI,KAAKhB,KAAK,CAACiB,SAAS,IACjCJ,MAAM,CAACE,GAAG,CAACV,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACW,IAAI,KAAKhB,KAAK,CAACkB,cAAc,EACtD;MACArB,MAAM,CAACS,KAAK,CAAC,CAAC,CAAC,CAACa,UAAU,EAAE,oCAAoC,CAAC;MACjER,SAAS,GAAGL,KAAK,CAAC,CAAC,CAAC,CAACa,UAAU,CAACN,MAAM;MACtCL,UAAU,GAAG,CAAC;MAEd,IACEA,UAAU,GAAGG,SAAS,CAACG,MAAM,IAC7BH,SAAS,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,CAACQ,IAAI,KAAKhB,KAAK,CAACoB,eAAe,EACvD;QACAZ,UAAU,IAAI,CAAC;MACjB;MAEA,IACEA,UAAU,GAAGG,SAAS,CAACG,MAAM,IAC7BH,SAAS,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,CAACQ,IAAI,KAAKhB,KAAK,CAACqB,OAAO,EAC/C;QACA,OAAO,EAAEb,UAAU,GAAGG,SAAS,CAACG,MAAM,EAAE;UACtC,IAAIH,SAAS,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,CAACQ,IAAI,KAAKhB,KAAK,CAACqB,OAAO,EAAE;YACnD;UACF;UAEA,IAAIV,SAAS,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,CAACQ,IAAI,KAAKhB,KAAK,CAACsB,SAAS,EAAE;YACrDX,SAAS,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,CAACe,2BAA2B,GAAG,IAAI;YAC3Df,UAAU,EAAE;UACd;QACF;MACF;IACF;;IAEA;IACA,IAAIF,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;MACxB,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACkB,WAAW,EAAE;QACxBC,MAAM,CAACC,MAAM,CAACtB,KAAK,EAAEuB,UAAU,CAACd,MAAM,EAAER,KAAK,CAAC,CAAC;QAC/CA,KAAK,GAAGD,KAAK,CAACC,KAAK,CAAC;QACpBO,IAAI,GAAG,IAAI;MACb;IACF;IACA;IAAA,KACK,IAAIN,KAAK,CAAC,CAAC,CAAC,CAACsB,UAAU,EAAE;MAC5BpB,UAAU,GAAGH,KAAK;MAClBE,SAAS,GAAGsB,SAAS;MAErB,OAAOrB,UAAU,EAAE,EAAE;QACnBC,UAAU,GAAGI,MAAM,CAACE,GAAG,CAACP,UAAU,CAAC;QAEnC,IACEC,UAAU,CAAC,CAAC,CAAC,CAACO,IAAI,KAAKhB,KAAK,CAAC8B,UAAU,IACvCrB,UAAU,CAAC,CAAC,CAAC,CAACO,IAAI,KAAKhB,KAAK,CAACoB,eAAe,EAC5C;UACA,IAAIX,UAAU,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;YAC7B,IAAIF,SAAS,EAAE;cACbM,MAAM,CAACE,GAAG,CAACR,SAAS,CAAC,CAAC,CAAC,CAAC,CAACS,IAAI,GAAGhB,KAAK,CAACoB,eAAe;YACvD;YAEAX,UAAU,CAAC,CAAC,CAAC,CAACO,IAAI,GAAGhB,KAAK,CAAC8B,UAAU;YACrCvB,SAAS,GAAGC,UAAU;UACxB;QACF,CAAC,MAAM,IACLC,UAAU,CAAC,CAAC,CAAC,CAACO,IAAI,KAAKhB,KAAK,CAAC+B,UAAU,IACvCtB,UAAU,CAAC,CAAC,CAAC,CAACO,IAAI,KAAKhB,KAAK,CAACgC,cAAc,EAC3C;UACA;QAAA,CACD,MAAM;UACL;QACF;MACF;MAEA,IAAIzB,SAAS,EAAE;QACb;QACAD,KAAK,CAAC,CAAC,CAAC,CAAC2B,GAAG,GAAG;UAAC,GAAGpB,MAAM,CAACE,GAAG,CAACR,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC2B;QAAK,CAAC;;QAElD;QACAxB,UAAU,GAAGG,MAAM,CAACsB,KAAK,CAAC5B,SAAS,EAAEF,KAAK,CAAC;QAC3CK,UAAU,CAAC0B,OAAO,CAAC9B,KAAK,CAAC;QACzBO,MAAM,CAACf,MAAM,CAACS,SAAS,EAAEF,KAAK,GAAGE,SAAS,GAAG,CAAC,EAAEG,UAAU,CAAC;MAC7D;IACF;EACF;;EAEA;EACAZ,MAAM,CAACK,WAAW,EAAE,CAAC,EAAEkC,MAAM,CAACC,iBAAiB,EAAEzB,MAAM,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC;EACjE,OAAO,CAACvB,IAAI;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,UAAUA,CAACd,MAAM,EAAE0B,UAAU,EAAE;EACtC,MAAMC,KAAK,GAAG3B,MAAM,CAACE,GAAG,CAACwB,UAAU,CAAC,CAAC,CAAC,CAAC;EACvC,MAAME,OAAO,GAAG5B,MAAM,CAACE,GAAG,CAACwB,UAAU,CAAC,CAAC,CAAC,CAAC;EACzC,IAAIG,aAAa,GAAGH,UAAU,GAAG,CAAC;EAClC;EACA,MAAMI,cAAc,GAAG,EAAE;EACzB9C,MAAM,CAAC2C,KAAK,CAAChB,WAAW,EAAE,qCAAqC,CAAC;EAEhE,IAAIoB,SAAS,GAAGJ,KAAK,CAACrB,UAAU;EAEhC,IAAI,CAACyB,SAAS,EAAE;IACdA,SAAS,GAAGH,OAAO,CAACI,MAAM,CAACL,KAAK,CAAChB,WAAW,CAAC,CAACgB,KAAK,CAACN,KAAK,CAAC;IAE1D,IAAIM,KAAK,CAACM,wBAAwB,EAAE;MAClCF,SAAS,CAACE,wBAAwB,GAAG,IAAI;IAC3C;EACF;EAEA,MAAMC,WAAW,GAAGH,SAAS,CAAC/B,MAAM;EACpC;EACA,MAAMT,KAAK,GAAG,EAAE;EAChB;EACA,MAAM4C,IAAI,GAAG,CAAC,CAAC;EACf;EACA,IAAIC,MAAM;EACV;EACA,IAAIC,QAAQ;EACZ,IAAI7C,KAAK,GAAG,CAAC,CAAC;EACd;EACA,IAAI8C,OAAO,GAAGX,KAAK;EACnB,IAAIY,MAAM,GAAG,CAAC;EACd,IAAIlB,KAAK,GAAG,CAAC;EACb,MAAMmB,MAAM,GAAG,CAACnB,KAAK,CAAC;;EAEtB;EACA;EACA,OAAOiB,OAAO,EAAE;IACd;IACA,OAAOtC,MAAM,CAACE,GAAG,CAAC,EAAE2B,aAAa,CAAC,CAAC,CAAC,CAAC,KAAKS,OAAO,EAAE;MACjD;IAAA;IAGFtD,MAAM,CACJ,CAACqD,QAAQ,IAAIC,OAAO,CAACD,QAAQ,KAAKA,QAAQ,EAC1C,4BACF,CAAC;IACDrD,MAAM,CAAC,CAACqD,QAAQ,IAAIA,QAAQ,CAACI,IAAI,KAAKH,OAAO,EAAE,wBAAwB,CAAC;IAExER,cAAc,CAACY,IAAI,CAACb,aAAa,CAAC;IAElC,IAAI,CAACS,OAAO,CAAChC,UAAU,EAAE;MACvB8B,MAAM,GAAGR,OAAO,CAACe,WAAW,CAACL,OAAO,CAAC;MAErC,IAAI,CAACA,OAAO,CAACG,IAAI,EAAE;QACjBL,MAAM,CAACM,IAAI,CAACxD,KAAK,CAAC0D,GAAG,CAAC;MACxB;MAEA,IAAIP,QAAQ,EAAE;QACZN,SAAS,CAACc,UAAU,CAACP,OAAO,CAACjB,KAAK,CAAC;MACrC;MAEA,IAAIiB,OAAO,CAAC5B,2BAA2B,EAAE;QACvCqB,SAAS,CAACe,kCAAkC,GAAG,IAAI;MACrD;MAEAf,SAAS,CAACgB,KAAK,CAACX,MAAM,CAAC;MAEvB,IAAIE,OAAO,CAAC5B,2BAA2B,EAAE;QACvCqB,SAAS,CAACe,kCAAkC,GAAG9B,SAAS;MAC1D;IACF;;IAEA;IACAqB,QAAQ,GAAGC,OAAO;IAClBA,OAAO,GAAGA,OAAO,CAACG,IAAI;EACxB;;EAEA;EACA;EACAH,OAAO,GAAGX,KAAK;EAEf,OAAO,EAAEnC,KAAK,GAAG0C,WAAW,CAACjC,MAAM,EAAE;IACnC;IACE;IACAiC,WAAW,CAAC1C,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,IAChC0C,WAAW,CAAC1C,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,IACrC0C,WAAW,CAAC1C,KAAK,CAAC,CAAC,CAAC,CAAC,CAACW,IAAI,KAAK+B,WAAW,CAAC1C,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACW,IAAI,IAC7D+B,WAAW,CAAC1C,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC6B,KAAK,CAAC2B,IAAI,KAAKd,WAAW,CAAC1C,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC4B,GAAG,CAAC4B,IAAI,EACnE;MACAhE,MAAM,CAACsD,OAAO,EAAE,0BAA0B,CAAC;MAC3CjB,KAAK,GAAG7B,KAAK,GAAG,CAAC;MACjBgD,MAAM,CAACE,IAAI,CAACrB,KAAK,CAAC;MAClB;MACAiB,OAAO,CAAChC,UAAU,GAAGU,SAAS;MAC9BsB,OAAO,CAACD,QAAQ,GAAGrB,SAAS;MAC5BsB,OAAO,GAAGA,OAAO,CAACG,IAAI;IACxB;EACF;;EAEA;EACAV,SAAS,CAAC/B,MAAM,GAAG,EAAE;;EAErB;EACA;EACA;EACA,IAAIsC,OAAO,EAAE;IACX;IACAA,OAAO,CAAChC,UAAU,GAAGU,SAAS;IAC9BsB,OAAO,CAACD,QAAQ,GAAGrB,SAAS;IAC5BhC,MAAM,CAAC,CAACsD,OAAO,CAACG,IAAI,EAAE,wBAAwB,CAAC;EACjD,CAAC,MAAM;IACLD,MAAM,CAACS,GAAG,CAAC,CAAC;EACd;;EAEA;EACA;EACAzD,KAAK,GAAGgD,MAAM,CAACvC,MAAM;EAErB,OAAOT,KAAK,EAAE,EAAE;IACd,MAAM8B,KAAK,GAAGY,WAAW,CAACZ,KAAK,CAACkB,MAAM,CAAChD,KAAK,CAAC,EAAEgD,MAAM,CAAChD,KAAK,GAAG,CAAC,CAAC,CAAC;IACjE,MAAM6B,KAAK,GAAGS,cAAc,CAACmB,GAAG,CAAC,CAAC;IAClCjE,MAAM,CAACqC,KAAK,KAAKL,SAAS,EAAE,yCAAyC,CAAC;IACtEzB,KAAK,CAACmD,IAAI,CAAC,CAACrB,KAAK,EAAEA,KAAK,GAAGC,KAAK,CAACrB,MAAM,GAAG,CAAC,CAAC,CAAC;IAC7CD,MAAM,CAACf,MAAM,CAACoC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC;EAChC;EAEA/B,KAAK,CAAC2D,OAAO,CAAC,CAAC;EACf1D,KAAK,GAAG,CAAC,CAAC;EAEV,OAAO,EAAEA,KAAK,GAAGD,KAAK,CAACU,MAAM,EAAE;IAC7BkC,IAAI,CAACI,MAAM,GAAGhD,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG+C,MAAM,GAAGhD,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IACzD+C,MAAM,IAAIhD,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGD,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACjD;EAEA,OAAO2C,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}