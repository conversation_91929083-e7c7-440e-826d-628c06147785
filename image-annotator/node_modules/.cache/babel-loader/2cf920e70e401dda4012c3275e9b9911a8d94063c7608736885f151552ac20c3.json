{"ast": null, "code": "/**\n * @typedef {import('./lib/index.js').Options} Options\n */\n\nexport { VFileMessage } from './lib/index.js';", "map": {"version": 3, "names": ["VFileMessage"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/vfile-message/index.js"], "sourcesContent": ["/**\n * @typedef {import('./lib/index.js').Options} Options\n */\n\nexport {VFileMessage} from './lib/index.js'\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}