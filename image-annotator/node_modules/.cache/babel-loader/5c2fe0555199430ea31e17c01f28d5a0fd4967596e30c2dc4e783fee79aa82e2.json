{"ast": null, "code": "// Note: types exported from `index.d.ts`\nexport { CONTINUE, EXIT, SKIP, visit } from './lib/index.js';", "map": {"version": 3, "names": ["CONTINUE", "EXIT", "SKIP", "visit"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/unist-util-visit/index.js"], "sourcesContent": ["// Note: types exported from `index.d.ts`\nexport {CONTINUE, EXIT, SKIP, visit} from './lib/index.js'\n"], "mappings": "AAAA;AACA,SAAQA,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}