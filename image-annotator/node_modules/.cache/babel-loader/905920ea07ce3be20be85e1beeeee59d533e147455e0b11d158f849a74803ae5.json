{"ast": null, "code": "/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n  let warned = false;\n  return () => {\n    if (!warned) {\n      warned = true;\n      console.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n    }\n  };\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = ['#0000CC', '#0000FF', '#0033CC', '#0033FF', '#0066CC', '#0066FF', '#0099CC', '#0099FF', '#00CC00', '#00CC33', '#00CC66', '#00CC99', '#00CCCC', '#00CCFF', '#3300CC', '#3300FF', '#3333CC', '#3333FF', '#3366CC', '#3366FF', '#3399CC', '#3399FF', '#33CC00', '#33CC33', '#33CC66', '#33CC99', '#33CCCC', '#33CCFF', '#6600CC', '#6600FF', '#6633CC', '#6633FF', '#66CC00', '#66CC33', '#9900CC', '#9900FF', '#9933CC', '#9933FF', '#99CC00', '#99CC33', '#CC0000', '#CC0033', '#CC0066', '#CC0099', '#CC00CC', '#CC00FF', '#CC3300', '#CC3333', '#CC3366', '#CC3399', '#CC33CC', '#CC33FF', '#CC6600', '#CC6633', '#CC9900', '#CC9933', '#CCCC00', '#CCCC33', '#FF0000', '#FF0033', '#FF0066', '#FF0099', '#FF00CC', '#FF00FF', '#FF3300', '#FF3333', '#FF3366', '#FF3399', '#FF33CC', '#FF33FF', '#FF6600', '#FF6633', '#FF9900', '#FF9933', '#FFCC00', '#FFCC33'];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n  // NB: In an Electron preload script, document will be defined but not fully\n  // initialized. Since we know we're in Chrome, we'll just detect this case\n  // explicitly\n  if (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n    return true;\n  }\n\n  // Internet Explorer and Edge do not support colors.\n  if (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n    return false;\n  }\n  let m;\n\n  // Is webkit? http://stackoverflow.com/a/16459606/376773\n  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n  // eslint-disable-next-line no-return-assign\n  return typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance ||\n  // Is firebug? http://stackoverflow.com/a/398120/376773\n  typeof window !== 'undefined' && window.console && (window.console.firebug || window.console.exception && window.console.table) ||\n  // Is firefox >= v31?\n  // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n  typeof navigator !== 'undefined' && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31 ||\n  // Double check webkit in userAgent just in case we are in a worker\n  typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/);\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n  args[0] = (this.useColors ? '%c' : '') + this.namespace + (this.useColors ? ' %c' : ' ') + args[0] + (this.useColors ? '%c ' : ' ') + '+' + module.exports.humanize(this.diff);\n  if (!this.useColors) {\n    return;\n  }\n  const c = 'color: ' + this.color;\n  args.splice(1, 0, c, 'color: inherit');\n\n  // The final \"%c\" is somewhat tricky, because there could be other\n  // arguments passed either before or after the %c, so we need to\n  // figure out the correct index to insert the CSS into\n  let index = 0;\n  let lastC = 0;\n  args[0].replace(/%[a-zA-Z%]/g, match => {\n    if (match === '%%') {\n      return;\n    }\n    index++;\n    if (match === '%c') {\n      // We only are interested in the *last* %c\n      // (the user may have provided their own)\n      lastC = index;\n    }\n  });\n  args.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n  try {\n    if (namespaces) {\n      exports.storage.setItem('debug', namespaces);\n    } else {\n      exports.storage.removeItem('debug');\n    }\n  } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n  }\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n  let r;\n  try {\n    r = exports.storage.getItem('debug') || exports.storage.getItem('DEBUG');\n  } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n  }\n\n  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n  if (!r && typeof process !== 'undefined' && 'env' in process) {\n    r = process.env.DEBUG;\n  }\n  return r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n  try {\n    // TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n    // The Browser also has localStorage in the global context.\n    return localStorage;\n  } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n  }\n}\nmodule.exports = require('./common')(exports);\nconst {\n  formatters\n} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n  try {\n    return JSON.stringify(v);\n  } catch (error) {\n    return '[UnexpectedJSONParseError]: ' + error.message;\n  }\n};", "map": {"version": 3, "names": ["exports", "formatArgs", "save", "load", "useColors", "storage", "localstorage", "destroy", "warned", "console", "warn", "colors", "window", "process", "type", "__nwjs", "navigator", "userAgent", "toLowerCase", "match", "m", "document", "documentElement", "style", "WebkitAppearance", "firebug", "exception", "table", "parseInt", "args", "namespace", "module", "humanize", "diff", "c", "color", "splice", "index", "lastC", "replace", "log", "debug", "namespaces", "setItem", "removeItem", "error", "r", "getItem", "env", "DEBUG", "localStorage", "require", "formatters", "j", "v", "JSON", "stringify", "message"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/debug/src/browser.js"], "sourcesContent": ["/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\tlet m;\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\t// eslint-disable-next-line no-return-assign\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug') || exports.storage.getItem('DEBUG') ;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n"], "mappings": "AAAA;;AAEA;AACA;AACA;;AAEAA,OAAO,CAACC,UAAU,GAAGA,UAAU;AAC/BD,OAAO,CAACE,IAAI,GAAGA,IAAI;AACnBF,OAAO,CAACG,IAAI,GAAGA,IAAI;AACnBH,OAAO,CAACI,SAAS,GAAGA,SAAS;AAC7BJ,OAAO,CAACK,OAAO,GAAGC,YAAY,CAAC,CAAC;AAChCN,OAAO,CAACO,OAAO,GAAG,CAAC,MAAM;EACxB,IAAIC,MAAM,GAAG,KAAK;EAElB,OAAO,MAAM;IACZ,IAAI,CAACA,MAAM,EAAE;MACZA,MAAM,GAAG,IAAI;MACbC,OAAO,CAACC,IAAI,CAAC,uIAAuI,CAAC;IACtJ;EACD,CAAC;AACF,CAAC,EAAE,CAAC;;AAEJ;AACA;AACA;;AAEAV,OAAO,CAACW,MAAM,GAAG,CAChB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACT;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASP,SAASA,CAAA,EAAG;EACpB;EACA;EACA;EACA,IAAI,OAAOQ,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,KAAKD,MAAM,CAACC,OAAO,CAACC,IAAI,KAAK,UAAU,IAAIF,MAAM,CAACC,OAAO,CAACE,MAAM,CAAC,EAAE;IACrH,OAAO,IAAI;EACZ;;EAEA;EACA,IAAI,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,IAAID,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,uBAAuB,CAAC,EAAE;IAChI,OAAO,KAAK;EACb;EAEA,IAAIC,CAAC;;EAEL;EACA;EACA;EACA,OAAQ,OAAOC,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAACC,eAAe,IAAID,QAAQ,CAACC,eAAe,CAACC,KAAK,IAAIF,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,gBAAgB;EACvJ;EACC,OAAOZ,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACH,OAAO,KAAKG,MAAM,CAACH,OAAO,CAACgB,OAAO,IAAKb,MAAM,CAACH,OAAO,CAACiB,SAAS,IAAId,MAAM,CAACH,OAAO,CAACkB,KAAM,CAAE;EACnI;EACA;EACC,OAAOX,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,KAAKG,CAAC,GAAGJ,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAIS,QAAQ,CAACR,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,EAAG;EACxJ;EACC,OAAOJ,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,IAAID,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,oBAAoB,CAAE;AAC5H;;AAEA;AACA;AACA;AACA;AACA;;AAEA,SAASlB,UAAUA,CAAC4B,IAAI,EAAE;EACzBA,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAACzB,SAAS,GAAG,IAAI,GAAG,EAAE,IACpC,IAAI,CAAC0B,SAAS,IACb,IAAI,CAAC1B,SAAS,GAAG,KAAK,GAAG,GAAG,CAAC,GAC9ByB,IAAI,CAAC,CAAC,CAAC,IACN,IAAI,CAACzB,SAAS,GAAG,KAAK,GAAG,GAAG,CAAC,GAC9B,GAAG,GAAG2B,MAAM,CAAC/B,OAAO,CAACgC,QAAQ,CAAC,IAAI,CAACC,IAAI,CAAC;EAEzC,IAAI,CAAC,IAAI,CAAC7B,SAAS,EAAE;IACpB;EACD;EAEA,MAAM8B,CAAC,GAAG,SAAS,GAAG,IAAI,CAACC,KAAK;EAChCN,IAAI,CAACO,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEF,CAAC,EAAE,gBAAgB,CAAC;;EAEtC;EACA;EACA;EACA,IAAIG,KAAK,GAAG,CAAC;EACb,IAAIC,KAAK,GAAG,CAAC;EACbT,IAAI,CAAC,CAAC,CAAC,CAACU,OAAO,CAAC,aAAa,EAAEpB,KAAK,IAAI;IACvC,IAAIA,KAAK,KAAK,IAAI,EAAE;MACnB;IACD;IACAkB,KAAK,EAAE;IACP,IAAIlB,KAAK,KAAK,IAAI,EAAE;MACnB;MACA;MACAmB,KAAK,GAAGD,KAAK;IACd;EACD,CAAC,CAAC;EAEFR,IAAI,CAACO,MAAM,CAACE,KAAK,EAAE,CAAC,EAAEJ,CAAC,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,OAAO,CAACwC,GAAG,GAAG/B,OAAO,CAACgC,KAAK,IAAIhC,OAAO,CAAC+B,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA,SAAStC,IAAIA,CAACwC,UAAU,EAAE;EACzB,IAAI;IACH,IAAIA,UAAU,EAAE;MACf1C,OAAO,CAACK,OAAO,CAACsC,OAAO,CAAC,OAAO,EAAED,UAAU,CAAC;IAC7C,CAAC,MAAM;MACN1C,OAAO,CAACK,OAAO,CAACuC,UAAU,CAAC,OAAO,CAAC;IACpC;EACD,CAAC,CAAC,OAAOC,KAAK,EAAE;IACf;IACA;EAAA;AAEF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS1C,IAAIA,CAAA,EAAG;EACf,IAAI2C,CAAC;EACL,IAAI;IACHA,CAAC,GAAG9C,OAAO,CAACK,OAAO,CAAC0C,OAAO,CAAC,OAAO,CAAC,IAAI/C,OAAO,CAACK,OAAO,CAAC0C,OAAO,CAAC,OAAO,CAAC;EACzE,CAAC,CAAC,OAAOF,KAAK,EAAE;IACf;IACA;EAAA;;EAGD;EACA,IAAI,CAACC,CAAC,IAAI,OAAOjC,OAAO,KAAK,WAAW,IAAI,KAAK,IAAIA,OAAO,EAAE;IAC7DiC,CAAC,GAAGjC,OAAO,CAACmC,GAAG,CAACC,KAAK;EACtB;EAEA,OAAOH,CAAC;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASxC,YAAYA,CAAA,EAAG;EACvB,IAAI;IACH;IACA;IACA,OAAO4C,YAAY;EACpB,CAAC,CAAC,OAAOL,KAAK,EAAE;IACf;IACA;EAAA;AAEF;AAEAd,MAAM,CAAC/B,OAAO,GAAGmD,OAAO,CAAC,UAAU,CAAC,CAACnD,OAAO,CAAC;AAE7C,MAAM;EAACoD;AAAU,CAAC,GAAGrB,MAAM,CAAC/B,OAAO;;AAEnC;AACA;AACA;;AAEAoD,UAAU,CAACC,CAAC,GAAG,UAAUC,CAAC,EAAE;EAC3B,IAAI;IACH,OAAOC,IAAI,CAACC,SAAS,CAACF,CAAC,CAAC;EACzB,CAAC,CAAC,OAAOT,KAAK,EAAE;IACf,OAAO,8BAA8B,GAAGA,KAAK,CAACY,OAAO;EACtD;AACD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}