{"ast": null, "code": "/**\n * @import {\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEndingOrSpace, markdownLineEnding, markdownSpace } from 'micromark-util-character';\nimport { splice } from 'micromark-util-chunked';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const headingAtx = {\n  name: 'headingAtx',\n  resolve: resolveHeadingAtx,\n  tokenize: tokenizeHeadingAtx\n};\n\n/** @type {Resolver} */\nfunction resolveHeadingAtx(events, context) {\n  let contentEnd = events.length - 2;\n  let contentStart = 3;\n  /** @type {Token} */\n  let content;\n  /** @type {Token} */\n  let text;\n\n  // Prefix whitespace, part of the opening.\n  if (events[contentStart][1].type === types.whitespace) {\n    contentStart += 2;\n  }\n\n  // Suffix whitespace, part of the closing.\n  if (contentEnd - 2 > contentStart && events[contentEnd][1].type === types.whitespace) {\n    contentEnd -= 2;\n  }\n  if (events[contentEnd][1].type === types.atxHeadingSequence && (contentStart === contentEnd - 1 || contentEnd - 4 > contentStart && events[contentEnd - 2][1].type === types.whitespace)) {\n    contentEnd -= contentStart + 1 === contentEnd ? 2 : 4;\n  }\n  if (contentEnd > contentStart) {\n    content = {\n      type: types.atxHeadingText,\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end\n    };\n    text = {\n      type: types.chunkText,\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end,\n      contentType: constants.contentTypeText\n    };\n    splice(events, contentStart, contentEnd - contentStart + 1, [['enter', content, context], ['enter', text, context], ['exit', text, context], ['exit', content, context]]);\n  }\n  return events;\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHeadingAtx(effects, ok, nok) {\n  let size = 0;\n  return start;\n\n  /**\n   * Start of a heading (atx).\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse indent like `markdown-rs`.\n    effects.enter(types.atxHeading);\n    return before(code);\n  }\n\n  /**\n   * After optional whitespace, at `#`.\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(code === codes.numberSign, 'expected `#`');\n    effects.enter(types.atxHeadingSequence);\n    return sequenceOpen(code);\n  }\n\n  /**\n   * In opening sequence.\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === codes.numberSign && size++ < constants.atxHeadingOpeningFenceSizeMax) {\n      effects.consume(code);\n      return sequenceOpen;\n    }\n\n    // Always at least one `#`.\n    if (code === codes.eof || markdownLineEndingOrSpace(code)) {\n      effects.exit(types.atxHeadingSequence);\n      return atBreak(code);\n    }\n    return nok(code);\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ## aa\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === codes.numberSign) {\n      effects.enter(types.atxHeadingSequence);\n      return sequenceFurther(code);\n    }\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.atxHeading);\n      // To do: interrupt like `markdown-rs`.\n      // // Feel free to interrupt.\n      // tokenizer.interrupt = false\n      return ok(code);\n    }\n    if (markdownSpace(code)) {\n      return factorySpace(effects, atBreak, types.whitespace)(code);\n    }\n\n    // To do: generate `data` tokens, add the `text` token later.\n    // Needs edit map, see: `markdown.rs`.\n    effects.enter(types.atxHeadingText);\n    return data(code);\n  }\n\n  /**\n   * In further sequence (after whitespace).\n   *\n   * Could be normal “visible” hashes in the heading or a final sequence.\n   *\n   * ```markdown\n   * > | ## aa ##\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceFurther(code) {\n    if (code === codes.numberSign) {\n      effects.consume(code);\n      return sequenceFurther;\n    }\n    effects.exit(types.atxHeadingSequence);\n    return atBreak(code);\n  }\n\n  /**\n   * In text.\n   *\n   * ```markdown\n   * > | ## aa\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function data(code) {\n    if (code === codes.eof || code === codes.numberSign || markdownLineEndingOrSpace(code)) {\n      effects.exit(types.atxHeadingText);\n      return atBreak(code);\n    }\n    effects.consume(code);\n    return data;\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "factorySpace", "markdownLineEndingOrSpace", "markdownLineEnding", "markdownSpace", "splice", "codes", "constants", "types", "headingAtx", "name", "resolve", "resolveHeadingAtx", "tokenize", "tokenizeHeadingAtx", "events", "context", "contentEnd", "length", "contentStart", "content", "text", "type", "whitespace", "atxHeadingSequence", "atxHeadingText", "start", "end", "chunkText", "contentType", "contentTypeText", "effects", "nok", "size", "code", "enter", "atxHeading", "before", "numberSign", "sequenceOpen", "atxHeadingOpeningFenceSizeMax", "consume", "eof", "exit", "atBreak", "<PERSON><PERSON><PERSON><PERSON>", "data"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark-core-commonmark/dev/lib/heading-atx.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {splice} from 'micromark-util-chunked'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const headingAtx = {\n  name: 'headingAtx',\n  resolve: resolveHeadingAtx,\n  tokenize: tokenizeHeadingAtx\n}\n\n/** @type {Resolver} */\nfunction resolveHeadingAtx(events, context) {\n  let contentEnd = events.length - 2\n  let contentStart = 3\n  /** @type {Token} */\n  let content\n  /** @type {Token} */\n  let text\n\n  // Prefix whitespace, part of the opening.\n  if (events[contentStart][1].type === types.whitespace) {\n    contentStart += 2\n  }\n\n  // Suffix whitespace, part of the closing.\n  if (\n    contentEnd - 2 > contentStart &&\n    events[contentEnd][1].type === types.whitespace\n  ) {\n    contentEnd -= 2\n  }\n\n  if (\n    events[contentEnd][1].type === types.atxHeadingSequence &&\n    (contentStart === contentEnd - 1 ||\n      (contentEnd - 4 > contentStart &&\n        events[contentEnd - 2][1].type === types.whitespace))\n  ) {\n    contentEnd -= contentStart + 1 === contentEnd ? 2 : 4\n  }\n\n  if (contentEnd > contentStart) {\n    content = {\n      type: types.atxHeadingText,\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end\n    }\n    text = {\n      type: types.chunkText,\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end,\n      contentType: constants.contentTypeText\n    }\n\n    splice(events, contentStart, contentEnd - contentStart + 1, [\n      ['enter', content, context],\n      ['enter', text, context],\n      ['exit', text, context],\n      ['exit', content, context]\n    ])\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHeadingAtx(effects, ok, nok) {\n  let size = 0\n\n  return start\n\n  /**\n   * Start of a heading (atx).\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse indent like `markdown-rs`.\n    effects.enter(types.atxHeading)\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at `#`.\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(code === codes.numberSign, 'expected `#`')\n    effects.enter(types.atxHeadingSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening sequence.\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (\n      code === codes.numberSign &&\n      size++ < constants.atxHeadingOpeningFenceSizeMax\n    ) {\n      effects.consume(code)\n      return sequenceOpen\n    }\n\n    // Always at least one `#`.\n    if (code === codes.eof || markdownLineEndingOrSpace(code)) {\n      effects.exit(types.atxHeadingSequence)\n      return atBreak(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ## aa\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === codes.numberSign) {\n      effects.enter(types.atxHeadingSequence)\n      return sequenceFurther(code)\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.atxHeading)\n      // To do: interrupt like `markdown-rs`.\n      // // Feel free to interrupt.\n      // tokenizer.interrupt = false\n      return ok(code)\n    }\n\n    if (markdownSpace(code)) {\n      return factorySpace(effects, atBreak, types.whitespace)(code)\n    }\n\n    // To do: generate `data` tokens, add the `text` token later.\n    // Needs edit map, see: `markdown.rs`.\n    effects.enter(types.atxHeadingText)\n    return data(code)\n  }\n\n  /**\n   * In further sequence (after whitespace).\n   *\n   * Could be normal “visible” hashes in the heading or a final sequence.\n   *\n   * ```markdown\n   * > | ## aa ##\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceFurther(code) {\n    if (code === codes.numberSign) {\n      effects.consume(code)\n      return sequenceFurther\n    }\n\n    effects.exit(types.atxHeadingSequence)\n    return atBreak(code)\n  }\n\n  /**\n   * In text.\n   *\n   * ```markdown\n   * > | ## aa\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function data(code) {\n    if (\n      code === codes.eof ||\n      code === codes.numberSign ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      effects.exit(types.atxHeadingText)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return data\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SACEC,yBAAyB,EACzBC,kBAAkB,EAClBC,aAAa,QACR,0BAA0B;AACjC,SAAQC,MAAM,QAAO,wBAAwB;AAC7C,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;;AAE7D;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,IAAI,EAAE,YAAY;EAClBC,OAAO,EAAEC,iBAAiB;EAC1BC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA,SAASF,iBAAiBA,CAACG,MAAM,EAAEC,OAAO,EAAE;EAC1C,IAAIC,UAAU,GAAGF,MAAM,CAACG,MAAM,GAAG,CAAC;EAClC,IAAIC,YAAY,GAAG,CAAC;EACpB;EACA,IAAIC,OAAO;EACX;EACA,IAAIC,IAAI;;EAER;EACA,IAAIN,MAAM,CAACI,YAAY,CAAC,CAAC,CAAC,CAAC,CAACG,IAAI,KAAKd,KAAK,CAACe,UAAU,EAAE;IACrDJ,YAAY,IAAI,CAAC;EACnB;;EAEA;EACA,IACEF,UAAU,GAAG,CAAC,GAAGE,YAAY,IAC7BJ,MAAM,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKd,KAAK,CAACe,UAAU,EAC/C;IACAN,UAAU,IAAI,CAAC;EACjB;EAEA,IACEF,MAAM,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKd,KAAK,CAACgB,kBAAkB,KACtDL,YAAY,KAAKF,UAAU,GAAG,CAAC,IAC7BA,UAAU,GAAG,CAAC,GAAGE,YAAY,IAC5BJ,MAAM,CAACE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKd,KAAK,CAACe,UAAW,CAAC,EACzD;IACAN,UAAU,IAAIE,YAAY,GAAG,CAAC,KAAKF,UAAU,GAAG,CAAC,GAAG,CAAC;EACvD;EAEA,IAAIA,UAAU,GAAGE,YAAY,EAAE;IAC7BC,OAAO,GAAG;MACRE,IAAI,EAAEd,KAAK,CAACiB,cAAc;MAC1BC,KAAK,EAAEX,MAAM,CAACI,YAAY,CAAC,CAAC,CAAC,CAAC,CAACO,KAAK;MACpCC,GAAG,EAAEZ,MAAM,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,CAACU;IAC7B,CAAC;IACDN,IAAI,GAAG;MACLC,IAAI,EAAEd,KAAK,CAACoB,SAAS;MACrBF,KAAK,EAAEX,MAAM,CAACI,YAAY,CAAC,CAAC,CAAC,CAAC,CAACO,KAAK;MACpCC,GAAG,EAAEZ,MAAM,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,CAACU,GAAG;MAC9BE,WAAW,EAAEtB,SAAS,CAACuB;IACzB,CAAC;IAEDzB,MAAM,CAACU,MAAM,EAAEI,YAAY,EAAEF,UAAU,GAAGE,YAAY,GAAG,CAAC,EAAE,CAC1D,CAAC,OAAO,EAAEC,OAAO,EAAEJ,OAAO,CAAC,EAC3B,CAAC,OAAO,EAAEK,IAAI,EAAEL,OAAO,CAAC,EACxB,CAAC,MAAM,EAAEK,IAAI,EAAEL,OAAO,CAAC,EACvB,CAAC,MAAM,EAAEI,OAAO,EAAEJ,OAAO,CAAC,CAC3B,CAAC;EACJ;EAEA,OAAOD,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASD,kBAAkBA,CAACiB,OAAO,EAAEhC,EAAE,EAAEiC,GAAG,EAAE;EAC5C,IAAIC,IAAI,GAAG,CAAC;EAEZ,OAAOP,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACQ,IAAI,EAAE;IACnB;IACAH,OAAO,CAACI,KAAK,CAAC3B,KAAK,CAAC4B,UAAU,CAAC;IAC/B,OAAOC,MAAM,CAACH,IAAI,CAAC;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASG,MAAMA,CAACH,IAAI,EAAE;IACpBlC,MAAM,CAACkC,IAAI,KAAK5B,KAAK,CAACgC,UAAU,EAAE,cAAc,CAAC;IACjDP,OAAO,CAACI,KAAK,CAAC3B,KAAK,CAACgB,kBAAkB,CAAC;IACvC,OAAOe,YAAY,CAACL,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASK,YAAYA,CAACL,IAAI,EAAE;IAC1B,IACEA,IAAI,KAAK5B,KAAK,CAACgC,UAAU,IACzBL,IAAI,EAAE,GAAG1B,SAAS,CAACiC,6BAA6B,EAChD;MACAT,OAAO,CAACU,OAAO,CAACP,IAAI,CAAC;MACrB,OAAOK,YAAY;IACrB;;IAEA;IACA,IAAIL,IAAI,KAAK5B,KAAK,CAACoC,GAAG,IAAIxC,yBAAyB,CAACgC,IAAI,CAAC,EAAE;MACzDH,OAAO,CAACY,IAAI,CAACnC,KAAK,CAACgB,kBAAkB,CAAC;MACtC,OAAOoB,OAAO,CAACV,IAAI,CAAC;IACtB;IAEA,OAAOF,GAAG,CAACE,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASU,OAAOA,CAACV,IAAI,EAAE;IACrB,IAAIA,IAAI,KAAK5B,KAAK,CAACgC,UAAU,EAAE;MAC7BP,OAAO,CAACI,KAAK,CAAC3B,KAAK,CAACgB,kBAAkB,CAAC;MACvC,OAAOqB,eAAe,CAACX,IAAI,CAAC;IAC9B;IAEA,IAAIA,IAAI,KAAK5B,KAAK,CAACoC,GAAG,IAAIvC,kBAAkB,CAAC+B,IAAI,CAAC,EAAE;MAClDH,OAAO,CAACY,IAAI,CAACnC,KAAK,CAAC4B,UAAU,CAAC;MAC9B;MACA;MACA;MACA,OAAOrC,EAAE,CAACmC,IAAI,CAAC;IACjB;IAEA,IAAI9B,aAAa,CAAC8B,IAAI,CAAC,EAAE;MACvB,OAAOjC,YAAY,CAAC8B,OAAO,EAAEa,OAAO,EAAEpC,KAAK,CAACe,UAAU,CAAC,CAACW,IAAI,CAAC;IAC/D;;IAEA;IACA;IACAH,OAAO,CAACI,KAAK,CAAC3B,KAAK,CAACiB,cAAc,CAAC;IACnC,OAAOqB,IAAI,CAACZ,IAAI,CAAC;EACnB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASW,eAAeA,CAACX,IAAI,EAAE;IAC7B,IAAIA,IAAI,KAAK5B,KAAK,CAACgC,UAAU,EAAE;MAC7BP,OAAO,CAACU,OAAO,CAACP,IAAI,CAAC;MACrB,OAAOW,eAAe;IACxB;IAEAd,OAAO,CAACY,IAAI,CAACnC,KAAK,CAACgB,kBAAkB,CAAC;IACtC,OAAOoB,OAAO,CAACV,IAAI,CAAC;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASY,IAAIA,CAACZ,IAAI,EAAE;IAClB,IACEA,IAAI,KAAK5B,KAAK,CAACoC,GAAG,IAClBR,IAAI,KAAK5B,KAAK,CAACgC,UAAU,IACzBpC,yBAAyB,CAACgC,IAAI,CAAC,EAC/B;MACAH,OAAO,CAACY,IAAI,CAACnC,KAAK,CAACiB,cAAc,CAAC;MAClC,OAAOmB,OAAO,CAACV,IAAI,CAAC;IACtB;IAEAH,OAAO,CAACU,OAAO,CAACP,IAAI,CAAC;IACrB,OAAOY,IAAI;EACb;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}