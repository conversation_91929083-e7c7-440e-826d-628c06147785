{"ast": null, "code": "let powers = 0;\nexport const boolean = increment();\nexport const booleanish = increment();\nexport const overloadedBoolean = increment();\nexport const number = increment();\nexport const spaceSeparated = increment();\nexport const commaSeparated = increment();\nexport const commaOrSpaceSeparated = increment();\nfunction increment() {\n  return 2 ** ++powers;\n}", "map": {"version": 3, "names": ["powers", "boolean", "increment", "booleanish", "overloadedBoolean", "number", "spaceSeparated", "commaSeparated", "commaOrSpaceSeparated"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/property-information/lib/util/types.js"], "sourcesContent": ["let powers = 0\n\nexport const boolean = increment()\nexport const booleanish = increment()\nexport const overloadedBoolean = increment()\nexport const number = increment()\nexport const spaceSeparated = increment()\nexport const commaSeparated = increment()\nexport const commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return 2 ** ++powers\n}\n"], "mappings": "AAAA,IAAIA,MAAM,GAAG,CAAC;AAEd,OAAO,MAAMC,OAAO,GAAGC,SAAS,CAAC,CAAC;AAClC,OAAO,MAAMC,UAAU,GAAGD,SAAS,CAAC,CAAC;AACrC,OAAO,MAAME,iBAAiB,GAAGF,SAAS,CAAC,CAAC;AAC5C,OAAO,MAAMG,MAAM,GAAGH,SAAS,CAAC,CAAC;AACjC,OAAO,MAAMI,cAAc,GAAGJ,SAAS,CAAC,CAAC;AACzC,OAAO,MAAMK,cAAc,GAAGL,SAAS,CAAC,CAAC;AACzC,OAAO,MAAMM,qBAAqB,GAAGN,SAAS,CAAC,CAAC;AAEhD,SAASA,SAASA,CAAA,EAAG;EACnB,OAAO,CAAC,IAAI,EAAEF,MAAM;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}