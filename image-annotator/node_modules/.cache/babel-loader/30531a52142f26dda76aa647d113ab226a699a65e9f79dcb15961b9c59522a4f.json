{"ast": null, "code": "import { blockquote } from './blockquote.js';\nimport { hardBreak } from './break.js';\nimport { code } from './code.js';\nimport { strikethrough } from './delete.js';\nimport { emphasis } from './emphasis.js';\nimport { footnoteReference } from './footnote-reference.js';\nimport { heading } from './heading.js';\nimport { html } from './html.js';\nimport { imageReference } from './image-reference.js';\nimport { image } from './image.js';\nimport { inlineCode } from './inline-code.js';\nimport { linkReference } from './link-reference.js';\nimport { link } from './link.js';\nimport { listItem } from './list-item.js';\nimport { list } from './list.js';\nimport { paragraph } from './paragraph.js';\nimport { root } from './root.js';\nimport { strong } from './strong.js';\nimport { table } from './table.js';\nimport { tableRow } from './table-row.js';\nimport { tableCell } from './table-cell.js';\nimport { text } from './text.js';\nimport { thematicBreak } from './thematic-break.js';\n\n/**\n * Default handlers for nodes.\n *\n * @satisfies {import('../state.js').Handlers}\n */\nexport const handlers = {\n  blockquote,\n  break: hardBreak,\n  code,\n  delete: strikethrough,\n  emphasis,\n  footnoteReference,\n  heading,\n  html,\n  imageReference,\n  image,\n  inlineCode,\n  linkReference,\n  link,\n  listItem,\n  list,\n  paragraph,\n  // @ts-expect-error: root is different, but hard to type.\n  root,\n  strong,\n  table,\n  tableCell,\n  tableRow,\n  text,\n  thematicBreak,\n  toml: ignore,\n  yaml: ignore,\n  definition: ignore,\n  footnoteDefinition: ignore\n};\n\n// Return nothing for nodes that are ignored.\nfunction ignore() {\n  return undefined;\n}", "map": {"version": 3, "names": ["blockquote", "hardBreak", "code", "strikethrough", "emphasis", "footnoteReference", "heading", "html", "imageReference", "image", "inlineCode", "linkReference", "link", "listItem", "list", "paragraph", "root", "strong", "table", "tableRow", "tableCell", "text", "thematicBreak", "handlers", "break", "delete", "toml", "ignore", "yaml", "definition", "footnoteDefinition", "undefined"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/mdast-util-to-hast/lib/handlers/index.js"], "sourcesContent": ["import {blockquote} from './blockquote.js'\nimport {hardBreak} from './break.js'\nimport {code} from './code.js'\nimport {strikethrough} from './delete.js'\nimport {emphasis} from './emphasis.js'\nimport {footnoteReference} from './footnote-reference.js'\nimport {heading} from './heading.js'\nimport {html} from './html.js'\nimport {imageReference} from './image-reference.js'\nimport {image} from './image.js'\nimport {inlineCode} from './inline-code.js'\nimport {linkReference} from './link-reference.js'\nimport {link} from './link.js'\nimport {listItem} from './list-item.js'\nimport {list} from './list.js'\nimport {paragraph} from './paragraph.js'\nimport {root} from './root.js'\nimport {strong} from './strong.js'\nimport {table} from './table.js'\nimport {tableRow} from './table-row.js'\nimport {tableCell} from './table-cell.js'\nimport {text} from './text.js'\nimport {thematicBreak} from './thematic-break.js'\n\n/**\n * Default handlers for nodes.\n *\n * @satisfies {import('../state.js').Handlers}\n */\nexport const handlers = {\n  blockquote,\n  break: hardBreak,\n  code,\n  delete: strikethrough,\n  emphasis,\n  footnoteReference,\n  heading,\n  html,\n  imageReference,\n  image,\n  inlineCode,\n  linkReference,\n  link,\n  listItem,\n  list,\n  paragraph,\n  // @ts-expect-error: root is different, but hard to type.\n  root,\n  strong,\n  table,\n  tableCell,\n  tableRow,\n  text,\n  thematicBreak,\n  toml: ignore,\n  yaml: ignore,\n  definition: ignore,\n  footnoteDefinition: ignore\n}\n\n// Return nothing for nodes that are ignored.\nfunction ignore() {\n  return undefined\n}\n"], "mappings": "AAAA,SAAQA,UAAU,QAAO,iBAAiB;AAC1C,SAAQC,SAAS,QAAO,YAAY;AACpC,SAAQC,IAAI,QAAO,WAAW;AAC9B,SAAQC,aAAa,QAAO,aAAa;AACzC,SAAQC,QAAQ,QAAO,eAAe;AACtC,SAAQC,iBAAiB,QAAO,yBAAyB;AACzD,SAAQC,OAAO,QAAO,cAAc;AACpC,SAAQC,IAAI,QAAO,WAAW;AAC9B,SAAQC,cAAc,QAAO,sBAAsB;AACnD,SAAQC,KAAK,QAAO,YAAY;AAChC,SAAQC,UAAU,QAAO,kBAAkB;AAC3C,SAAQC,aAAa,QAAO,qBAAqB;AACjD,SAAQC,IAAI,QAAO,WAAW;AAC9B,SAAQC,QAAQ,QAAO,gBAAgB;AACvC,SAAQC,IAAI,QAAO,WAAW;AAC9B,SAAQC,SAAS,QAAO,gBAAgB;AACxC,SAAQC,IAAI,QAAO,WAAW;AAC9B,SAAQC,MAAM,QAAO,aAAa;AAClC,SAAQC,KAAK,QAAO,YAAY;AAChC,SAAQC,QAAQ,QAAO,gBAAgB;AACvC,SAAQC,SAAS,QAAO,iBAAiB;AACzC,SAAQC,IAAI,QAAO,WAAW;AAC9B,SAAQC,aAAa,QAAO,qBAAqB;;AAEjD;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,QAAQ,GAAG;EACtBvB,UAAU;EACVwB,KAAK,EAAEvB,SAAS;EAChBC,IAAI;EACJuB,MAAM,EAAEtB,aAAa;EACrBC,QAAQ;EACRC,iBAAiB;EACjBC,OAAO;EACPC,IAAI;EACJC,cAAc;EACdC,KAAK;EACLC,UAAU;EACVC,aAAa;EACbC,IAAI;EACJC,QAAQ;EACRC,IAAI;EACJC,SAAS;EACT;EACAC,IAAI;EACJC,MAAM;EACNC,KAAK;EACLE,SAAS;EACTD,QAAQ;EACRE,IAAI;EACJC,aAAa;EACbI,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAED,MAAM;EACZE,UAAU,EAAEF,MAAM;EAClBG,kBAAkB,EAAEH;AACtB,CAAC;;AAED;AACA,SAASA,MAAMA,CAAA,EAAG;EAChB,OAAOI,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}