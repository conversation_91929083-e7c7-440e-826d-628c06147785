{"ast": null, "code": "/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n  createDebug.debug = createDebug;\n  createDebug.default = createDebug;\n  createDebug.coerce = coerce;\n  createDebug.disable = disable;\n  createDebug.enable = enable;\n  createDebug.enabled = enabled;\n  createDebug.humanize = require('ms');\n  createDebug.destroy = destroy;\n  Object.keys(env).forEach(key => {\n    createDebug[key] = env[key];\n  });\n\n  /**\n  * The currently active debug mode names, and names to skip.\n  */\n\n  createDebug.names = [];\n  createDebug.skips = [];\n\n  /**\n  * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n  *\n  * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n  */\n  createDebug.formatters = {};\n\n  /**\n  * Selects a color for a debug namespace\n  * @param {String} namespace The namespace string for the debug instance to be colored\n  * @return {Number|String} An ANSI color code for the given namespace\n  * @api private\n  */\n  function selectColor(namespace) {\n    let hash = 0;\n    for (let i = 0; i < namespace.length; i++) {\n      hash = (hash << 5) - hash + namespace.charCodeAt(i);\n      hash |= 0; // Convert to 32bit integer\n    }\n    return createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n  }\n  createDebug.selectColor = selectColor;\n\n  /**\n  * Create a debugger with the given `namespace`.\n  *\n  * @param {String} namespace\n  * @return {Function}\n  * @api public\n  */\n  function createDebug(namespace) {\n    let prevTime;\n    let enableOverride = null;\n    let namespacesCache;\n    let enabledCache;\n    function debug(...args) {\n      // Disabled?\n      if (!debug.enabled) {\n        return;\n      }\n      const self = debug;\n\n      // Set `diff` timestamp\n      const curr = Number(new Date());\n      const ms = curr - (prevTime || curr);\n      self.diff = ms;\n      self.prev = prevTime;\n      self.curr = curr;\n      prevTime = curr;\n      args[0] = createDebug.coerce(args[0]);\n      if (typeof args[0] !== 'string') {\n        // Anything else let's inspect with %O\n        args.unshift('%O');\n      }\n\n      // Apply any `formatters` transformations\n      let index = 0;\n      args[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n        // If we encounter an escaped % then don't increase the array index\n        if (match === '%%') {\n          return '%';\n        }\n        index++;\n        const formatter = createDebug.formatters[format];\n        if (typeof formatter === 'function') {\n          const val = args[index];\n          match = formatter.call(self, val);\n\n          // Now we need to remove `args[index]` since it's inlined in the `format`\n          args.splice(index, 1);\n          index--;\n        }\n        return match;\n      });\n\n      // Apply env-specific formatting (colors, etc.)\n      createDebug.formatArgs.call(self, args);\n      const logFn = self.log || createDebug.log;\n      logFn.apply(self, args);\n    }\n    debug.namespace = namespace;\n    debug.useColors = createDebug.useColors();\n    debug.color = createDebug.selectColor(namespace);\n    debug.extend = extend;\n    debug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n    Object.defineProperty(debug, 'enabled', {\n      enumerable: true,\n      configurable: false,\n      get: () => {\n        if (enableOverride !== null) {\n          return enableOverride;\n        }\n        if (namespacesCache !== createDebug.namespaces) {\n          namespacesCache = createDebug.namespaces;\n          enabledCache = createDebug.enabled(namespace);\n        }\n        return enabledCache;\n      },\n      set: v => {\n        enableOverride = v;\n      }\n    });\n\n    // Env-specific initialization logic for debug instances\n    if (typeof createDebug.init === 'function') {\n      createDebug.init(debug);\n    }\n    return debug;\n  }\n  function extend(namespace, delimiter) {\n    const newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n    newDebug.log = this.log;\n    return newDebug;\n  }\n\n  /**\n  * Enables a debug mode by namespaces. This can include modes\n  * separated by a colon and wildcards.\n  *\n  * @param {String} namespaces\n  * @api public\n  */\n  function enable(namespaces) {\n    createDebug.save(namespaces);\n    createDebug.namespaces = namespaces;\n    createDebug.names = [];\n    createDebug.skips = [];\n    const split = (typeof namespaces === 'string' ? namespaces : '').trim().replace(/\\s+/g, ',').split(',').filter(Boolean);\n    for (const ns of split) {\n      if (ns[0] === '-') {\n        createDebug.skips.push(ns.slice(1));\n      } else {\n        createDebug.names.push(ns);\n      }\n    }\n  }\n\n  /**\n   * Checks if the given string matches a namespace template, honoring\n   * asterisks as wildcards.\n   *\n   * @param {String} search\n   * @param {String} template\n   * @return {Boolean}\n   */\n  function matchesTemplate(search, template) {\n    let searchIndex = 0;\n    let templateIndex = 0;\n    let starIndex = -1;\n    let matchIndex = 0;\n    while (searchIndex < search.length) {\n      if (templateIndex < template.length && (template[templateIndex] === search[searchIndex] || template[templateIndex] === '*')) {\n        // Match character or proceed with wildcard\n        if (template[templateIndex] === '*') {\n          starIndex = templateIndex;\n          matchIndex = searchIndex;\n          templateIndex++; // Skip the '*'\n        } else {\n          searchIndex++;\n          templateIndex++;\n        }\n      } else if (starIndex !== -1) {\n        // eslint-disable-line no-negated-condition\n        // Backtrack to the last '*' and try to match more characters\n        templateIndex = starIndex + 1;\n        matchIndex++;\n        searchIndex = matchIndex;\n      } else {\n        return false; // No match\n      }\n    }\n\n    // Handle trailing '*' in template\n    while (templateIndex < template.length && template[templateIndex] === '*') {\n      templateIndex++;\n    }\n    return templateIndex === template.length;\n  }\n\n  /**\n  * Disable debug output.\n  *\n  * @return {String} namespaces\n  * @api public\n  */\n  function disable() {\n    const namespaces = [...createDebug.names, ...createDebug.skips.map(namespace => '-' + namespace)].join(',');\n    createDebug.enable('');\n    return namespaces;\n  }\n\n  /**\n  * Returns true if the given mode name is enabled, false otherwise.\n  *\n  * @param {String} name\n  * @return {Boolean}\n  * @api public\n  */\n  function enabled(name) {\n    for (const skip of createDebug.skips) {\n      if (matchesTemplate(name, skip)) {\n        return false;\n      }\n    }\n    for (const ns of createDebug.names) {\n      if (matchesTemplate(name, ns)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n  * Coerce `val`.\n  *\n  * @param {Mixed} val\n  * @return {Mixed}\n  * @api private\n  */\n  function coerce(val) {\n    if (val instanceof Error) {\n      return val.stack || val.message;\n    }\n    return val;\n  }\n\n  /**\n  * XXX DO NOT USE. This is a temporary stub function.\n  * XXX It WILL be removed in the next major release.\n  */\n  function destroy() {\n    console.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n  }\n  createDebug.enable(createDebug.load());\n  return createDebug;\n}\nmodule.exports = setup;", "map": {"version": 3, "names": ["setup", "env", "createDebug", "debug", "default", "coerce", "disable", "enable", "enabled", "humanize", "require", "destroy", "Object", "keys", "for<PERSON>ach", "key", "names", "skips", "formatters", "selectColor", "namespace", "hash", "i", "length", "charCodeAt", "colors", "Math", "abs", "prevTime", "enableOverride", "namespacesCache", "enabledCache", "args", "self", "curr", "Number", "Date", "ms", "diff", "prev", "unshift", "index", "replace", "match", "format", "formatter", "val", "call", "splice", "formatArgs", "logFn", "log", "apply", "useColors", "color", "extend", "defineProperty", "enumerable", "configurable", "get", "namespaces", "set", "v", "init", "delimiter", "newDebug", "save", "split", "trim", "filter", "Boolean", "ns", "push", "slice", "matchesTemplate", "search", "template", "searchIndex", "templateIndex", "starIndex", "matchIndex", "map", "join", "name", "skip", "Error", "stack", "message", "console", "warn", "load", "module", "exports"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/debug/src/common.js"], "sourcesContent": ["\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '')\n\t\t\t.trim()\n\t\t\t.replace(/\\s+/g, ',')\n\t\t\t.split(',')\n\t\t\t.filter(Boolean);\n\n\t\tfor (const ns of split) {\n\t\t\tif (ns[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(ns.slice(1));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(ns);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Checks if the given string matches a namespace template, honoring\n\t * asterisks as wildcards.\n\t *\n\t * @param {String} search\n\t * @param {String} template\n\t * @return {Boolean}\n\t */\n\tfunction matchesTemplate(search, template) {\n\t\tlet searchIndex = 0;\n\t\tlet templateIndex = 0;\n\t\tlet starIndex = -1;\n\t\tlet matchIndex = 0;\n\n\t\twhile (searchIndex < search.length) {\n\t\t\tif (templateIndex < template.length && (template[templateIndex] === search[searchIndex] || template[templateIndex] === '*')) {\n\t\t\t\t// Match character or proceed with wildcard\n\t\t\t\tif (template[templateIndex] === '*') {\n\t\t\t\t\tstarIndex = templateIndex;\n\t\t\t\t\tmatchIndex = searchIndex;\n\t\t\t\t\ttemplateIndex++; // Skip the '*'\n\t\t\t\t} else {\n\t\t\t\t\tsearchIndex++;\n\t\t\t\t\ttemplateIndex++;\n\t\t\t\t}\n\t\t\t} else if (starIndex !== -1) { // eslint-disable-line no-negated-condition\n\t\t\t\t// Backtrack to the last '*' and try to match more characters\n\t\t\t\ttemplateIndex = starIndex + 1;\n\t\t\t\tmatchIndex++;\n\t\t\t\tsearchIndex = matchIndex;\n\t\t\t} else {\n\t\t\t\treturn false; // No match\n\t\t\t}\n\t\t}\n\n\t\t// Handle trailing '*' in template\n\t\twhile (templateIndex < template.length && template[templateIndex] === '*') {\n\t\t\ttemplateIndex++;\n\t\t}\n\n\t\treturn templateIndex === template.length;\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names,\n\t\t\t...createDebug.skips.map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tfor (const skip of createDebug.skips) {\n\t\t\tif (matchesTemplate(name, skip)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (const ns of createDebug.names) {\n\t\t\tif (matchesTemplate(name, ns)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n"], "mappings": "AACA;AACA;AACA;AACA;;AAEA,SAASA,KAAKA,CAACC,GAAG,EAAE;EACnBC,WAAW,CAACC,KAAK,GAAGD,WAAW;EAC/BA,WAAW,CAACE,OAAO,GAAGF,WAAW;EACjCA,WAAW,CAACG,MAAM,GAAGA,MAAM;EAC3BH,WAAW,CAACI,OAAO,GAAGA,OAAO;EAC7BJ,WAAW,CAACK,MAAM,GAAGA,MAAM;EAC3BL,WAAW,CAACM,OAAO,GAAGA,OAAO;EAC7BN,WAAW,CAACO,QAAQ,GAAGC,OAAO,CAAC,IAAI,CAAC;EACpCR,WAAW,CAACS,OAAO,GAAGA,OAAO;EAE7BC,MAAM,CAACC,IAAI,CAACZ,GAAG,CAAC,CAACa,OAAO,CAACC,GAAG,IAAI;IAC/Bb,WAAW,CAACa,GAAG,CAAC,GAAGd,GAAG,CAACc,GAAG,CAAC;EAC5B,CAAC,CAAC;;EAEF;AACD;AACA;;EAECb,WAAW,CAACc,KAAK,GAAG,EAAE;EACtBd,WAAW,CAACe,KAAK,GAAG,EAAE;;EAEtB;AACD;AACA;AACA;AACA;EACCf,WAAW,CAACgB,UAAU,GAAG,CAAC,CAAC;;EAE3B;AACD;AACA;AACA;AACA;AACA;EACC,SAASC,WAAWA,CAACC,SAAS,EAAE;IAC/B,IAAIC,IAAI,GAAG,CAAC;IAEZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1CD,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAID,SAAS,CAACI,UAAU,CAACF,CAAC,CAAC;MACrDD,IAAI,IAAI,CAAC,CAAC,CAAC;IACZ;IAEA,OAAOnB,WAAW,CAACuB,MAAM,CAACC,IAAI,CAACC,GAAG,CAACN,IAAI,CAAC,GAAGnB,WAAW,CAACuB,MAAM,CAACF,MAAM,CAAC;EACtE;EACArB,WAAW,CAACiB,WAAW,GAAGA,WAAW;;EAErC;AACD;AACA;AACA;AACA;AACA;AACA;EACC,SAASjB,WAAWA,CAACkB,SAAS,EAAE;IAC/B,IAAIQ,QAAQ;IACZ,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAIC,eAAe;IACnB,IAAIC,YAAY;IAEhB,SAAS5B,KAAKA,CAAC,GAAG6B,IAAI,EAAE;MACvB;MACA,IAAI,CAAC7B,KAAK,CAACK,OAAO,EAAE;QACnB;MACD;MAEA,MAAMyB,IAAI,GAAG9B,KAAK;;MAElB;MACA,MAAM+B,IAAI,GAAGC,MAAM,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;MAC/B,MAAMC,EAAE,GAAGH,IAAI,IAAIN,QAAQ,IAAIM,IAAI,CAAC;MACpCD,IAAI,CAACK,IAAI,GAAGD,EAAE;MACdJ,IAAI,CAACM,IAAI,GAAGX,QAAQ;MACpBK,IAAI,CAACC,IAAI,GAAGA,IAAI;MAChBN,QAAQ,GAAGM,IAAI;MAEfF,IAAI,CAAC,CAAC,CAAC,GAAG9B,WAAW,CAACG,MAAM,CAAC2B,IAAI,CAAC,CAAC,CAAC,CAAC;MAErC,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QAChC;QACAA,IAAI,CAACQ,OAAO,CAAC,IAAI,CAAC;MACnB;;MAEA;MACA,IAAIC,KAAK,GAAG,CAAC;MACbT,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,CAACU,OAAO,CAAC,eAAe,EAAE,CAACC,KAAK,EAAEC,MAAM,KAAK;QAC7D;QACA,IAAID,KAAK,KAAK,IAAI,EAAE;UACnB,OAAO,GAAG;QACX;QACAF,KAAK,EAAE;QACP,MAAMI,SAAS,GAAG3C,WAAW,CAACgB,UAAU,CAAC0B,MAAM,CAAC;QAChD,IAAI,OAAOC,SAAS,KAAK,UAAU,EAAE;UACpC,MAAMC,GAAG,GAAGd,IAAI,CAACS,KAAK,CAAC;UACvBE,KAAK,GAAGE,SAAS,CAACE,IAAI,CAACd,IAAI,EAAEa,GAAG,CAAC;;UAEjC;UACAd,IAAI,CAACgB,MAAM,CAACP,KAAK,EAAE,CAAC,CAAC;UACrBA,KAAK,EAAE;QACR;QACA,OAAOE,KAAK;MACb,CAAC,CAAC;;MAEF;MACAzC,WAAW,CAAC+C,UAAU,CAACF,IAAI,CAACd,IAAI,EAAED,IAAI,CAAC;MAEvC,MAAMkB,KAAK,GAAGjB,IAAI,CAACkB,GAAG,IAAIjD,WAAW,CAACiD,GAAG;MACzCD,KAAK,CAACE,KAAK,CAACnB,IAAI,EAAED,IAAI,CAAC;IACxB;IAEA7B,KAAK,CAACiB,SAAS,GAAGA,SAAS;IAC3BjB,KAAK,CAACkD,SAAS,GAAGnD,WAAW,CAACmD,SAAS,CAAC,CAAC;IACzClD,KAAK,CAACmD,KAAK,GAAGpD,WAAW,CAACiB,WAAW,CAACC,SAAS,CAAC;IAChDjB,KAAK,CAACoD,MAAM,GAAGA,MAAM;IACrBpD,KAAK,CAACQ,OAAO,GAAGT,WAAW,CAACS,OAAO,CAAC,CAAC;;IAErCC,MAAM,CAAC4C,cAAc,CAACrD,KAAK,EAAE,SAAS,EAAE;MACvCsD,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,KAAK;MACnBC,GAAG,EAAEA,CAAA,KAAM;QACV,IAAI9B,cAAc,KAAK,IAAI,EAAE;UAC5B,OAAOA,cAAc;QACtB;QACA,IAAIC,eAAe,KAAK5B,WAAW,CAAC0D,UAAU,EAAE;UAC/C9B,eAAe,GAAG5B,WAAW,CAAC0D,UAAU;UACxC7B,YAAY,GAAG7B,WAAW,CAACM,OAAO,CAACY,SAAS,CAAC;QAC9C;QAEA,OAAOW,YAAY;MACpB,CAAC;MACD8B,GAAG,EAAEC,CAAC,IAAI;QACTjC,cAAc,GAAGiC,CAAC;MACnB;IACD,CAAC,CAAC;;IAEF;IACA,IAAI,OAAO5D,WAAW,CAAC6D,IAAI,KAAK,UAAU,EAAE;MAC3C7D,WAAW,CAAC6D,IAAI,CAAC5D,KAAK,CAAC;IACxB;IAEA,OAAOA,KAAK;EACb;EAEA,SAASoD,MAAMA,CAACnC,SAAS,EAAE4C,SAAS,EAAE;IACrC,MAAMC,QAAQ,GAAG/D,WAAW,CAAC,IAAI,CAACkB,SAAS,IAAI,OAAO4C,SAAS,KAAK,WAAW,GAAG,GAAG,GAAGA,SAAS,CAAC,GAAG5C,SAAS,CAAC;IAC/G6C,QAAQ,CAACd,GAAG,GAAG,IAAI,CAACA,GAAG;IACvB,OAAOc,QAAQ;EAChB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC,SAAS1D,MAAMA,CAACqD,UAAU,EAAE;IAC3B1D,WAAW,CAACgE,IAAI,CAACN,UAAU,CAAC;IAC5B1D,WAAW,CAAC0D,UAAU,GAAGA,UAAU;IAEnC1D,WAAW,CAACc,KAAK,GAAG,EAAE;IACtBd,WAAW,CAACe,KAAK,GAAG,EAAE;IAEtB,MAAMkD,KAAK,GAAG,CAAC,OAAOP,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,EAAE,EAC7DQ,IAAI,CAAC,CAAC,CACN1B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpByB,KAAK,CAAC,GAAG,CAAC,CACVE,MAAM,CAACC,OAAO,CAAC;IAEjB,KAAK,MAAMC,EAAE,IAAIJ,KAAK,EAAE;MACvB,IAAII,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAClBrE,WAAW,CAACe,KAAK,CAACuD,IAAI,CAACD,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,MAAM;QACNvE,WAAW,CAACc,KAAK,CAACwD,IAAI,CAACD,EAAE,CAAC;MAC3B;IACD;EACD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASG,eAAeA,CAACC,MAAM,EAAEC,QAAQ,EAAE;IAC1C,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,SAAS,GAAG,CAAC,CAAC;IAClB,IAAIC,UAAU,GAAG,CAAC;IAElB,OAAOH,WAAW,GAAGF,MAAM,CAACpD,MAAM,EAAE;MACnC,IAAIuD,aAAa,GAAGF,QAAQ,CAACrD,MAAM,KAAKqD,QAAQ,CAACE,aAAa,CAAC,KAAKH,MAAM,CAACE,WAAW,CAAC,IAAID,QAAQ,CAACE,aAAa,CAAC,KAAK,GAAG,CAAC,EAAE;QAC5H;QACA,IAAIF,QAAQ,CAACE,aAAa,CAAC,KAAK,GAAG,EAAE;UACpCC,SAAS,GAAGD,aAAa;UACzBE,UAAU,GAAGH,WAAW;UACxBC,aAAa,EAAE,CAAC,CAAC;QAClB,CAAC,MAAM;UACND,WAAW,EAAE;UACbC,aAAa,EAAE;QAChB;MACD,CAAC,MAAM,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;QAAE;QAC9B;QACAD,aAAa,GAAGC,SAAS,GAAG,CAAC;QAC7BC,UAAU,EAAE;QACZH,WAAW,GAAGG,UAAU;MACzB,CAAC,MAAM;QACN,OAAO,KAAK,CAAC,CAAC;MACf;IACD;;IAEA;IACA,OAAOF,aAAa,GAAGF,QAAQ,CAACrD,MAAM,IAAIqD,QAAQ,CAACE,aAAa,CAAC,KAAK,GAAG,EAAE;MAC1EA,aAAa,EAAE;IAChB;IAEA,OAAOA,aAAa,KAAKF,QAAQ,CAACrD,MAAM;EACzC;;EAEA;AACD;AACA;AACA;AACA;AACA;EACC,SAASjB,OAAOA,CAAA,EAAG;IAClB,MAAMsD,UAAU,GAAG,CAClB,GAAG1D,WAAW,CAACc,KAAK,EACpB,GAAGd,WAAW,CAACe,KAAK,CAACgE,GAAG,CAAC7D,SAAS,IAAI,GAAG,GAAGA,SAAS,CAAC,CACtD,CAAC8D,IAAI,CAAC,GAAG,CAAC;IACXhF,WAAW,CAACK,MAAM,CAAC,EAAE,CAAC;IACtB,OAAOqD,UAAU;EAClB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC,SAASpD,OAAOA,CAAC2E,IAAI,EAAE;IACtB,KAAK,MAAMC,IAAI,IAAIlF,WAAW,CAACe,KAAK,EAAE;MACrC,IAAIyD,eAAe,CAACS,IAAI,EAAEC,IAAI,CAAC,EAAE;QAChC,OAAO,KAAK;MACb;IACD;IAEA,KAAK,MAAMb,EAAE,IAAIrE,WAAW,CAACc,KAAK,EAAE;MACnC,IAAI0D,eAAe,CAACS,IAAI,EAAEZ,EAAE,CAAC,EAAE;QAC9B,OAAO,IAAI;MACZ;IACD;IAEA,OAAO,KAAK;EACb;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC,SAASlE,MAAMA,CAACyC,GAAG,EAAE;IACpB,IAAIA,GAAG,YAAYuC,KAAK,EAAE;MACzB,OAAOvC,GAAG,CAACwC,KAAK,IAAIxC,GAAG,CAACyC,OAAO;IAChC;IACA,OAAOzC,GAAG;EACX;;EAEA;AACD;AACA;AACA;EACC,SAASnC,OAAOA,CAAA,EAAG;IAClB6E,OAAO,CAACC,IAAI,CAAC,uIAAuI,CAAC;EACtJ;EAEAvF,WAAW,CAACK,MAAM,CAACL,WAAW,CAACwF,IAAI,CAAC,CAAC,CAAC;EAEtC,OAAOxF,WAAW;AACnB;AAEAyF,MAAM,CAACC,OAAO,GAAG5F,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}