{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/ImageUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useRef, useState } from 'react';\nimport { useImageContext } from '../contexts/ImageContext';\nimport './ImageUpload.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageUpload = ({\n  collectionId\n}) => {\n  _s();\n  const {\n    addImage\n  } = useImageContext();\n  const fileInputRef = useRef(null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [isUploading, setIsUploading] = useState(false);\n  const handleFileSelect = async files => {\n    if (!files || files.length === 0) return;\n    setIsUploading(true);\n    try {\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        if (file.type.startsWith('image/')) {\n          await addImage(file, collectionId);\n        }\n      }\n    } catch (error) {\n      console.error('Error uploading images:', error);\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    setIsDragging(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setIsDragging(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragging(false);\n    handleFileSelect(e.dataTransfer.files);\n  };\n  const handleClick = () => {\n    var _fileInputRef$current;\n    (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n  };\n  const handleFileInputChange = e => {\n    handleFileSelect(e.target.files);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"image-upload\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `upload-area ${isDragging ? 'dragging' : ''} ${isUploading ? 'uploading' : ''}`,\n      onDragOver: handleDragOver,\n      onDragLeave: handleDragLeave,\n      onDrop: handleDrop,\n      onClick: handleClick,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        ref: fileInputRef,\n        type: \"file\",\n        accept: \"image/*\",\n        multiple: true,\n        onChange: handleFileInputChange,\n        style: {\n          display: 'none'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), isUploading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"upload-status\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u0110ang t\\u1EA3i \\u1EA3nh l\\xEAn...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"upload-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"upload-icon\",\n          children: \"\\uD83D\\uDCF7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"K\\xE9o th\\u1EA3 \\u1EA3nh v\\xE0o \\u0111\\xE2y ho\\u1EB7c click \\u0111\\u1EC3 ch\\u1ECDn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          children: \"H\\u1ED7 tr\\u1EE3 nhi\\u1EC1u \\u1EA3nh c\\xF9ng l\\xFAc\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(ImageUpload, \"GVJC+liVylna32pD6GxTAYhWQUg=\", false, function () {\n  return [useImageContext];\n});\n_c = ImageUpload;\nexport default ImageUpload;\nvar _c;\n$RefreshReg$(_c, \"ImageUpload\");", "map": {"version": 3, "names": ["React", "useRef", "useState", "useImageContext", "jsxDEV", "_jsxDEV", "ImageUpload", "collectionId", "_s", "addImage", "fileInputRef", "isDragging", "setIsDragging", "isUploading", "setIsUploading", "handleFileSelect", "files", "length", "i", "file", "type", "startsWith", "error", "console", "handleDragOver", "e", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleClick", "_fileInputRef$current", "current", "click", "handleFileInputChange", "target", "className", "children", "onDragOver", "onDragLeave", "onDrop", "onClick", "ref", "accept", "multiple", "onChange", "style", "display", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/ImageUpload.tsx"], "sourcesContent": ["import React, { useRef, useState } from 'react';\nimport { useImageContext } from '../contexts/ImageContext';\nimport './ImageUpload.css';\n\ninterface ImageUploadProps {\n  collectionId: string;\n}\n\nconst ImageUpload: React.FC<ImageUploadProps> = ({ collectionId }) => {\n  const { addImage } = useImageContext();\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [isUploading, setIsUploading] = useState(false);\n\n  const handleFileSelect = async (files: FileList | null) => {\n    if (!files || files.length === 0) return;\n\n    setIsUploading(true);\n    \n    try {\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        if (file.type.startsWith('image/')) {\n          await addImage(file, collectionId);\n        }\n      }\n    } catch (error) {\n      console.error('Error uploading images:', error);\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n    handleFileSelect(e.dataTransfer.files);\n  };\n\n  const handleClick = () => {\n    fileInputRef.current?.click();\n  };\n\n  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    handleFileSelect(e.target.files);\n  };\n\n  return (\n    <div className=\"image-upload\">\n      <div \n        className={`upload-area ${isDragging ? 'dragging' : ''} ${isUploading ? 'uploading' : ''}`}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n        onClick={handleClick}\n      >\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          accept=\"image/*\"\n          multiple\n          onChange={handleFileInputChange}\n          style={{ display: 'none' }}\n        />\n        \n        {isUploading ? (\n          <div className=\"upload-status\">\n            <div className=\"spinner\"></div>\n            <p>Đang tải ảnh lên...</p>\n          </div>\n        ) : (\n          <div className=\"upload-content\">\n            <div className=\"upload-icon\">📷</div>\n            <p>Kéo thả ảnh vào đây hoặc click để chọn</p>\n            <small>Hỗ trợ nhiều ảnh cùng lúc</small>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ImageUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC/C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM3B,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACpE,MAAM;IAAEC;EAAS,CAAC,GAAGN,eAAe,CAAC,CAAC;EACtC,MAAMO,YAAY,GAAGT,MAAM,CAAmB,IAAI,CAAC;EACnD,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMa,gBAAgB,GAAG,MAAOC,KAAsB,IAAK;IACzD,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IAElCH,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACC,MAAM,EAAEC,CAAC,EAAE,EAAE;QACrC,MAAMC,IAAI,GAAGH,KAAK,CAACE,CAAC,CAAC;QACrB,IAAIC,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;UAClC,MAAMZ,QAAQ,CAACU,IAAI,EAAEZ,YAAY,CAAC;QACpC;MACF;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRR,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMU,cAAc,GAAIC,CAAkB,IAAK;IAC7CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBd,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMe,eAAe,GAAIF,CAAkB,IAAK;IAC9CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBd,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMgB,UAAU,GAAIH,CAAkB,IAAK;IACzCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBd,aAAa,CAAC,KAAK,CAAC;IACpBG,gBAAgB,CAACU,CAAC,CAACI,YAAY,CAACb,KAAK,CAAC;EACxC,CAAC;EAED,MAAMc,WAAW,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IACxB,CAAAA,qBAAA,GAAArB,YAAY,CAACsB,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,KAAK,CAAC,CAAC;EAC/B,CAAC;EAED,MAAMC,qBAAqB,GAAIT,CAAsC,IAAK;IACxEV,gBAAgB,CAACU,CAAC,CAACU,MAAM,CAACnB,KAAK,CAAC;EAClC,CAAC;EAED,oBACEX,OAAA;IAAK+B,SAAS,EAAC,cAAc;IAAAC,QAAA,eAC3BhC,OAAA;MACE+B,SAAS,EAAE,eAAezB,UAAU,GAAG,UAAU,GAAG,EAAE,IAAIE,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;MAC3FyB,UAAU,EAAEd,cAAe;MAC3Be,WAAW,EAAEZ,eAAgB;MAC7Ba,MAAM,EAAEZ,UAAW;MACnBa,OAAO,EAAEX,WAAY;MAAAO,QAAA,gBAErBhC,OAAA;QACEqC,GAAG,EAAEhC,YAAa;QAClBU,IAAI,EAAC,MAAM;QACXuB,MAAM,EAAC,SAAS;QAChBC,QAAQ;QACRC,QAAQ,EAAEX,qBAAsB;QAChCY,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,EAEDtC,WAAW,gBACVR,OAAA;QAAK+B,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BhC,OAAA;UAAK+B,SAAS,EAAC;QAAS;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/B9C,OAAA;UAAAgC,QAAA,EAAG;QAAmB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,gBAEN9C,OAAA;QAAK+B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhC,OAAA;UAAK+B,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrC9C,OAAA;UAAAgC,QAAA,EAAG;QAAsC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7C9C,OAAA;UAAAgC,QAAA,EAAO;QAAyB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAlFIF,WAAuC;EAAA,QACtBH,eAAe;AAAA;AAAAiD,EAAA,GADhC9C,WAAuC;AAoF7C,eAAeA,WAAW;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}