{"ast": null, "code": "'use strict';\n\nvar hasOwn = Object.prototype.hasOwnProperty;\nvar toStr = Object.prototype.toString;\nvar defineProperty = Object.defineProperty;\nvar gOPD = Object.getOwnPropertyDescriptor;\nvar isArray = function isArray(arr) {\n  if (typeof Array.isArray === 'function') {\n    return Array.isArray(arr);\n  }\n  return toStr.call(arr) === '[object Array]';\n};\nvar isPlainObject = function isPlainObject(obj) {\n  if (!obj || toStr.call(obj) !== '[object Object]') {\n    return false;\n  }\n  var hasOwnConstructor = hasOwn.call(obj, 'constructor');\n  var hasIsPrototypeOf = obj.constructor && obj.constructor.prototype && hasOwn.call(obj.constructor.prototype, 'isPrototypeOf');\n  // Not own constructor property must be Object\n  if (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {\n    return false;\n  }\n\n  // Own properties are enumerated firstly, so to speed up,\n  // if last one is own, then all properties are own.\n  var key;\n  for (key in obj) {/**/}\n  return typeof key === 'undefined' || hasOwn.call(obj, key);\n};\n\n// If name is '__proto__', and Object.defineProperty is available, define __proto__ as an own property on target\nvar setProperty = function setProperty(target, options) {\n  if (defineProperty && options.name === '__proto__') {\n    defineProperty(target, options.name, {\n      enumerable: true,\n      configurable: true,\n      value: options.newValue,\n      writable: true\n    });\n  } else {\n    target[options.name] = options.newValue;\n  }\n};\n\n// Return undefined instead of __proto__ if '__proto__' is not an own property\nvar getProperty = function getProperty(obj, name) {\n  if (name === '__proto__') {\n    if (!hasOwn.call(obj, name)) {\n      return void 0;\n    } else if (gOPD) {\n      // In early versions of node, obj['__proto__'] is buggy when obj has\n      // __proto__ as an own property. Object.getOwnPropertyDescriptor() works.\n      return gOPD(obj, name).value;\n    }\n  }\n  return obj[name];\n};\nmodule.exports = function extend() {\n  var options, name, src, copy, copyIsArray, clone;\n  var target = arguments[0];\n  var i = 1;\n  var length = arguments.length;\n  var deep = false;\n\n  // Handle a deep copy situation\n  if (typeof target === 'boolean') {\n    deep = target;\n    target = arguments[1] || {};\n    // skip the boolean and the target\n    i = 2;\n  }\n  if (target == null || typeof target !== 'object' && typeof target !== 'function') {\n    target = {};\n  }\n  for (; i < length; ++i) {\n    options = arguments[i];\n    // Only deal with non-null/undefined values\n    if (options != null) {\n      // Extend the base object\n      for (name in options) {\n        src = getProperty(target, name);\n        copy = getProperty(options, name);\n\n        // Prevent never-ending loop\n        if (target !== copy) {\n          // Recurse if we're merging plain objects or arrays\n          if (deep && copy && (isPlainObject(copy) || (copyIsArray = isArray(copy)))) {\n            if (copyIsArray) {\n              copyIsArray = false;\n              clone = src && isArray(src) ? src : [];\n            } else {\n              clone = src && isPlainObject(src) ? src : {};\n            }\n\n            // Never move original objects, clone them\n            setProperty(target, {\n              name: name,\n              newValue: extend(deep, clone, copy)\n            });\n\n            // Don't bring in undefined values\n          } else if (typeof copy !== 'undefined') {\n            setProperty(target, {\n              name: name,\n              newValue: copy\n            });\n          }\n        }\n      }\n    }\n  }\n\n  // Return the modified object\n  return target;\n};", "map": {"version": 3, "names": ["hasOwn", "Object", "prototype", "hasOwnProperty", "toStr", "toString", "defineProperty", "gOPD", "getOwnPropertyDescriptor", "isArray", "arr", "Array", "call", "isPlainObject", "obj", "hasOwnConstructor", "hasIsPrototypeOf", "constructor", "key", "setProperty", "target", "options", "name", "enumerable", "configurable", "value", "newValue", "writable", "getProperty", "module", "exports", "extend", "src", "copy", "copyIsArray", "clone", "arguments", "i", "length", "deep"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/extend/index.js"], "sourcesContent": ["'use strict';\n\nvar hasOwn = Object.prototype.hasOwnProperty;\nvar toStr = Object.prototype.toString;\nvar defineProperty = Object.defineProperty;\nvar gOPD = Object.getOwnPropertyDescriptor;\n\nvar isArray = function isArray(arr) {\n\tif (typeof Array.isArray === 'function') {\n\t\treturn Array.isArray(arr);\n\t}\n\n\treturn toStr.call(arr) === '[object Array]';\n};\n\nvar isPlainObject = function isPlainObject(obj) {\n\tif (!obj || toStr.call(obj) !== '[object Object]') {\n\t\treturn false;\n\t}\n\n\tvar hasOwnConstructor = hasOwn.call(obj, 'constructor');\n\tvar hasIsPrototypeOf = obj.constructor && obj.constructor.prototype && hasOwn.call(obj.constructor.prototype, 'isPrototypeOf');\n\t// Not own constructor property must be Object\n\tif (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {\n\t\treturn false;\n\t}\n\n\t// Own properties are enumerated firstly, so to speed up,\n\t// if last one is own, then all properties are own.\n\tvar key;\n\tfor (key in obj) { /**/ }\n\n\treturn typeof key === 'undefined' || hasOwn.call(obj, key);\n};\n\n// If name is '__proto__', and Object.defineProperty is available, define __proto__ as an own property on target\nvar setProperty = function setProperty(target, options) {\n\tif (defineProperty && options.name === '__proto__') {\n\t\tdefineProperty(target, options.name, {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\t\t\tvalue: options.newValue,\n\t\t\twritable: true\n\t\t});\n\t} else {\n\t\ttarget[options.name] = options.newValue;\n\t}\n};\n\n// Return undefined instead of __proto__ if '__proto__' is not an own property\nvar getProperty = function getProperty(obj, name) {\n\tif (name === '__proto__') {\n\t\tif (!hasOwn.call(obj, name)) {\n\t\t\treturn void 0;\n\t\t} else if (gOPD) {\n\t\t\t// In early versions of node, obj['__proto__'] is buggy when obj has\n\t\t\t// __proto__ as an own property. Object.getOwnPropertyDescriptor() works.\n\t\t\treturn gOPD(obj, name).value;\n\t\t}\n\t}\n\n\treturn obj[name];\n};\n\nmodule.exports = function extend() {\n\tvar options, name, src, copy, copyIsArray, clone;\n\tvar target = arguments[0];\n\tvar i = 1;\n\tvar length = arguments.length;\n\tvar deep = false;\n\n\t// Handle a deep copy situation\n\tif (typeof target === 'boolean') {\n\t\tdeep = target;\n\t\ttarget = arguments[1] || {};\n\t\t// skip the boolean and the target\n\t\ti = 2;\n\t}\n\tif (target == null || (typeof target !== 'object' && typeof target !== 'function')) {\n\t\ttarget = {};\n\t}\n\n\tfor (; i < length; ++i) {\n\t\toptions = arguments[i];\n\t\t// Only deal with non-null/undefined values\n\t\tif (options != null) {\n\t\t\t// Extend the base object\n\t\t\tfor (name in options) {\n\t\t\t\tsrc = getProperty(target, name);\n\t\t\t\tcopy = getProperty(options, name);\n\n\t\t\t\t// Prevent never-ending loop\n\t\t\t\tif (target !== copy) {\n\t\t\t\t\t// Recurse if we're merging plain objects or arrays\n\t\t\t\t\tif (deep && copy && (isPlainObject(copy) || (copyIsArray = isArray(copy)))) {\n\t\t\t\t\t\tif (copyIsArray) {\n\t\t\t\t\t\t\tcopyIsArray = false;\n\t\t\t\t\t\t\tclone = src && isArray(src) ? src : [];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tclone = src && isPlainObject(src) ? src : {};\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Never move original objects, clone them\n\t\t\t\t\t\tsetProperty(target, { name: name, newValue: extend(deep, clone, copy) });\n\n\t\t\t\t\t// Don't bring in undefined values\n\t\t\t\t\t} else if (typeof copy !== 'undefined') {\n\t\t\t\t\t\tsetProperty(target, { name: name, newValue: copy });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// Return the modified object\n\treturn target;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAC5C,IAAIC,KAAK,GAAGH,MAAM,CAACC,SAAS,CAACG,QAAQ;AACrC,IAAIC,cAAc,GAAGL,MAAM,CAACK,cAAc;AAC1C,IAAIC,IAAI,GAAGN,MAAM,CAACO,wBAAwB;AAE1C,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;EACnC,IAAI,OAAOC,KAAK,CAACF,OAAO,KAAK,UAAU,EAAE;IACxC,OAAOE,KAAK,CAACF,OAAO,CAACC,GAAG,CAAC;EAC1B;EAEA,OAAON,KAAK,CAACQ,IAAI,CAACF,GAAG,CAAC,KAAK,gBAAgB;AAC5C,CAAC;AAED,IAAIG,aAAa,GAAG,SAASA,aAAaA,CAACC,GAAG,EAAE;EAC/C,IAAI,CAACA,GAAG,IAAIV,KAAK,CAACQ,IAAI,CAACE,GAAG,CAAC,KAAK,iBAAiB,EAAE;IAClD,OAAO,KAAK;EACb;EAEA,IAAIC,iBAAiB,GAAGf,MAAM,CAACY,IAAI,CAACE,GAAG,EAAE,aAAa,CAAC;EACvD,IAAIE,gBAAgB,GAAGF,GAAG,CAACG,WAAW,IAAIH,GAAG,CAACG,WAAW,CAACf,SAAS,IAAIF,MAAM,CAACY,IAAI,CAACE,GAAG,CAACG,WAAW,CAACf,SAAS,EAAE,eAAe,CAAC;EAC9H;EACA,IAAIY,GAAG,CAACG,WAAW,IAAI,CAACF,iBAAiB,IAAI,CAACC,gBAAgB,EAAE;IAC/D,OAAO,KAAK;EACb;;EAEA;EACA;EACA,IAAIE,GAAG;EACP,KAAKA,GAAG,IAAIJ,GAAG,EAAE,CAAE;EAEnB,OAAO,OAAOI,GAAG,KAAK,WAAW,IAAIlB,MAAM,CAACY,IAAI,CAACE,GAAG,EAAEI,GAAG,CAAC;AAC3D,CAAC;;AAED;AACA,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACvD,IAAIf,cAAc,IAAIe,OAAO,CAACC,IAAI,KAAK,WAAW,EAAE;IACnDhB,cAAc,CAACc,MAAM,EAAEC,OAAO,CAACC,IAAI,EAAE;MACpCC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,KAAK,EAAEJ,OAAO,CAACK,QAAQ;MACvBC,QAAQ,EAAE;IACX,CAAC,CAAC;EACH,CAAC,MAAM;IACNP,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,GAAGD,OAAO,CAACK,QAAQ;EACxC;AACD,CAAC;;AAED;AACA,IAAIE,WAAW,GAAG,SAASA,WAAWA,CAACd,GAAG,EAAEQ,IAAI,EAAE;EACjD,IAAIA,IAAI,KAAK,WAAW,EAAE;IACzB,IAAI,CAACtB,MAAM,CAACY,IAAI,CAACE,GAAG,EAAEQ,IAAI,CAAC,EAAE;MAC5B,OAAO,KAAK,CAAC;IACd,CAAC,MAAM,IAAIf,IAAI,EAAE;MAChB;MACA;MACA,OAAOA,IAAI,CAACO,GAAG,EAAEQ,IAAI,CAAC,CAACG,KAAK;IAC7B;EACD;EAEA,OAAOX,GAAG,CAACQ,IAAI,CAAC;AACjB,CAAC;AAEDO,MAAM,CAACC,OAAO,GAAG,SAASC,MAAMA,CAAA,EAAG;EAClC,IAAIV,OAAO,EAAEC,IAAI,EAAEU,GAAG,EAAEC,IAAI,EAAEC,WAAW,EAAEC,KAAK;EAChD,IAAIf,MAAM,GAAGgB,SAAS,CAAC,CAAC,CAAC;EACzB,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,MAAM,GAAGF,SAAS,CAACE,MAAM;EAC7B,IAAIC,IAAI,GAAG,KAAK;;EAEhB;EACA,IAAI,OAAOnB,MAAM,KAAK,SAAS,EAAE;IAChCmB,IAAI,GAAGnB,MAAM;IACbA,MAAM,GAAGgB,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC3B;IACAC,CAAC,GAAG,CAAC;EACN;EACA,IAAIjB,MAAM,IAAI,IAAI,IAAK,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,KAAK,UAAW,EAAE;IACnFA,MAAM,GAAG,CAAC,CAAC;EACZ;EAEA,OAAOiB,CAAC,GAAGC,MAAM,EAAE,EAAED,CAAC,EAAE;IACvBhB,OAAO,GAAGe,SAAS,CAACC,CAAC,CAAC;IACtB;IACA,IAAIhB,OAAO,IAAI,IAAI,EAAE;MACpB;MACA,KAAKC,IAAI,IAAID,OAAO,EAAE;QACrBW,GAAG,GAAGJ,WAAW,CAACR,MAAM,EAAEE,IAAI,CAAC;QAC/BW,IAAI,GAAGL,WAAW,CAACP,OAAO,EAAEC,IAAI,CAAC;;QAEjC;QACA,IAAIF,MAAM,KAAKa,IAAI,EAAE;UACpB;UACA,IAAIM,IAAI,IAAIN,IAAI,KAAKpB,aAAa,CAACoB,IAAI,CAAC,KAAKC,WAAW,GAAGzB,OAAO,CAACwB,IAAI,CAAC,CAAC,CAAC,EAAE;YAC3E,IAAIC,WAAW,EAAE;cAChBA,WAAW,GAAG,KAAK;cACnBC,KAAK,GAAGH,GAAG,IAAIvB,OAAO,CAACuB,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE;YACvC,CAAC,MAAM;cACNG,KAAK,GAAGH,GAAG,IAAInB,aAAa,CAACmB,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,CAAC;YAC7C;;YAEA;YACAb,WAAW,CAACC,MAAM,EAAE;cAAEE,IAAI,EAAEA,IAAI;cAAEI,QAAQ,EAAEK,MAAM,CAACQ,IAAI,EAAEJ,KAAK,EAAEF,IAAI;YAAE,CAAC,CAAC;;YAEzE;UACA,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,WAAW,EAAE;YACvCd,WAAW,CAACC,MAAM,EAAE;cAAEE,IAAI,EAAEA,IAAI;cAAEI,QAAQ,EAAEO;YAAK,CAAC,CAAC;UACpD;QACD;MACD;IACD;EACD;;EAEA;EACA,OAAOb,MAAM;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}