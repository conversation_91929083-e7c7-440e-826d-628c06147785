{"ast": null, "code": "export { default as MAX } from './max.js';\nexport { default as NIL } from './nil.js';\nexport { default as parse } from './parse.js';\nexport { default as stringify } from './stringify.js';\nexport { default as v1 } from './v1.js';\nexport { default as v1ToV6 } from './v1ToV6.js';\nexport { default as v3 } from './v3.js';\nexport { default as v4 } from './v4.js';\nexport { default as v5 } from './v5.js';\nexport { default as v6 } from './v6.js';\nexport { default as v6ToV1 } from './v6ToV1.js';\nexport { default as v7 } from './v7.js';\nexport { default as validate } from './validate.js';\nexport { default as version } from './version.js';", "map": {"version": 3, "names": ["default", "MAX", "NIL", "parse", "stringify", "v1", "v1ToV6", "v3", "v4", "v5", "v6", "v6ToV1", "v7", "validate", "version"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/uuid/dist/esm-browser/index.js"], "sourcesContent": ["export { default as MAX } from './max.js';\nexport { default as NIL } from './nil.js';\nexport { default as parse } from './parse.js';\nexport { default as stringify } from './stringify.js';\nexport { default as v1 } from './v1.js';\nexport { default as v1ToV6 } from './v1ToV6.js';\nexport { default as v3 } from './v3.js';\nexport { default as v4 } from './v4.js';\nexport { default as v5 } from './v5.js';\nexport { default as v6 } from './v6.js';\nexport { default as v6ToV1 } from './v6ToV1.js';\nexport { default as v7 } from './v7.js';\nexport { default as validate } from './validate.js';\nexport { default as version } from './version.js';\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,GAAG,QAAQ,UAAU;AACzC,SAASD,OAAO,IAAIE,GAAG,QAAQ,UAAU;AACzC,SAASF,OAAO,IAAIG,KAAK,QAAQ,YAAY;AAC7C,SAASH,OAAO,IAAII,SAAS,QAAQ,gBAAgB;AACrD,SAASJ,OAAO,IAAIK,EAAE,QAAQ,SAAS;AACvC,SAASL,OAAO,IAAIM,MAAM,QAAQ,aAAa;AAC/C,SAASN,OAAO,IAAIO,EAAE,QAAQ,SAAS;AACvC,SAASP,OAAO,IAAIQ,EAAE,QAAQ,SAAS;AACvC,SAASR,OAAO,IAAIS,EAAE,QAAQ,SAAS;AACvC,SAAST,OAAO,IAAIU,EAAE,QAAQ,SAAS;AACvC,SAASV,OAAO,IAAIW,MAAM,QAAQ,aAAa;AAC/C,SAASX,OAAO,IAAIY,EAAE,QAAQ,SAAS;AACvC,SAASZ,OAAO,IAAIa,QAAQ,QAAQ,eAAe;AACnD,SAASb,OAAO,IAAIc,OAAO,QAAQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}