{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/contexts/ImageContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { v4 as uuidv4 } from 'uuid';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageContext = /*#__PURE__*/createContext(undefined);\nexport const useImageContext = () => {\n  _s();\n  const context = useContext(ImageContext);\n  if (!context) {\n    throw new Error('useImageContext must be used within an ImageProvider');\n  }\n  return context;\n};\n_s(useImageContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ImageProvider = ({\n  children\n}) => {\n  _s2();\n  const [collections, setCollections] = useState([]);\n  const [images, setImages] = useState([]);\n\n  // Load data from localStorage on mount\n  useEffect(() => {\n    const savedCollections = localStorage.getItem('image-annotator-collections');\n    const savedImages = localStorage.getItem('image-annotator-images');\n    if (savedCollections) {\n      setCollections(JSON.parse(savedCollections));\n    } else {\n      // Create default root collection\n      const rootCollection = {\n        id: 'root',\n        name: 'Root',\n        children: [],\n        images: [],\n        createdAt: new Date()\n      };\n      setCollections([rootCollection]);\n    }\n    if (savedImages) {\n      setImages(JSON.parse(savedImages));\n    }\n  }, []);\n\n  // Save to localStorage whenever data changes\n  useEffect(() => {\n    localStorage.setItem('image-annotator-collections', JSON.stringify(collections));\n  }, [collections]);\n  useEffect(() => {\n    localStorage.setItem('image-annotator-images', JSON.stringify(images));\n  }, [images]);\n  const addCollection = (name, parentId) => {\n    const newCollection = {\n      id: uuidv4(),\n      name,\n      parentId,\n      children: [],\n      images: [],\n      createdAt: new Date()\n    };\n    setCollections(prev => {\n      if (parentId) {\n        return updateCollectionInTree(prev, parentId, collection => ({\n          ...collection,\n          children: [...collection.children, newCollection]\n        }));\n      } else {\n        return [...prev, newCollection];\n      }\n    });\n  };\n  const addImage = async (file, collectionId) => {\n    return new Promise(resolve => {\n      const reader = new FileReader();\n      reader.onload = e => {\n        var _e$target;\n        const newImage = {\n          id: uuidv4(),\n          name: file.name,\n          url: (_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result,\n          annotations: [],\n          collectionId,\n          createdAt: new Date(),\n          updatedAt: new Date()\n        };\n        setImages(prev => [...prev, newImage]);\n        resolve();\n      };\n      reader.readAsDataURL(file);\n    });\n  };\n  const deleteImage = imageId => {\n    setImages(prev => prev.filter(img => img.id !== imageId));\n  };\n  const addAnnotation = (imageId, annotation) => {\n    const newAnnotation = {\n      ...annotation,\n      id: uuidv4()\n    };\n    setImages(prev => prev.map(img => img.id === imageId ? {\n      ...img,\n      annotations: [...img.annotations, newAnnotation],\n      updatedAt: new Date()\n    } : img));\n  };\n  const updateAnnotation = (imageId, annotationId, updates) => {\n    setImages(prev => prev.map(img => img.id === imageId ? {\n      ...img,\n      annotations: img.annotations.map(ann => ann.id === annotationId ? {\n        ...ann,\n        ...updates\n      } : ann),\n      updatedAt: new Date()\n    } : img));\n  };\n  const deleteAnnotation = (imageId, annotationId) => {\n    setImages(prev => prev.map(img => img.id === imageId ? {\n      ...img,\n      annotations: img.annotations.filter(ann => ann.id !== annotationId),\n      updatedAt: new Date()\n    } : img));\n  };\n  const getImageById = id => {\n    return images.find(img => img.id === id);\n  };\n  const getCollectionById = id => {\n    const findInTree = collections => {\n      for (const collection of collections) {\n        if (collection.id === id) return collection;\n        const found = findInTree(collection.children);\n        if (found) return found;\n      }\n      return undefined;\n    };\n    return findInTree(collections);\n  };\n\n  // Helper function to update collection in tree\n  const updateCollectionInTree = (collections, targetId, updater) => {\n    return collections.map(collection => {\n      if (collection.id === targetId) {\n        return updater(collection);\n      }\n      if (collection.children.length > 0) {\n        return {\n          ...collection,\n          children: updateCollectionInTree(collection.children, targetId, updater)\n        };\n      }\n      return collection;\n    });\n  };\n  const value = {\n    collections,\n    images,\n    addCollection,\n    addImage,\n    deleteImage,\n    addAnnotation,\n    updateAnnotation,\n    deleteAnnotation,\n    getImageById,\n    getCollectionById\n  };\n  return /*#__PURE__*/_jsxDEV(ImageContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 201,\n    columnNumber: 5\n  }, this);\n};\n_s2(ImageProvider, \"nDIG3MEa4BJhVYl30Ns8m9EdLZM=\");\n_c = ImageProvider;\nvar _c;\n$RefreshReg$(_c, \"ImageProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "v4", "uuidv4", "jsxDEV", "_jsxDEV", "ImageContext", "undefined", "useImageContext", "_s", "context", "Error", "ImageProvider", "children", "_s2", "collections", "setCollections", "images", "setImages", "savedCollections", "localStorage", "getItem", "savedImages", "JSON", "parse", "rootCollection", "id", "name", "createdAt", "Date", "setItem", "stringify", "addCollection", "parentId", "newCollection", "prev", "updateCollectionInTree", "collection", "addImage", "file", "collectionId", "Promise", "resolve", "reader", "FileReader", "onload", "e", "_e$target", "newImage", "url", "target", "result", "annotations", "updatedAt", "readAsDataURL", "deleteImage", "imageId", "filter", "img", "addAnnotation", "annotation", "newAnnotation", "map", "updateAnnotation", "annotationId", "updates", "ann", "deleteAnnotation", "getImageById", "find", "getCollectionById", "findInTree", "found", "targetId", "updater", "length", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/contexts/ImageContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { ImageData, Collection, Annotation } from '../types';\nimport { v4 as uuidv4 } from 'uuid';\n\ninterface ImageContextType {\n  collections: Collection[];\n  images: ImageData[];\n  addCollection: (name: string, parentId?: string) => void;\n  addImage: (file: File, collectionId: string) => Promise<void>;\n  deleteImage: (imageId: string) => void;\n  addAnnotation: (imageId: string, annotation: Omit<Annotation, 'id'>) => void;\n  updateAnnotation: (imageId: string, annotationId: string, updates: Partial<Annotation>) => void;\n  deleteAnnotation: (imageId: string, annotationId: string) => void;\n  getImageById: (id: string) => ImageData | undefined;\n  getCollectionById: (id: string) => Collection | undefined;\n}\n\nconst ImageContext = createContext<ImageContextType | undefined>(undefined);\n\nexport const useImageContext = () => {\n  const context = useContext(ImageContext);\n  if (!context) {\n    throw new Error('useImageContext must be used within an ImageProvider');\n  }\n  return context;\n};\n\nexport const ImageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [collections, setCollections] = useState<Collection[]>([]);\n  const [images, setImages] = useState<ImageData[]>([]);\n\n  // Load data from localStorage on mount\n  useEffect(() => {\n    const savedCollections = localStorage.getItem('image-annotator-collections');\n    const savedImages = localStorage.getItem('image-annotator-images');\n    \n    if (savedCollections) {\n      setCollections(JSON.parse(savedCollections));\n    } else {\n      // Create default root collection\n      const rootCollection: Collection = {\n        id: 'root',\n        name: 'Root',\n        children: [],\n        images: [],\n        createdAt: new Date(),\n      };\n      setCollections([rootCollection]);\n    }\n    \n    if (savedImages) {\n      setImages(JSON.parse(savedImages));\n    }\n  }, []);\n\n  // Save to localStorage whenever data changes\n  useEffect(() => {\n    localStorage.setItem('image-annotator-collections', JSON.stringify(collections));\n  }, [collections]);\n\n  useEffect(() => {\n    localStorage.setItem('image-annotator-images', JSON.stringify(images));\n  }, [images]);\n\n  const addCollection = (name: string, parentId?: string) => {\n    const newCollection: Collection = {\n      id: uuidv4(),\n      name,\n      parentId,\n      children: [],\n      images: [],\n      createdAt: new Date(),\n    };\n\n    setCollections(prev => {\n      if (parentId) {\n        return updateCollectionInTree(prev, parentId, (collection) => ({\n          ...collection,\n          children: [...collection.children, newCollection]\n        }));\n      } else {\n        return [...prev, newCollection];\n      }\n    });\n  };\n\n  const addImage = async (file: File, collectionId: string) => {\n    return new Promise<void>((resolve) => {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        const newImage: ImageData = {\n          id: uuidv4(),\n          name: file.name,\n          url: e.target?.result as string,\n          annotations: [],\n          collectionId,\n          createdAt: new Date(),\n          updatedAt: new Date(),\n        };\n\n        setImages(prev => [...prev, newImage]);\n        resolve();\n      };\n      reader.readAsDataURL(file);\n    });\n  };\n\n  const deleteImage = (imageId: string) => {\n    setImages(prev => prev.filter(img => img.id !== imageId));\n  };\n\n  const addAnnotation = (imageId: string, annotation: Omit<Annotation, 'id'>) => {\n    const newAnnotation: Annotation = {\n      ...annotation,\n      id: uuidv4(),\n    };\n\n    setImages(prev => prev.map(img => \n      img.id === imageId \n        ? { ...img, annotations: [...img.annotations, newAnnotation], updatedAt: new Date() }\n        : img\n    ));\n  };\n\n  const updateAnnotation = (imageId: string, annotationId: string, updates: Partial<Annotation>) => {\n    setImages(prev => prev.map(img => \n      img.id === imageId \n        ? { \n            ...img, \n            annotations: img.annotations.map(ann => \n              ann.id === annotationId ? { ...ann, ...updates } : ann\n            ),\n            updatedAt: new Date()\n          }\n        : img\n    ));\n  };\n\n  const deleteAnnotation = (imageId: string, annotationId: string) => {\n    setImages(prev => prev.map(img => \n      img.id === imageId \n        ? { \n            ...img, \n            annotations: img.annotations.filter(ann => ann.id !== annotationId),\n            updatedAt: new Date()\n          }\n        : img\n    ));\n  };\n\n  const getImageById = (id: string) => {\n    return images.find(img => img.id === id);\n  };\n\n  const getCollectionById = (id: string) => {\n    const findInTree = (collections: Collection[]): Collection | undefined => {\n      for (const collection of collections) {\n        if (collection.id === id) return collection;\n        const found = findInTree(collection.children);\n        if (found) return found;\n      }\n      return undefined;\n    };\n    return findInTree(collections);\n  };\n\n  // Helper function to update collection in tree\n  const updateCollectionInTree = (\n    collections: Collection[], \n    targetId: string, \n    updater: (collection: Collection) => Collection\n  ): Collection[] => {\n    return collections.map(collection => {\n      if (collection.id === targetId) {\n        return updater(collection);\n      }\n      if (collection.children.length > 0) {\n        return {\n          ...collection,\n          children: updateCollectionInTree(collection.children, targetId, updater)\n        };\n      }\n      return collection;\n    });\n  };\n\n  const value: ImageContextType = {\n    collections,\n    images,\n    addCollection,\n    addImage,\n    deleteImage,\n    addAnnotation,\n    updateAnnotation,\n    deleteAnnotation,\n    getImageById,\n    getCollectionById,\n  };\n\n  return (\n    <ImageContext.Provider value={value}>\n      {children}\n    </ImageContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAE7E,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAepC,MAAMC,YAAY,gBAAGR,aAAa,CAA+BS,SAAS,CAAC;AAE3E,OAAO,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,OAAO,GAAGX,UAAU,CAACO,YAAY,CAAC;EACxC,IAAI,CAACI,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,sDAAsD,CAAC;EACzE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,eAAe;AAQ5B,OAAO,MAAMI,aAAsD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACtF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACiB,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAc,EAAE,CAAC;;EAErD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,gBAAgB,GAAGC,YAAY,CAACC,OAAO,CAAC,6BAA6B,CAAC;IAC5E,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IAElE,IAAIF,gBAAgB,EAAE;MACpBH,cAAc,CAACO,IAAI,CAACC,KAAK,CAACL,gBAAgB,CAAC,CAAC;IAC9C,CAAC,MAAM;MACL;MACA,MAAMM,cAA0B,GAAG;QACjCC,EAAE,EAAE,MAAM;QACVC,IAAI,EAAE,MAAM;QACZd,QAAQ,EAAE,EAAE;QACZI,MAAM,EAAE,EAAE;QACVW,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MACDb,cAAc,CAAC,CAACS,cAAc,CAAC,CAAC;IAClC;IAEA,IAAIH,WAAW,EAAE;MACfJ,SAAS,CAACK,IAAI,CAACC,KAAK,CAACF,WAAW,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArB,SAAS,CAAC,MAAM;IACdmB,YAAY,CAACU,OAAO,CAAC,6BAA6B,EAAEP,IAAI,CAACQ,SAAS,CAAChB,WAAW,CAAC,CAAC;EAClF,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjBd,SAAS,CAAC,MAAM;IACdmB,YAAY,CAACU,OAAO,CAAC,wBAAwB,EAAEP,IAAI,CAACQ,SAAS,CAACd,MAAM,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAEZ,MAAMe,aAAa,GAAGA,CAACL,IAAY,EAAEM,QAAiB,KAAK;IACzD,MAAMC,aAAyB,GAAG;MAChCR,EAAE,EAAEvB,MAAM,CAAC,CAAC;MACZwB,IAAI;MACJM,QAAQ;MACRpB,QAAQ,EAAE,EAAE;MACZI,MAAM,EAAE,EAAE;MACVW,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDb,cAAc,CAACmB,IAAI,IAAI;MACrB,IAAIF,QAAQ,EAAE;QACZ,OAAOG,sBAAsB,CAACD,IAAI,EAAEF,QAAQ,EAAGI,UAAU,KAAM;UAC7D,GAAGA,UAAU;UACbxB,QAAQ,EAAE,CAAC,GAAGwB,UAAU,CAACxB,QAAQ,EAAEqB,aAAa;QAClD,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,OAAO,CAAC,GAAGC,IAAI,EAAED,aAAa,CAAC;MACjC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,QAAQ,GAAG,MAAAA,CAAOC,IAAU,EAAEC,YAAoB,KAAK;IAC3D,OAAO,IAAIC,OAAO,CAAQC,OAAO,IAAK;MACpC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;QAAA,IAAAC,SAAA;QACrB,MAAMC,QAAmB,GAAG;UAC1BtB,EAAE,EAAEvB,MAAM,CAAC,CAAC;UACZwB,IAAI,EAAEY,IAAI,CAACZ,IAAI;UACfsB,GAAG,GAAAF,SAAA,GAAED,CAAC,CAACI,MAAM,cAAAH,SAAA,uBAARA,SAAA,CAAUI,MAAgB;UAC/BC,WAAW,EAAE,EAAE;UACfZ,YAAY;UACZZ,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;UACrBwB,SAAS,EAAE,IAAIxB,IAAI,CAAC;QACtB,CAAC;QAEDX,SAAS,CAACiB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,QAAQ,CAAC,CAAC;QACtCN,OAAO,CAAC,CAAC;MACX,CAAC;MACDC,MAAM,CAACW,aAAa,CAACf,IAAI,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgB,WAAW,GAAIC,OAAe,IAAK;IACvCtC,SAAS,CAACiB,IAAI,IAAIA,IAAI,CAACsB,MAAM,CAACC,GAAG,IAAIA,GAAG,CAAChC,EAAE,KAAK8B,OAAO,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMG,aAAa,GAAGA,CAACH,OAAe,EAAEI,UAAkC,KAAK;IAC7E,MAAMC,aAAyB,GAAG;MAChC,GAAGD,UAAU;MACblC,EAAE,EAAEvB,MAAM,CAAC;IACb,CAAC;IAEDe,SAAS,CAACiB,IAAI,IAAIA,IAAI,CAAC2B,GAAG,CAACJ,GAAG,IAC5BA,GAAG,CAAChC,EAAE,KAAK8B,OAAO,GACd;MAAE,GAAGE,GAAG;MAAEN,WAAW,EAAE,CAAC,GAAGM,GAAG,CAACN,WAAW,EAAES,aAAa,CAAC;MAAER,SAAS,EAAE,IAAIxB,IAAI,CAAC;IAAE,CAAC,GACnF6B,GACN,CAAC,CAAC;EACJ,CAAC;EAED,MAAMK,gBAAgB,GAAGA,CAACP,OAAe,EAAEQ,YAAoB,EAAEC,OAA4B,KAAK;IAChG/C,SAAS,CAACiB,IAAI,IAAIA,IAAI,CAAC2B,GAAG,CAACJ,GAAG,IAC5BA,GAAG,CAAChC,EAAE,KAAK8B,OAAO,GACd;MACE,GAAGE,GAAG;MACNN,WAAW,EAAEM,GAAG,CAACN,WAAW,CAACU,GAAG,CAACI,GAAG,IAClCA,GAAG,CAACxC,EAAE,KAAKsC,YAAY,GAAG;QAAE,GAAGE,GAAG;QAAE,GAAGD;MAAQ,CAAC,GAAGC,GACrD,CAAC;MACDb,SAAS,EAAE,IAAIxB,IAAI,CAAC;IACtB,CAAC,GACD6B,GACN,CAAC,CAAC;EACJ,CAAC;EAED,MAAMS,gBAAgB,GAAGA,CAACX,OAAe,EAAEQ,YAAoB,KAAK;IAClE9C,SAAS,CAACiB,IAAI,IAAIA,IAAI,CAAC2B,GAAG,CAACJ,GAAG,IAC5BA,GAAG,CAAChC,EAAE,KAAK8B,OAAO,GACd;MACE,GAAGE,GAAG;MACNN,WAAW,EAAEM,GAAG,CAACN,WAAW,CAACK,MAAM,CAACS,GAAG,IAAIA,GAAG,CAACxC,EAAE,KAAKsC,YAAY,CAAC;MACnEX,SAAS,EAAE,IAAIxB,IAAI,CAAC;IACtB,CAAC,GACD6B,GACN,CAAC,CAAC;EACJ,CAAC;EAED,MAAMU,YAAY,GAAI1C,EAAU,IAAK;IACnC,OAAOT,MAAM,CAACoD,IAAI,CAACX,GAAG,IAAIA,GAAG,CAAChC,EAAE,KAAKA,EAAE,CAAC;EAC1C,CAAC;EAED,MAAM4C,iBAAiB,GAAI5C,EAAU,IAAK;IACxC,MAAM6C,UAAU,GAAIxD,WAAyB,IAA6B;MACxE,KAAK,MAAMsB,UAAU,IAAItB,WAAW,EAAE;QACpC,IAAIsB,UAAU,CAACX,EAAE,KAAKA,EAAE,EAAE,OAAOW,UAAU;QAC3C,MAAMmC,KAAK,GAAGD,UAAU,CAAClC,UAAU,CAACxB,QAAQ,CAAC;QAC7C,IAAI2D,KAAK,EAAE,OAAOA,KAAK;MACzB;MACA,OAAOjE,SAAS;IAClB,CAAC;IACD,OAAOgE,UAAU,CAACxD,WAAW,CAAC;EAChC,CAAC;;EAED;EACA,MAAMqB,sBAAsB,GAAGA,CAC7BrB,WAAyB,EACzB0D,QAAgB,EAChBC,OAA+C,KAC9B;IACjB,OAAO3D,WAAW,CAAC+C,GAAG,CAACzB,UAAU,IAAI;MACnC,IAAIA,UAAU,CAACX,EAAE,KAAK+C,QAAQ,EAAE;QAC9B,OAAOC,OAAO,CAACrC,UAAU,CAAC;MAC5B;MACA,IAAIA,UAAU,CAACxB,QAAQ,CAAC8D,MAAM,GAAG,CAAC,EAAE;QAClC,OAAO;UACL,GAAGtC,UAAU;UACbxB,QAAQ,EAAEuB,sBAAsB,CAACC,UAAU,CAACxB,QAAQ,EAAE4D,QAAQ,EAAEC,OAAO;QACzE,CAAC;MACH;MACA,OAAOrC,UAAU;IACnB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMuC,KAAuB,GAAG;IAC9B7D,WAAW;IACXE,MAAM;IACNe,aAAa;IACbM,QAAQ;IACRiB,WAAW;IACXI,aAAa;IACbI,gBAAgB;IAChBI,gBAAgB;IAChBC,YAAY;IACZE;EACF,CAAC;EAED,oBACEjE,OAAA,CAACC,YAAY,CAACuE,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA/D,QAAA,EACjCA;EAAQ;IAAAiE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;AAACnE,GAAA,CAjLWF,aAAsD;AAAAsE,EAAA,GAAtDtE,aAAsD;AAAA,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}