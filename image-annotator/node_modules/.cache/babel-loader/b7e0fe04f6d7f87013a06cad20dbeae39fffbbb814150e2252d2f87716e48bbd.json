{"ast": null, "code": "/**\n * @typedef {import('./lib/index.js').AllowElement} AllowElement\n * @typedef {import('./lib/index.js').Components} Components\n * @typedef {import('./lib/index.js').ExtraProps} ExtraProps\n * @typedef {import('./lib/index.js').HooksOptions} HooksOptions\n * @typedef {import('./lib/index.js').Options} Options\n * @typedef {import('./lib/index.js').UrlTransform} UrlTransform\n */\n\nexport { MarkdownAsync, MarkdownHooks, Markdown as default, defaultUrlTransform } from './lib/index.js';", "map": {"version": 3, "names": ["<PERSON>downAsync", "MarkdownHook<PERSON>", "<PERSON><PERSON>", "default", "defaultUrlTransform"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/react-markdown/index.js"], "sourcesContent": ["/**\n * @typedef {import('./lib/index.js').AllowElement} AllowElement\n * @typedef {import('./lib/index.js').Components} Components\n * @typedef {import('./lib/index.js').ExtraProps} ExtraProps\n * @typedef {import('./lib/index.js').HooksOptions} HooksOptions\n * @typedef {import('./lib/index.js').Options} Options\n * @typedef {import('./lib/index.js').UrlTransform} UrlTransform\n */\n\nexport {\n  MarkdownAsync,\n  MarkdownHooks,\n  Markdown as default,\n  defaultUrlTransform\n} from './lib/index.js'\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SACEA,aAAa,EACbC,aAAa,EACbC,QAAQ,IAAIC,OAAO,EACnBC,mBAAmB,QACd,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}