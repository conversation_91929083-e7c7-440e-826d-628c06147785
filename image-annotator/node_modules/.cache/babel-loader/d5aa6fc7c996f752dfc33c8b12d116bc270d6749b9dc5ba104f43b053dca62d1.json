{"ast": null, "code": "/**\n * Parse space-separated tokens to an array of strings.\n *\n * @param {string} value\n *   Space-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\nexport function parse(value) {\n  const input = String(value || '').trim();\n  return input ? input.split(/[ \\t\\n\\r\\f]+/g) : [];\n}\n\n/**\n * Serialize an array of strings as space separated-tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @returns {string}\n *   Space-separated tokens.\n */\nexport function stringify(values) {\n  return values.join(' ').trim();\n}", "map": {"version": 3, "names": ["parse", "value", "input", "String", "trim", "split", "stringify", "values", "join"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/space-separated-tokens/index.js"], "sourcesContent": ["/**\n * Parse space-separated tokens to an array of strings.\n *\n * @param {string} value\n *   Space-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\nexport function parse(value) {\n  const input = String(value || '').trim()\n  return input ? input.split(/[ \\t\\n\\r\\f]+/g) : []\n}\n\n/**\n * Serialize an array of strings as space separated-tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @returns {string}\n *   Space-separated tokens.\n */\nexport function stringify(values) {\n  return values.join(' ').trim()\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,KAAKA,CAACC,KAAK,EAAE;EAC3B,MAAMC,KAAK,GAAGC,MAAM,CAACF,KAAK,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC;EACxC,OAAOF,KAAK,GAAGA,KAAK,CAACG,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,MAAM,EAAE;EAChC,OAAOA,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}