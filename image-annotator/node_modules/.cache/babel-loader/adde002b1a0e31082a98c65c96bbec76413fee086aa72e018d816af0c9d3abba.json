{"ast": null, "code": "export const VOID = -1;\nexport const PRIMITIVE = 0;\nexport const ARRAY = 1;\nexport const OBJECT = 2;\nexport const DATE = 3;\nexport const REGEXP = 4;\nexport const MAP = 5;\nexport const SET = 6;\nexport const ERROR = 7;\nexport const BIGINT = 8;\n// export const SYMBOL = 9;", "map": {"version": 3, "names": ["VOID", "PRIMITIVE", "ARRAY", "OBJECT", "DATE", "REGEXP", "MAP", "SET", "ERROR", "BIGINT"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/@ungap/structured-clone/esm/types.js"], "sourcesContent": ["export const VOID       = -1;\nexport const PRIMITIVE  = 0;\nexport const ARRAY      = 1;\nexport const OBJECT     = 2;\nexport const DATE       = 3;\nexport const REGEXP     = 4;\nexport const MAP        = 5;\nexport const SET        = 6;\nexport const ERROR      = 7;\nexport const BIGINT     = 8;\n// export const SYMBOL = 9;\n"], "mappings": "AAAA,OAAO,MAAMA,IAAI,GAAS,CAAC,CAAC;AAC5B,OAAO,MAAMC,SAAS,GAAI,CAAC;AAC3B,OAAO,MAAMC,KAAK,GAAQ,CAAC;AAC3B,OAAO,MAAMC,MAAM,GAAO,CAAC;AAC3B,OAAO,MAAMC,IAAI,GAAS,CAAC;AAC3B,OAAO,MAAMC,MAAM,GAAO,CAAC;AAC3B,OAAO,MAAMC,GAAG,GAAU,CAAC;AAC3B,OAAO,MAAMC,GAAG,GAAU,CAAC;AAC3B,OAAO,MAAMC,KAAK,GAAQ,CAAC;AAC3B,OAAO,MAAMC,MAAM,GAAO,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}