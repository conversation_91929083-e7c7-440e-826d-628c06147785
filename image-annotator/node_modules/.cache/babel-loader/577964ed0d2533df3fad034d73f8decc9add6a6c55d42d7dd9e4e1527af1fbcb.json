{"ast": null, "code": "import { create } from './util/create.js';\nimport { caseInsensitiveTransform } from './util/case-insensitive-transform.js';\nexport const xmlns = create({\n  attributes: {\n    xmlnsxlink: 'xmlns:xlink'\n  },\n  properties: {\n    xmlnsXLink: null,\n    xmlns: null\n  },\n  space: 'xmlns',\n  transform: caseInsensitiveTransform\n});", "map": {"version": 3, "names": ["create", "caseInsensitiveTransform", "xmlns", "attributes", "xmlnsxlink", "properties", "xmlnsXLink", "space", "transform"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/property-information/lib/xmlns.js"], "sourcesContent": ["import {create} from './util/create.js'\nimport {caseInsensitiveTransform} from './util/case-insensitive-transform.js'\n\nexport const xmlns = create({\n  attributes: {xmlnsxlink: 'xmlns:xlink'},\n  properties: {xmlnsXLink: null, xmlns: null},\n  space: 'xmlns',\n  transform: caseInsensitiveTransform\n})\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,kBAAkB;AACvC,SAAQC,wBAAwB,QAAO,sCAAsC;AAE7E,OAAO,MAAMC,KAAK,GAAGF,MAAM,CAAC;EAC1BG,UAAU,EAAE;IAACC,UAAU,EAAE;EAAa,CAAC;EACvCC,UAAU,EAAE;IAACC,UAAU,EAAE,IAAI;IAAEJ,KAAK,EAAE;EAAI,CAAC;EAC3CK,KAAK,EAAE,OAAO;EACdC,SAAS,EAAEP;AACb,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}