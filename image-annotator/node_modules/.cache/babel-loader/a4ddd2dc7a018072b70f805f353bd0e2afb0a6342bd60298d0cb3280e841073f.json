{"ast": null, "code": "/**\n * @typedef {import('./lib/index.js').Options} Options\n */\n\nexport { cont, name, start } from './lib/index.js';", "map": {"version": 3, "names": ["cont", "name", "start"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/estree-util-is-identifier-name/index.js"], "sourcesContent": ["/**\n * @typedef {import('./lib/index.js').Options} Options\n */\n\nexport {cont, name, start} from './lib/index.js'\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAAQA,IAAI,EAAEC,IAAI,EAAEC,KAAK,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}