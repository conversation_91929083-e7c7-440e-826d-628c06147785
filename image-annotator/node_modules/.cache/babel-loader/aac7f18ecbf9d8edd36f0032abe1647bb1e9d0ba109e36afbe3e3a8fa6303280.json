{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = StyleToObject;\nvar inline_style_parser_1 = __importDefault(require(\"inline-style-parser\"));\n/**\n * Parses inline style to object.\n *\n * @param style - Inline style.\n * @param iterator - Iterator.\n * @returns - Style object or null.\n *\n * @example Parsing inline style to object:\n *\n * ```js\n * import parse from 'style-to-object';\n * parse('line-height: 42;'); // { 'line-height': '42' }\n * ```\n */\nfunction StyleToObject(style, iterator) {\n  var styleObject = null;\n  if (!style || typeof style !== 'string') {\n    return styleObject;\n  }\n  var declarations = (0, inline_style_parser_1.default)(style);\n  var hasIterator = typeof iterator === 'function';\n  declarations.forEach(function (declaration) {\n    if (declaration.type !== 'declaration') {\n      return;\n    }\n    var property = declaration.property,\n      value = declaration.value;\n    if (hasIterator) {\n      iterator(property, value, declaration);\n    } else if (value) {\n      styleObject = styleObject || {};\n      styleObject[property] = value;\n    }\n  });\n  return styleObject;\n}", "map": {"version": 3, "names": ["exports", "default", "StyleToObject", "inline_style_parser_1", "__importDefault", "require", "style", "iterator", "styleObject", "declarations", "hasIterator", "for<PERSON>ach", "declaration", "type", "property", "value"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/style-to-object/src/index.ts"], "sourcesContent": ["import type { Declaration } from 'inline-style-parser';\nimport parse from 'inline-style-parser';\n\nexport { Declaration };\n\ninterface StyleObject {\n  [name: string]: string;\n}\n\ntype Iterator = (\n  property: string,\n  value: string,\n  declaration: Declaration,\n) => void;\n\n/**\n * Parses inline style to object.\n *\n * @param style - Inline style.\n * @param iterator - Iterator.\n * @returns - Style object or null.\n *\n * @example Parsing inline style to object:\n *\n * ```js\n * import parse from 'style-to-object';\n * parse('line-height: 42;'); // { 'line-height': '42' }\n * ```\n */\nexport default function StyleToObject(\n  style: string,\n  iterator?: Iterator,\n): StyleObject | null {\n  let styleObject: StyleObject | null = null;\n\n  if (!style || typeof style !== 'string') {\n    return styleObject;\n  }\n\n  const declarations = parse(style);\n  const hasIterator = typeof iterator === 'function';\n\n  declarations.forEach((declaration) => {\n    if (declaration.type !== 'declaration') {\n      return;\n    }\n\n    const { property, value } = declaration;\n\n    if (hasIterator) {\n      iterator(property, value, declaration);\n    } else if (value) {\n      styleObject = styleObject || {};\n      styleObject[property] = value;\n    }\n  });\n\n  return styleObject;\n}\n"], "mappings": ";;;;;;;;;;AA6BAA,OAAA,CAAAC,OAAA,GAAAC,aAAA;AA5BA,IAAAC,qBAAA,GAAAC,eAAA,CAAAC,OAAA;AAcA;;;;;;;;;;;;;;AAcA,SAAwBH,aAAaA,CACnCI,KAAa,EACbC,QAAmB;EAEnB,IAAIC,WAAW,GAAuB,IAAI;EAE1C,IAAI,CAACF,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACvC,OAAOE,WAAW;EACpB;EAEA,IAAMC,YAAY,GAAG,IAAAN,qBAAA,CAAAF,OAAK,EAACK,KAAK,CAAC;EACjC,IAAMI,WAAW,GAAG,OAAOH,QAAQ,KAAK,UAAU;EAElDE,YAAY,CAACE,OAAO,CAAC,UAACC,WAAW;IAC/B,IAAIA,WAAW,CAACC,IAAI,KAAK,aAAa,EAAE;MACtC;IACF;IAEQ,IAAAC,QAAQ,GAAYF,WAAW,CAAAE,QAAvB;MAAEC,KAAK,GAAKH,WAAW,CAAAG,KAAhB;IAEvB,IAAIL,WAAW,EAAE;MACfH,QAAQ,CAACO,QAAQ,EAAEC,KAAK,EAAEH,WAAW,CAAC;IACxC,CAAC,MAAM,IAAIG,KAAK,EAAE;MAChBP,WAAW,GAAGA,WAAW,IAAI,EAAE;MAC/BA,WAAW,CAACM,QAAQ,CAAC,GAAGC,KAAK;IAC/B;EACF,CAAC,CAAC;EAEF,OAAOP,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}