{"ast": null, "code": "import { isUrl } from './minurl.shared.js';\nexport { isUrl } from './minurl.shared.js';\n\n// See: <https://github.com/nodejs/node/blob/6a3403c/lib/internal/url.js>\n\n/**\n * @param {URL | string} path\n *   File URL.\n * @returns {string}\n *   File URL.\n */\nexport function urlToPath(path) {\n  if (typeof path === 'string') {\n    path = new URL(path);\n  } else if (!isUrl(path)) {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError('The \"path\" argument must be of type string or an instance of URL. Received `' + path + '`');\n    error.code = 'ERR_INVALID_ARG_TYPE';\n    throw error;\n  }\n  if (path.protocol !== 'file:') {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError('The URL must be of scheme file');\n    error.code = 'ERR_INVALID_URL_SCHEME';\n    throw error;\n  }\n  return getPathFromURLPosix(path);\n}\n\n/**\n * Get a path from a POSIX URL.\n *\n * @param {URL} url\n *   URL.\n * @returns {string}\n *   File path.\n */\nfunction getPathFromURLPosix(url) {\n  if (url.hostname !== '') {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError('File URL host must be \"localhost\" or empty on darwin');\n    error.code = 'ERR_INVALID_FILE_URL_HOST';\n    throw error;\n  }\n  const pathname = url.pathname;\n  let index = -1;\n  while (++index < pathname.length) {\n    if (pathname.codePointAt(index) === 37 /* `%` */ && pathname.codePointAt(index + 1) === 50 /* `2` */) {\n      const third = pathname.codePointAt(index + 2);\n      if (third === 70 /* `F` */ || third === 102 /* `f` */) {\n        /** @type {NodeJS.ErrnoException} */\n        const error = new TypeError('File URL path must not include encoded / characters');\n        error.code = 'ERR_INVALID_FILE_URL_PATH';\n        throw error;\n      }\n    }\n  }\n  return decodeURIComponent(pathname);\n}", "map": {"version": 3, "names": ["isUrl", "urlToPath", "path", "URL", "error", "TypeError", "code", "protocol", "getPathFromURLPosix", "url", "hostname", "pathname", "index", "length", "codePointAt", "third", "decodeURIComponent"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/vfile/lib/minurl.browser.js"], "sourcesContent": ["import {isUrl} from './minurl.shared.js'\n\nexport {isUrl} from './minurl.shared.js'\n\n// See: <https://github.com/nodejs/node/blob/6a3403c/lib/internal/url.js>\n\n/**\n * @param {URL | string} path\n *   File URL.\n * @returns {string}\n *   File URL.\n */\nexport function urlToPath(path) {\n  if (typeof path === 'string') {\n    path = new URL(path)\n  } else if (!isUrl(path)) {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError(\n      'The \"path\" argument must be of type string or an instance of URL. Received `' +\n        path +\n        '`'\n    )\n    error.code = 'ERR_INVALID_ARG_TYPE'\n    throw error\n  }\n\n  if (path.protocol !== 'file:') {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError('The URL must be of scheme file')\n    error.code = 'ERR_INVALID_URL_SCHEME'\n    throw error\n  }\n\n  return getPathFromURLPosix(path)\n}\n\n/**\n * Get a path from a POSIX URL.\n *\n * @param {URL} url\n *   URL.\n * @returns {string}\n *   File path.\n */\nfunction getPathFromURLPosix(url) {\n  if (url.hostname !== '') {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError(\n      'File URL host must be \"localhost\" or empty on darwin'\n    )\n    error.code = 'ERR_INVALID_FILE_URL_HOST'\n    throw error\n  }\n\n  const pathname = url.pathname\n  let index = -1\n\n  while (++index < pathname.length) {\n    if (\n      pathname.codePointAt(index) === 37 /* `%` */ &&\n      pathname.codePointAt(index + 1) === 50 /* `2` */\n    ) {\n      const third = pathname.codePointAt(index + 2)\n      if (third === 70 /* `F` */ || third === 102 /* `f` */) {\n        /** @type {NodeJS.ErrnoException} */\n        const error = new TypeError(\n          'File URL path must not include encoded / characters'\n        )\n        error.code = 'ERR_INVALID_FILE_URL_PATH'\n        throw error\n      }\n    }\n  }\n\n  return decodeURIComponent(pathname)\n}\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,oBAAoB;AAExC,SAAQA,KAAK,QAAO,oBAAoB;;AAExC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAE;EAC9B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5BA,IAAI,GAAG,IAAIC,GAAG,CAACD,IAAI,CAAC;EACtB,CAAC,MAAM,IAAI,CAACF,KAAK,CAACE,IAAI,CAAC,EAAE;IACvB;IACA,MAAME,KAAK,GAAG,IAAIC,SAAS,CACzB,8EAA8E,GAC5EH,IAAI,GACJ,GACJ,CAAC;IACDE,KAAK,CAACE,IAAI,GAAG,sBAAsB;IACnC,MAAMF,KAAK;EACb;EAEA,IAAIF,IAAI,CAACK,QAAQ,KAAK,OAAO,EAAE;IAC7B;IACA,MAAMH,KAAK,GAAG,IAAIC,SAAS,CAAC,gCAAgC,CAAC;IAC7DD,KAAK,CAACE,IAAI,GAAG,wBAAwB;IACrC,MAAMF,KAAK;EACb;EAEA,OAAOI,mBAAmB,CAACN,IAAI,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,mBAAmBA,CAACC,GAAG,EAAE;EAChC,IAAIA,GAAG,CAACC,QAAQ,KAAK,EAAE,EAAE;IACvB;IACA,MAAMN,KAAK,GAAG,IAAIC,SAAS,CACzB,sDACF,CAAC;IACDD,KAAK,CAACE,IAAI,GAAG,2BAA2B;IACxC,MAAMF,KAAK;EACb;EAEA,MAAMO,QAAQ,GAAGF,GAAG,CAACE,QAAQ;EAC7B,IAAIC,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAGD,QAAQ,CAACE,MAAM,EAAE;IAChC,IACEF,QAAQ,CAACG,WAAW,CAACF,KAAK,CAAC,KAAK,EAAE,CAAC,aACnCD,QAAQ,CAACG,WAAW,CAACF,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,WACvC;MACA,MAAMG,KAAK,GAAGJ,QAAQ,CAACG,WAAW,CAACF,KAAK,GAAG,CAAC,CAAC;MAC7C,IAAIG,KAAK,KAAK,EAAE,CAAC,aAAaA,KAAK,KAAK,GAAG,CAAC,WAAW;QACrD;QACA,MAAMX,KAAK,GAAG,IAAIC,SAAS,CACzB,qDACF,CAAC;QACDD,KAAK,CAACE,IAAI,GAAG,2BAA2B;QACxC,MAAMF,KAAK;MACb;IACF;EACF;EAEA,OAAOY,kBAAkB,CAACL,QAAQ,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}