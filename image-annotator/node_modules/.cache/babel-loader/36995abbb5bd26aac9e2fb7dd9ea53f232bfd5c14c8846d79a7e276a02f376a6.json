{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Table} Table\n * @typedef {import('../state.js').State} State\n */\n\nimport { pointEnd, pointStart } from 'unist-util-position';\n\n/**\n * Turn an mdast `table` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Table} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function table(state, node) {\n  const rows = state.all(node);\n  const firstRow = rows.shift();\n  /** @type {Array<Element>} */\n  const tableContent = [];\n  if (firstRow) {\n    /** @type {Element} */\n    const head = {\n      type: 'element',\n      tagName: 'thead',\n      properties: {},\n      children: state.wrap([firstRow], true)\n    };\n    state.patch(node.children[0], head);\n    tableContent.push(head);\n  }\n  if (rows.length > 0) {\n    /** @type {Element} */\n    const body = {\n      type: 'element',\n      tagName: 'tbody',\n      properties: {},\n      children: state.wrap(rows, true)\n    };\n    const start = pointStart(node.children[1]);\n    const end = pointEnd(node.children[node.children.length - 1]);\n    if (start && end) body.position = {\n      start,\n      end\n    };\n    tableContent.push(body);\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'table',\n    properties: {},\n    children: state.wrap(tableContent, true)\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["pointEnd", "pointStart", "table", "state", "node", "rows", "all", "firstRow", "shift", "tableContent", "head", "type", "tagName", "properties", "children", "wrap", "patch", "push", "length", "body", "start", "end", "position", "result", "applyData"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/mdast-util-to-hast/lib/handlers/table.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Table} Table\n * @typedef {import('../state.js').State} State\n */\n\nimport {pointEnd, pointStart} from 'unist-util-position'\n\n/**\n * Turn an mdast `table` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Table} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function table(state, node) {\n  const rows = state.all(node)\n  const firstRow = rows.shift()\n  /** @type {Array<Element>} */\n  const tableContent = []\n\n  if (firstRow) {\n    /** @type {Element} */\n    const head = {\n      type: 'element',\n      tagName: 'thead',\n      properties: {},\n      children: state.wrap([firstRow], true)\n    }\n    state.patch(node.children[0], head)\n    tableContent.push(head)\n  }\n\n  if (rows.length > 0) {\n    /** @type {Element} */\n    const body = {\n      type: 'element',\n      tagName: 'tbody',\n      properties: {},\n      children: state.wrap(rows, true)\n    }\n\n    const start = pointStart(node.children[1])\n    const end = pointEnd(node.children[node.children.length - 1])\n    if (start && end) body.position = {start, end}\n    tableContent.push(body)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'table',\n    properties: {},\n    children: state.wrap(tableContent, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,QAAQ,EAAEC,UAAU,QAAO,qBAAqB;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACjC,MAAMC,IAAI,GAAGF,KAAK,CAACG,GAAG,CAACF,IAAI,CAAC;EAC5B,MAAMG,QAAQ,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC;EAC7B;EACA,MAAMC,YAAY,GAAG,EAAE;EAEvB,IAAIF,QAAQ,EAAE;IACZ;IACA,MAAMG,IAAI,GAAG;MACXC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,OAAO;MAChBC,UAAU,EAAE,CAAC,CAAC;MACdC,QAAQ,EAAEX,KAAK,CAACY,IAAI,CAAC,CAACR,QAAQ,CAAC,EAAE,IAAI;IACvC,CAAC;IACDJ,KAAK,CAACa,KAAK,CAACZ,IAAI,CAACU,QAAQ,CAAC,CAAC,CAAC,EAAEJ,IAAI,CAAC;IACnCD,YAAY,CAACQ,IAAI,CAACP,IAAI,CAAC;EACzB;EAEA,IAAIL,IAAI,CAACa,MAAM,GAAG,CAAC,EAAE;IACnB;IACA,MAAMC,IAAI,GAAG;MACXR,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,OAAO;MAChBC,UAAU,EAAE,CAAC,CAAC;MACdC,QAAQ,EAAEX,KAAK,CAACY,IAAI,CAACV,IAAI,EAAE,IAAI;IACjC,CAAC;IAED,MAAMe,KAAK,GAAGnB,UAAU,CAACG,IAAI,CAACU,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1C,MAAMO,GAAG,GAAGrB,QAAQ,CAACI,IAAI,CAACU,QAAQ,CAACV,IAAI,CAACU,QAAQ,CAACI,MAAM,GAAG,CAAC,CAAC,CAAC;IAC7D,IAAIE,KAAK,IAAIC,GAAG,EAAEF,IAAI,CAACG,QAAQ,GAAG;MAACF,KAAK;MAAEC;IAAG,CAAC;IAC9CZ,YAAY,CAACQ,IAAI,CAACE,IAAI,CAAC;EACzB;;EAEA;EACA,MAAMI,MAAM,GAAG;IACbZ,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,OAAO;IAChBC,UAAU,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAEX,KAAK,CAACY,IAAI,CAACN,YAAY,EAAE,IAAI;EACzC,CAAC;EACDN,KAAK,CAACa,KAAK,CAACZ,IAAI,EAAEmB,MAAM,CAAC;EACzB,OAAOpB,KAAK,CAACqB,SAAS,CAACpB,IAAI,EAAEmB,MAAM,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}