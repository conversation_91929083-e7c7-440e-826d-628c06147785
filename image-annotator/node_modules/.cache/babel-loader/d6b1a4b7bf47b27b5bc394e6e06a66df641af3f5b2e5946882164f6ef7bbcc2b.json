{"ast": null, "code": "import { create } from './util/create.js';\nexport const xlink = create({\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  },\n  space: 'xlink',\n  transform(_, property) {\n    return 'xlink:' + property.slice(5).toLowerCase();\n  }\n});", "map": {"version": 3, "names": ["create", "xlink", "properties", "xLinkActuate", "xLinkArcRole", "xLinkHref", "xLinkRole", "xLinkShow", "xLinkTitle", "xLinkType", "space", "transform", "_", "property", "slice", "toLowerCase"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/property-information/lib/xlink.js"], "sourcesContent": ["import {create} from './util/create.js'\n\nexport const xlink = create({\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  },\n  space: 'xlink',\n  transform(_, property) {\n    return 'xlink:' + property.slice(5).toLowerCase()\n  }\n})\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,kBAAkB;AAEvC,OAAO,MAAMC,KAAK,GAAGD,MAAM,CAAC;EAC1BE,UAAU,EAAE;IACVC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE;EACb,CAAC;EACDC,KAAK,EAAE,OAAO;EACdC,SAASA,CAACC,CAAC,EAAEC,QAAQ,EAAE;IACrB,OAAO,QAAQ,GAAGA,QAAQ,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACnD;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}