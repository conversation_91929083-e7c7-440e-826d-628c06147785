{"ast": null, "code": "import { unsafeStringify } from './stringify.js';\nimport v1 from './v1.js';\nimport v1ToV6 from './v1ToV6.js';\nfunction v6(options, buf, offset) {\n  options ??= {};\n  offset ??= 0;\n  let bytes = v1({\n    ...options,\n    _v6: true\n  }, new Uint8Array(16));\n  bytes = v1ToV6(bytes);\n  if (buf) {\n    for (let i = 0; i < 16; i++) {\n      buf[offset + i] = bytes[i];\n    }\n    return buf;\n  }\n  return unsafeStringify(bytes);\n}\nexport default v6;", "map": {"version": 3, "names": ["unsafeStringify", "v1", "v1ToV6", "v6", "options", "buf", "offset", "bytes", "_v6", "Uint8Array", "i"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/uuid/dist/esm-browser/v6.js"], "sourcesContent": ["import { unsafeStringify } from './stringify.js';\nimport v1 from './v1.js';\nimport v1ToV6 from './v1ToV6.js';\nfunction v6(options, buf, offset) {\n    options ??= {};\n    offset ??= 0;\n    let bytes = v1({ ...options, _v6: true }, new Uint8Array(16));\n    bytes = v1ToV6(bytes);\n    if (buf) {\n        for (let i = 0; i < 16; i++) {\n            buf[offset + i] = bytes[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(bytes);\n}\nexport default v6;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,EAAE,MAAM,SAAS;AACxB,OAAOC,MAAM,MAAM,aAAa;AAChC,SAASC,EAAEA,CAACC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAC9BF,OAAO,KAAK,CAAC,CAAC;EACdE,MAAM,KAAK,CAAC;EACZ,IAAIC,KAAK,GAAGN,EAAE,CAAC;IAAE,GAAGG,OAAO;IAAEI,GAAG,EAAE;EAAK,CAAC,EAAE,IAAIC,UAAU,CAAC,EAAE,CAAC,CAAC;EAC7DF,KAAK,GAAGL,MAAM,CAACK,KAAK,CAAC;EACrB,IAAIF,GAAG,EAAE;IACL,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACzBL,GAAG,CAACC,MAAM,GAAGI,CAAC,CAAC,GAAGH,KAAK,CAACG,CAAC,CAAC;IAC9B;IACA,OAAOL,GAAG;EACd;EACA,OAAOL,eAAe,CAACO,KAAK,CAAC;AACjC;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}