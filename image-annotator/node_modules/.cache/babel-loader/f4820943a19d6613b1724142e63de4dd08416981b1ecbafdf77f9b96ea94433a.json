{"ast": null, "code": "import { constants } from 'micromark-util-symbol';\n\n/**\n * Like `Array#splice`, but smarter for giant arrays.\n *\n * `Array#splice` takes all items to be inserted as individual argument which\n * causes a stack overflow in V8 when trying to insert 100k items for instance.\n *\n * Otherwise, this does not return the removed items, and takes `items` as an\n * array instead of rest parameters.\n *\n * @template {unknown} T\n *   Item type.\n * @param {Array<T>} list\n *   List to operate on.\n * @param {number} start\n *   Index to remove/insert at (can be negative).\n * @param {number} remove\n *   Number of items to remove.\n * @param {Array<T>} items\n *   Items to inject into `list`.\n * @returns {undefined}\n *   Nothing.\n */\nexport function splice(list, start, remove, items) {\n  const end = list.length;\n  let chunkStart = 0;\n  /** @type {Array<unknown>} */\n  let parameters;\n\n  // Make start between zero and `end` (included).\n  if (start < 0) {\n    start = -start > end ? 0 : end + start;\n  } else {\n    start = start > end ? end : start;\n  }\n  remove = remove > 0 ? remove : 0;\n\n  // No need to chunk the items if there’s only a couple (10k) items.\n  if (items.length < constants.v8MaxSafeChunkSize) {\n    parameters = Array.from(items);\n    parameters.unshift(start, remove);\n    // @ts-expect-error Hush, it’s fine.\n    list.splice(...parameters);\n  } else {\n    // Delete `remove` items starting from `start`\n    if (remove) list.splice(start, remove);\n\n    // Insert the items in chunks to not cause stack overflows.\n    while (chunkStart < items.length) {\n      parameters = items.slice(chunkStart, chunkStart + constants.v8MaxSafeChunkSize);\n      parameters.unshift(start, 0);\n      // @ts-expect-error Hush, it’s fine.\n      list.splice(...parameters);\n      chunkStart += constants.v8MaxSafeChunkSize;\n      start += constants.v8MaxSafeChunkSize;\n    }\n  }\n}\n\n/**\n * Append `items` (an array) at the end of `list` (another array).\n * When `list` was empty, returns `items` instead.\n *\n * This prevents a potentially expensive operation when `list` is empty,\n * and adds items in batches to prevent V8 from hanging.\n *\n * @template {unknown} T\n *   Item type.\n * @param {Array<T>} list\n *   List to operate on.\n * @param {Array<T>} items\n *   Items to add to `list`.\n * @returns {Array<T>}\n *   Either `list` or `items`.\n */\nexport function push(list, items) {\n  if (list.length > 0) {\n    splice(list, list.length, 0, items);\n    return list;\n  }\n  return items;\n}", "map": {"version": 3, "names": ["constants", "splice", "list", "start", "remove", "items", "end", "length", "chunkStart", "parameters", "v8MaxSafeChunkSize", "Array", "from", "unshift", "slice", "push"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark-util-chunked/dev/index.js"], "sourcesContent": ["import {constants} from 'micromark-util-symbol'\n\n/**\n * Like `Array#splice`, but smarter for giant arrays.\n *\n * `Array#splice` takes all items to be inserted as individual argument which\n * causes a stack overflow in V8 when trying to insert 100k items for instance.\n *\n * Otherwise, this does not return the removed items, and takes `items` as an\n * array instead of rest parameters.\n *\n * @template {unknown} T\n *   Item type.\n * @param {Array<T>} list\n *   List to operate on.\n * @param {number} start\n *   Index to remove/insert at (can be negative).\n * @param {number} remove\n *   Number of items to remove.\n * @param {Array<T>} items\n *   Items to inject into `list`.\n * @returns {undefined}\n *   Nothing.\n */\nexport function splice(list, start, remove, items) {\n  const end = list.length\n  let chunkStart = 0\n  /** @type {Array<unknown>} */\n  let parameters\n\n  // Make start between zero and `end` (included).\n  if (start < 0) {\n    start = -start > end ? 0 : end + start\n  } else {\n    start = start > end ? end : start\n  }\n\n  remove = remove > 0 ? remove : 0\n\n  // No need to chunk the items if there’s only a couple (10k) items.\n  if (items.length < constants.v8MaxSafeChunkSize) {\n    parameters = Array.from(items)\n    parameters.unshift(start, remove)\n    // @ts-expect-error Hush, it’s fine.\n    list.splice(...parameters)\n  } else {\n    // Delete `remove` items starting from `start`\n    if (remove) list.splice(start, remove)\n\n    // Insert the items in chunks to not cause stack overflows.\n    while (chunkStart < items.length) {\n      parameters = items.slice(\n        chunkStart,\n        chunkStart + constants.v8MaxSafeChunkSize\n      )\n      parameters.unshift(start, 0)\n      // @ts-expect-error Hush, it’s fine.\n      list.splice(...parameters)\n\n      chunkStart += constants.v8MaxSafeChunkSize\n      start += constants.v8MaxSafeChunkSize\n    }\n  }\n}\n\n/**\n * Append `items` (an array) at the end of `list` (another array).\n * When `list` was empty, returns `items` instead.\n *\n * This prevents a potentially expensive operation when `list` is empty,\n * and adds items in batches to prevent V8 from hanging.\n *\n * @template {unknown} T\n *   Item type.\n * @param {Array<T>} list\n *   List to operate on.\n * @param {Array<T>} items\n *   Items to add to `list`.\n * @returns {Array<T>}\n *   Either `list` or `items`.\n */\nexport function push(list, items) {\n  if (list.length > 0) {\n    splice(list, list.length, 0, items)\n    return list\n  }\n\n  return items\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,uBAAuB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAE;EACjD,MAAMC,GAAG,GAAGJ,IAAI,CAACK,MAAM;EACvB,IAAIC,UAAU,GAAG,CAAC;EAClB;EACA,IAAIC,UAAU;;EAEd;EACA,IAAIN,KAAK,GAAG,CAAC,EAAE;IACbA,KAAK,GAAG,CAACA,KAAK,GAAGG,GAAG,GAAG,CAAC,GAAGA,GAAG,GAAGH,KAAK;EACxC,CAAC,MAAM;IACLA,KAAK,GAAGA,KAAK,GAAGG,GAAG,GAAGA,GAAG,GAAGH,KAAK;EACnC;EAEAC,MAAM,GAAGA,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,CAAC;;EAEhC;EACA,IAAIC,KAAK,CAACE,MAAM,GAAGP,SAAS,CAACU,kBAAkB,EAAE;IAC/CD,UAAU,GAAGE,KAAK,CAACC,IAAI,CAACP,KAAK,CAAC;IAC9BI,UAAU,CAACI,OAAO,CAACV,KAAK,EAAEC,MAAM,CAAC;IACjC;IACAF,IAAI,CAACD,MAAM,CAAC,GAAGQ,UAAU,CAAC;EAC5B,CAAC,MAAM;IACL;IACA,IAAIL,MAAM,EAAEF,IAAI,CAACD,MAAM,CAACE,KAAK,EAAEC,MAAM,CAAC;;IAEtC;IACA,OAAOI,UAAU,GAAGH,KAAK,CAACE,MAAM,EAAE;MAChCE,UAAU,GAAGJ,KAAK,CAACS,KAAK,CACtBN,UAAU,EACVA,UAAU,GAAGR,SAAS,CAACU,kBACzB,CAAC;MACDD,UAAU,CAACI,OAAO,CAACV,KAAK,EAAE,CAAC,CAAC;MAC5B;MACAD,IAAI,CAACD,MAAM,CAAC,GAAGQ,UAAU,CAAC;MAE1BD,UAAU,IAAIR,SAAS,CAACU,kBAAkB;MAC1CP,KAAK,IAAIH,SAAS,CAACU,kBAAkB;IACvC;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,IAAIA,CAACb,IAAI,EAAEG,KAAK,EAAE;EAChC,IAAIH,IAAI,CAACK,MAAM,GAAG,CAAC,EAAE;IACnBN,MAAM,CAACC,IAAI,EAAEA,IAAI,CAACK,MAAM,EAAE,CAAC,EAAEF,KAAK,CAAC;IACnC,OAAOH,IAAI;EACb;EAEA,OAAOG,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}