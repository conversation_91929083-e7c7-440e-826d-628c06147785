{"ast": null, "code": "const characterReferences = {\n  '\"': 'quot',\n  '&': 'amp',\n  '<': 'lt',\n  '>': 'gt'\n};\n\n/**\n * Encode only the dangerous HTML characters.\n *\n * This ensures that certain characters which have special meaning in HTML are\n * dealt with.\n * Technically, we can skip `>` and `\"` in many cases, but CM includes them.\n *\n * @param {string} value\n *   Value to encode.\n * @returns {string}\n *   Encoded value.\n */\nexport function encode(value) {\n  return value.replace(/[\"&<>]/g, replace);\n\n  /**\n   * @param {string} value\n   *   Value to replace.\n   * @returns {string}\n   *   Encoded value.\n   */\n  function replace(value) {\n    return '&' + characterReferences[(/** @type {keyof typeof characterReferences} */value)] + ';';\n  }\n}", "map": {"version": 3, "names": ["characterReferences", "encode", "value", "replace"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark-util-encode/index.js"], "sourcesContent": ["const characterReferences = {'\"': 'quot', '&': 'amp', '<': 'lt', '>': 'gt'}\n\n/**\n * Encode only the dangerous HTML characters.\n *\n * This ensures that certain characters which have special meaning in HTML are\n * dealt with.\n * Technically, we can skip `>` and `\"` in many cases, but CM includes them.\n *\n * @param {string} value\n *   Value to encode.\n * @returns {string}\n *   Encoded value.\n */\nexport function encode(value) {\n  return value.replace(/[\"&<>]/g, replace)\n\n  /**\n   * @param {string} value\n   *   Value to replace.\n   * @returns {string}\n   *   Encoded value.\n   */\n  function replace(value) {\n    return (\n      '&' +\n      characterReferences[\n        /** @type {keyof typeof characterReferences} */ (value)\n      ] +\n      ';'\n    )\n  }\n}\n"], "mappings": "AAAA,MAAMA,mBAAmB,GAAG;EAAC,GAAG,EAAE,MAAM;EAAE,GAAG,EAAE,KAAK;EAAE,GAAG,EAAE,IAAI;EAAE,GAAG,EAAE;AAAI,CAAC;;AAE3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAACC,OAAO,CAAC,SAAS,EAAEA,OAAO,CAAC;;EAExC;AACF;AACA;AACA;AACA;AACA;EACE,SAASA,OAAOA,CAACD,KAAK,EAAE;IACtB,OACE,GAAG,GACHF,mBAAmB,EACjB,+CAAiDE,KAAK,EACvD,GACD,GAAG;EAEP;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}