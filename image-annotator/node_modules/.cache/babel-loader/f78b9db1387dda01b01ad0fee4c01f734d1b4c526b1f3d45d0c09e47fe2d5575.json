{"ast": null, "code": "/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { factorySpace } from 'micromark-factory-space';\nimport { asciiAlphanumeric, asciiAlpha, markdownLineEndingOrSpace, markdownLineEnding, markdownSpace } from 'micromark-util-character';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const htmlText = {\n  name: 'htmlText',\n  tokenize: tokenizeHtmlText\n};\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHtmlText(effects, ok, nok) {\n  const self = this;\n  /** @type {NonNullable<Code> | undefined} */\n  let marker;\n  /** @type {number} */\n  let index;\n  /** @type {State} */\n  let returnState;\n  return start;\n\n  /**\n   * Start of HTML (text).\n   *\n   * ```markdown\n   * > | a <b> c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.lessThan, 'expected `<`');\n    effects.enter(types.htmlText);\n    effects.enter(types.htmlTextData);\n    effects.consume(code);\n    return open;\n  }\n\n  /**\n   * After `<`, at tag name or other stuff.\n   *\n   * ```markdown\n   * > | a <b> c\n   *        ^\n   * > | a <!doctype> c\n   *        ^\n   * > | a <!--b--> c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.exclamationMark) {\n      effects.consume(code);\n      return declarationOpen;\n    }\n    if (code === codes.slash) {\n      effects.consume(code);\n      return tagCloseStart;\n    }\n    if (code === codes.questionMark) {\n      effects.consume(code);\n      return instruction;\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code);\n      return tagOpen;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After `<!`, at declaration, comment, or CDATA.\n   *\n   * ```markdown\n   * > | a <!doctype> c\n   *         ^\n   * > | a <!--b--> c\n   *         ^\n   * > | a <![CDATA[>&<]]> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declarationOpen(code) {\n    if (code === codes.dash) {\n      effects.consume(code);\n      return commentOpenInside;\n    }\n    if (code === codes.leftSquareBracket) {\n      effects.consume(code);\n      index = 0;\n      return cdataOpenInside;\n    }\n    if (asciiAlpha(code)) {\n      effects.consume(code);\n      return declaration;\n    }\n    return nok(code);\n  }\n\n  /**\n   * In a comment, after `<!-`, at another `-`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentOpenInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code);\n      return commentEnd;\n    }\n    return nok(code);\n  }\n\n  /**\n   * In comment.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function comment(code) {\n    if (code === codes.eof) {\n      return nok(code);\n    }\n    if (code === codes.dash) {\n      effects.consume(code);\n      return commentClose;\n    }\n    if (markdownLineEnding(code)) {\n      returnState = comment;\n      return lineEndingBefore(code);\n    }\n    effects.consume(code);\n    return comment;\n  }\n\n  /**\n   * In comment, after `-`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentClose(code) {\n    if (code === codes.dash) {\n      effects.consume(code);\n      return commentEnd;\n    }\n    return comment(code);\n  }\n\n  /**\n   * In comment, after `--`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentEnd(code) {\n    return code === codes.greaterThan ? end(code) : code === codes.dash ? commentClose(code) : comment(code);\n  }\n\n  /**\n   * After `<![`, in CDATA, expecting `CDATA[`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *          ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataOpenInside(code) {\n    const value = constants.cdataOpeningString;\n    if (code === value.charCodeAt(index++)) {\n      effects.consume(code);\n      return index === value.length ? cdata : cdataOpenInside;\n    }\n    return nok(code);\n  }\n\n  /**\n   * In CDATA.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdata(code) {\n    if (code === codes.eof) {\n      return nok(code);\n    }\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code);\n      return cdataClose;\n    }\n    if (markdownLineEnding(code)) {\n      returnState = cdata;\n      return lineEndingBefore(code);\n    }\n    effects.consume(code);\n    return cdata;\n  }\n\n  /**\n   * In CDATA, after `]`, at another `]`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataClose(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code);\n      return cdataEnd;\n    }\n    return cdata(code);\n  }\n\n  /**\n   * In CDATA, after `]]`, at `>`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataEnd(code) {\n    if (code === codes.greaterThan) {\n      return end(code);\n    }\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code);\n      return cdataEnd;\n    }\n    return cdata(code);\n  }\n\n  /**\n   * In declaration.\n   *\n   * ```markdown\n   * > | a <!b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declaration(code) {\n    if (code === codes.eof || code === codes.greaterThan) {\n      return end(code);\n    }\n    if (markdownLineEnding(code)) {\n      returnState = declaration;\n      return lineEndingBefore(code);\n    }\n    effects.consume(code);\n    return declaration;\n  }\n\n  /**\n   * In instruction.\n   *\n   * ```markdown\n   * > | a <?b?> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function instruction(code) {\n    if (code === codes.eof) {\n      return nok(code);\n    }\n    if (code === codes.questionMark) {\n      effects.consume(code);\n      return instructionClose;\n    }\n    if (markdownLineEnding(code)) {\n      returnState = instruction;\n      return lineEndingBefore(code);\n    }\n    effects.consume(code);\n    return instruction;\n  }\n\n  /**\n   * In instruction, after `?`, at `>`.\n   *\n   * ```markdown\n   * > | a <?b?> c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function instructionClose(code) {\n    return code === codes.greaterThan ? end(code) : instruction(code);\n  }\n\n  /**\n   * After `</`, in closing tag, at tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseStart(code) {\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code);\n      return tagClose;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After `</x`, in a tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagClose(code) {\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code);\n      return tagClose;\n    }\n    return tagCloseBetween(code);\n  }\n\n  /**\n   * In closing tag, after tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseBetween(code) {\n    if (markdownLineEnding(code)) {\n      returnState = tagCloseBetween;\n      return lineEndingBefore(code);\n    }\n    if (markdownSpace(code)) {\n      effects.consume(code);\n      return tagCloseBetween;\n    }\n    return end(code);\n  }\n\n  /**\n   * After `<x`, in opening tag name.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpen(code) {\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code);\n      return tagOpen;\n    }\n    if (code === codes.slash || code === codes.greaterThan || markdownLineEndingOrSpace(code)) {\n      return tagOpenBetween(code);\n    }\n    return nok(code);\n  }\n\n  /**\n   * In opening tag, after tag name.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenBetween(code) {\n    if (code === codes.slash) {\n      effects.consume(code);\n      return end;\n    }\n\n    // ASCII alphabetical and `:` and `_`.\n    if (code === codes.colon || code === codes.underscore || asciiAlpha(code)) {\n      effects.consume(code);\n      return tagOpenAttributeName;\n    }\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenBetween;\n      return lineEndingBefore(code);\n    }\n    if (markdownSpace(code)) {\n      effects.consume(code);\n      return tagOpenBetween;\n    }\n    return end(code);\n  }\n\n  /**\n   * In attribute name.\n   *\n   * ```markdown\n   * > | a <b c> d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeName(code) {\n    // ASCII alphabetical and `-`, `.`, `:`, and `_`.\n    if (code === codes.dash || code === codes.dot || code === codes.colon || code === codes.underscore || asciiAlphanumeric(code)) {\n      effects.consume(code);\n      return tagOpenAttributeName;\n    }\n    return tagOpenAttributeNameAfter(code);\n  }\n\n  /**\n   * After attribute name, before initializer, the end of the tag, or\n   * whitespace.\n   *\n   * ```markdown\n   * > | a <b c> d\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeNameAfter(code) {\n    if (code === codes.equalsTo) {\n      effects.consume(code);\n      return tagOpenAttributeValueBefore;\n    }\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeNameAfter;\n      return lineEndingBefore(code);\n    }\n    if (markdownSpace(code)) {\n      effects.consume(code);\n      return tagOpenAttributeNameAfter;\n    }\n    return tagOpenBetween(code);\n  }\n\n  /**\n   * Before unquoted, double quoted, or single quoted attribute value, allowing\n   * whitespace.\n   *\n   * ```markdown\n   * > | a <b c=d> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueBefore(code) {\n    if (code === codes.eof || code === codes.lessThan || code === codes.equalsTo || code === codes.greaterThan || code === codes.graveAccent) {\n      return nok(code);\n    }\n    if (code === codes.quotationMark || code === codes.apostrophe) {\n      effects.consume(code);\n      marker = code;\n      return tagOpenAttributeValueQuoted;\n    }\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeValueBefore;\n      return lineEndingBefore(code);\n    }\n    if (markdownSpace(code)) {\n      effects.consume(code);\n      return tagOpenAttributeValueBefore;\n    }\n    effects.consume(code);\n    return tagOpenAttributeValueUnquoted;\n  }\n\n  /**\n   * In double or single quoted attribute value.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueQuoted(code) {\n    if (code === marker) {\n      effects.consume(code);\n      marker = undefined;\n      return tagOpenAttributeValueQuotedAfter;\n    }\n    if (code === codes.eof) {\n      return nok(code);\n    }\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeValueQuoted;\n      return lineEndingBefore(code);\n    }\n    effects.consume(code);\n    return tagOpenAttributeValueQuoted;\n  }\n\n  /**\n   * In unquoted attribute value.\n   *\n   * ```markdown\n   * > | a <b c=d> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueUnquoted(code) {\n    if (code === codes.eof || code === codes.quotationMark || code === codes.apostrophe || code === codes.lessThan || code === codes.equalsTo || code === codes.graveAccent) {\n      return nok(code);\n    }\n    if (code === codes.slash || code === codes.greaterThan || markdownLineEndingOrSpace(code)) {\n      return tagOpenBetween(code);\n    }\n    effects.consume(code);\n    return tagOpenAttributeValueUnquoted;\n  }\n\n  /**\n   * After double or single quoted attribute value, before whitespace or the end\n   * of the tag.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueQuotedAfter(code) {\n    if (code === codes.slash || code === codes.greaterThan || markdownLineEndingOrSpace(code)) {\n      return tagOpenBetween(code);\n    }\n    return nok(code);\n  }\n\n  /**\n   * In certain circumstances of a tag where only an `>` is allowed.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function end(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code);\n      effects.exit(types.htmlTextData);\n      effects.exit(types.htmlText);\n      return ok;\n    }\n    return nok(code);\n  }\n\n  /**\n   * At eol.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   * > | a <!--a\n   *            ^\n   *   | b-->\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingBefore(code) {\n    assert(returnState, 'expected return state');\n    assert(markdownLineEnding(code), 'expected eol');\n    effects.exit(types.htmlTextData);\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return lineEndingAfter;\n  }\n\n  /**\n   * After eol, at optional whitespace.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   *   | a <!--a\n   * > | b-->\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingAfter(code) {\n    // Always populated by defaults.\n    assert(self.parser.constructs.disable.null, 'expected `disable.null` to be populated');\n    return markdownSpace(code) ? factorySpace(effects, lineEndingAfterPrefix, types.linePrefix, self.parser.constructs.disable.null.includes('codeIndented') ? undefined : constants.tabSize)(code) : lineEndingAfterPrefix(code);\n  }\n\n  /**\n   * After eol, after optional whitespace.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   *   | a <!--a\n   * > | b-->\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingAfterPrefix(code) {\n    effects.enter(types.htmlTextData);\n    return returnState(code);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "factorySpace", "asciiAlphanumeric", "asciiAlpha", "markdownLineEndingOrSpace", "markdownLineEnding", "markdownSpace", "codes", "constants", "types", "htmlText", "name", "tokenize", "tokenizeHtmlText", "effects", "nok", "self", "marker", "index", "returnState", "start", "code", "lessThan", "enter", "htmlTextData", "consume", "open", "exclamationMark", "declarationOpen", "slash", "tagCloseStart", "questionMark", "instruction", "tagOpen", "dash", "commentOpenInside", "leftSquareBracket", "cdataOpenInside", "declaration", "commentEnd", "comment", "eof", "commentClose", "lineEndingBefore", "greaterThan", "end", "value", "cdataOpeningString", "charCodeAt", "length", "cdata", "rightSquareBracket", "cdataClose", "cdataEnd", "instructionClose", "tagClose", "tagCloseBetween", "tagOpenBetween", "colon", "underscore", "tagOpenAttributeName", "dot", "tagOpenAttributeNameAfter", "equalsTo", "tagOpenAttributeValueBefore", "graveAccent", "quotationMark", "apostrophe", "tagOpenAttributeValueQuoted", "tagOpenAttributeValueUnquoted", "undefined", "tagOpenAttributeValueQuotedAfter", "exit", "lineEnding", "lineEndingAfter", "parser", "constructs", "disable", "null", "lineEndingAfterPrefix", "linePrefix", "includes", "tabSize"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark-core-commonmark/dev/lib/html-text.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {\n  asciiAlphanumeric,\n  asciiAlpha,\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const htmlText = {name: 'htmlText', tokenize: tokenizeHtmlText}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHtmlText(effects, ok, nok) {\n  const self = this\n  /** @type {NonNullable<Code> | undefined} */\n  let marker\n  /** @type {number} */\n  let index\n  /** @type {State} */\n  let returnState\n\n  return start\n\n  /**\n   * Start of HTML (text).\n   *\n   * ```markdown\n   * > | a <b> c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.lessThan, 'expected `<`')\n    effects.enter(types.htmlText)\n    effects.enter(types.htmlTextData)\n    effects.consume(code)\n    return open\n  }\n\n  /**\n   * After `<`, at tag name or other stuff.\n   *\n   * ```markdown\n   * > | a <b> c\n   *        ^\n   * > | a <!doctype> c\n   *        ^\n   * > | a <!--b--> c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.exclamationMark) {\n      effects.consume(code)\n      return declarationOpen\n    }\n\n    if (code === codes.slash) {\n      effects.consume(code)\n      return tagCloseStart\n    }\n\n    if (code === codes.questionMark) {\n      effects.consume(code)\n      return instruction\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return tagOpen\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!`, at declaration, comment, or CDATA.\n   *\n   * ```markdown\n   * > | a <!doctype> c\n   *         ^\n   * > | a <!--b--> c\n   *         ^\n   * > | a <![CDATA[>&<]]> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declarationOpen(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentOpenInside\n    }\n\n    if (code === codes.leftSquareBracket) {\n      effects.consume(code)\n      index = 0\n      return cdataOpenInside\n    }\n\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return declaration\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In a comment, after `<!-`, at another `-`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentOpenInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentEnd\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In comment.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function comment(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = comment\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return comment\n  }\n\n  /**\n   * In comment, after `-`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentClose(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentEnd\n    }\n\n    return comment(code)\n  }\n\n  /**\n   * In comment, after `--`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentEnd(code) {\n    return code === codes.greaterThan\n      ? end(code)\n      : code === codes.dash\n        ? commentClose(code)\n        : comment(code)\n  }\n\n  /**\n   * After `<![`, in CDATA, expecting `CDATA[`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *          ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataOpenInside(code) {\n    const value = constants.cdataOpeningString\n\n    if (code === value.charCodeAt(index++)) {\n      effects.consume(code)\n      return index === value.length ? cdata : cdataOpenInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In CDATA.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdata(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = cdata\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return cdata\n  }\n\n  /**\n   * In CDATA, after `]`, at another `]`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataClose(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataEnd\n    }\n\n    return cdata(code)\n  }\n\n  /**\n   * In CDATA, after `]]`, at `>`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataEnd(code) {\n    if (code === codes.greaterThan) {\n      return end(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataEnd\n    }\n\n    return cdata(code)\n  }\n\n  /**\n   * In declaration.\n   *\n   * ```markdown\n   * > | a <!b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declaration(code) {\n    if (code === codes.eof || code === codes.greaterThan) {\n      return end(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = declaration\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return declaration\n  }\n\n  /**\n   * In instruction.\n   *\n   * ```markdown\n   * > | a <?b?> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function instruction(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (code === codes.questionMark) {\n      effects.consume(code)\n      return instructionClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = instruction\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return instruction\n  }\n\n  /**\n   * In instruction, after `?`, at `>`.\n   *\n   * ```markdown\n   * > | a <?b?> c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function instructionClose(code) {\n    return code === codes.greaterThan ? end(code) : instruction(code)\n  }\n\n  /**\n   * After `</`, in closing tag, at tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseStart(code) {\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return tagClose\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `</x`, in a tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagClose(code) {\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      return tagClose\n    }\n\n    return tagCloseBetween(code)\n  }\n\n  /**\n   * In closing tag, after tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseBetween(code) {\n    if (markdownLineEnding(code)) {\n      returnState = tagCloseBetween\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagCloseBetween\n    }\n\n    return end(code)\n  }\n\n  /**\n   * After `<x`, in opening tag name.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpen(code) {\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      return tagOpen\n    }\n\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In opening tag, after tag name.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenBetween(code) {\n    if (code === codes.slash) {\n      effects.consume(code)\n      return end\n    }\n\n    // ASCII alphabetical and `:` and `_`.\n    if (code === codes.colon || code === codes.underscore || asciiAlpha(code)) {\n      effects.consume(code)\n      return tagOpenAttributeName\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenBetween\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenBetween\n    }\n\n    return end(code)\n  }\n\n  /**\n   * In attribute name.\n   *\n   * ```markdown\n   * > | a <b c> d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeName(code) {\n    // ASCII alphabetical and `-`, `.`, `:`, and `_`.\n    if (\n      code === codes.dash ||\n      code === codes.dot ||\n      code === codes.colon ||\n      code === codes.underscore ||\n      asciiAlphanumeric(code)\n    ) {\n      effects.consume(code)\n      return tagOpenAttributeName\n    }\n\n    return tagOpenAttributeNameAfter(code)\n  }\n\n  /**\n   * After attribute name, before initializer, the end of the tag, or\n   * whitespace.\n   *\n   * ```markdown\n   * > | a <b c> d\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeNameAfter(code) {\n    if (code === codes.equalsTo) {\n      effects.consume(code)\n      return tagOpenAttributeValueBefore\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeNameAfter\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenAttributeNameAfter\n    }\n\n    return tagOpenBetween(code)\n  }\n\n  /**\n   * Before unquoted, double quoted, or single quoted attribute value, allowing\n   * whitespace.\n   *\n   * ```markdown\n   * > | a <b c=d> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueBefore(code) {\n    if (\n      code === codes.eof ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.greaterThan ||\n      code === codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.quotationMark || code === codes.apostrophe) {\n      effects.consume(code)\n      marker = code\n      return tagOpenAttributeValueQuoted\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeValueBefore\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenAttributeValueBefore\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueUnquoted\n  }\n\n  /**\n   * In double or single quoted attribute value.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueQuoted(code) {\n    if (code === marker) {\n      effects.consume(code)\n      marker = undefined\n      return tagOpenAttributeValueQuotedAfter\n    }\n\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeValueQuoted\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueQuoted\n  }\n\n  /**\n   * In unquoted attribute value.\n   *\n   * ```markdown\n   * > | a <b c=d> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueUnquoted(code) {\n    if (\n      code === codes.eof ||\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueUnquoted\n  }\n\n  /**\n   * After double or single quoted attribute value, before whitespace or the end\n   * of the tag.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueQuotedAfter(code) {\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In certain circumstances of a tag where only an `>` is allowed.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function end(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      effects.exit(types.htmlTextData)\n      effects.exit(types.htmlText)\n      return ok\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * At eol.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   * > | a <!--a\n   *            ^\n   *   | b-->\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingBefore(code) {\n    assert(returnState, 'expected return state')\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.exit(types.htmlTextData)\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return lineEndingAfter\n  }\n\n  /**\n   * After eol, at optional whitespace.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   *   | a <!--a\n   * > | b-->\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingAfter(code) {\n    // Always populated by defaults.\n    assert(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n    return markdownSpace(code)\n      ? factorySpace(\n          effects,\n          lineEndingAfterPrefix,\n          types.linePrefix,\n          self.parser.constructs.disable.null.includes('codeIndented')\n            ? undefined\n            : constants.tabSize\n        )(code)\n      : lineEndingAfterPrefix(code)\n  }\n\n  /**\n   * After eol, after optional whitespace.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   *   | a <!--a\n   * > | b-->\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingAfterPrefix(code) {\n    effects.enter(types.htmlTextData)\n    return returnState(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SACEC,iBAAiB,EACjBC,UAAU,EACVC,yBAAyB,EACzBC,kBAAkB,EAClBC,aAAa,QACR,0BAA0B;AACjC,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;;AAE7D;AACA,OAAO,MAAMC,QAAQ,GAAG;EAACC,IAAI,EAAE,UAAU;EAAEC,QAAQ,EAAEC;AAAgB,CAAC;;AAEtE;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAgBA,CAACC,OAAO,EAAEf,EAAE,EAAEgB,GAAG,EAAE;EAC1C,MAAMC,IAAI,GAAG,IAAI;EACjB;EACA,IAAIC,MAAM;EACV;EACA,IAAIC,KAAK;EACT;EACA,IAAIC,WAAW;EAEf,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBrB,MAAM,CAACqB,IAAI,KAAKd,KAAK,CAACe,QAAQ,EAAE,cAAc,CAAC;IAC/CR,OAAO,CAACS,KAAK,CAACd,KAAK,CAACC,QAAQ,CAAC;IAC7BI,OAAO,CAACS,KAAK,CAACd,KAAK,CAACe,YAAY,CAAC;IACjCV,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;IACrB,OAAOK,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,IAAIA,CAACL,IAAI,EAAE;IAClB,IAAIA,IAAI,KAAKd,KAAK,CAACoB,eAAe,EAAE;MAClCb,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOO,eAAe;IACxB;IAEA,IAAIP,IAAI,KAAKd,KAAK,CAACsB,KAAK,EAAE;MACxBf,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOS,aAAa;IACtB;IAEA,IAAIT,IAAI,KAAKd,KAAK,CAACwB,YAAY,EAAE;MAC/BjB,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOW,WAAW;IACpB;;IAEA;IACA,IAAI7B,UAAU,CAACkB,IAAI,CAAC,EAAE;MACpBP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOY,OAAO;IAChB;IAEA,OAAOlB,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASO,eAAeA,CAACP,IAAI,EAAE;IAC7B,IAAIA,IAAI,KAAKd,KAAK,CAAC2B,IAAI,EAAE;MACvBpB,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOc,iBAAiB;IAC1B;IAEA,IAAId,IAAI,KAAKd,KAAK,CAAC6B,iBAAiB,EAAE;MACpCtB,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrBH,KAAK,GAAG,CAAC;MACT,OAAOmB,eAAe;IACxB;IAEA,IAAIlC,UAAU,CAACkB,IAAI,CAAC,EAAE;MACpBP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOiB,WAAW;IACpB;IAEA,OAAOvB,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASc,iBAAiBA,CAACd,IAAI,EAAE;IAC/B,IAAIA,IAAI,KAAKd,KAAK,CAAC2B,IAAI,EAAE;MACvBpB,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOkB,UAAU;IACnB;IAEA,OAAOxB,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASmB,OAAOA,CAACnB,IAAI,EAAE;IACrB,IAAIA,IAAI,KAAKd,KAAK,CAACkC,GAAG,EAAE;MACtB,OAAO1B,GAAG,CAACM,IAAI,CAAC;IAClB;IAEA,IAAIA,IAAI,KAAKd,KAAK,CAAC2B,IAAI,EAAE;MACvBpB,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOqB,YAAY;IACrB;IAEA,IAAIrC,kBAAkB,CAACgB,IAAI,CAAC,EAAE;MAC5BF,WAAW,GAAGqB,OAAO;MACrB,OAAOG,gBAAgB,CAACtB,IAAI,CAAC;IAC/B;IAEAP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;IACrB,OAAOmB,OAAO;EAChB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,YAAYA,CAACrB,IAAI,EAAE;IAC1B,IAAIA,IAAI,KAAKd,KAAK,CAAC2B,IAAI,EAAE;MACvBpB,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOkB,UAAU;IACnB;IAEA,OAAOC,OAAO,CAACnB,IAAI,CAAC;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASkB,UAAUA,CAAClB,IAAI,EAAE;IACxB,OAAOA,IAAI,KAAKd,KAAK,CAACqC,WAAW,GAC7BC,GAAG,CAACxB,IAAI,CAAC,GACTA,IAAI,KAAKd,KAAK,CAAC2B,IAAI,GACjBQ,YAAY,CAACrB,IAAI,CAAC,GAClBmB,OAAO,CAACnB,IAAI,CAAC;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASgB,eAAeA,CAAChB,IAAI,EAAE;IAC7B,MAAMyB,KAAK,GAAGtC,SAAS,CAACuC,kBAAkB;IAE1C,IAAI1B,IAAI,KAAKyB,KAAK,CAACE,UAAU,CAAC9B,KAAK,EAAE,CAAC,EAAE;MACtCJ,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOH,KAAK,KAAK4B,KAAK,CAACG,MAAM,GAAGC,KAAK,GAAGb,eAAe;IACzD;IAEA,OAAOtB,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS6B,KAAKA,CAAC7B,IAAI,EAAE;IACnB,IAAIA,IAAI,KAAKd,KAAK,CAACkC,GAAG,EAAE;MACtB,OAAO1B,GAAG,CAACM,IAAI,CAAC;IAClB;IAEA,IAAIA,IAAI,KAAKd,KAAK,CAAC4C,kBAAkB,EAAE;MACrCrC,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAO+B,UAAU;IACnB;IAEA,IAAI/C,kBAAkB,CAACgB,IAAI,CAAC,EAAE;MAC5BF,WAAW,GAAG+B,KAAK;MACnB,OAAOP,gBAAgB,CAACtB,IAAI,CAAC;IAC/B;IAEAP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;IACrB,OAAO6B,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,UAAUA,CAAC/B,IAAI,EAAE;IACxB,IAAIA,IAAI,KAAKd,KAAK,CAAC4C,kBAAkB,EAAE;MACrCrC,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOgC,QAAQ;IACjB;IAEA,OAAOH,KAAK,CAAC7B,IAAI,CAAC;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASgC,QAAQA,CAAChC,IAAI,EAAE;IACtB,IAAIA,IAAI,KAAKd,KAAK,CAACqC,WAAW,EAAE;MAC9B,OAAOC,GAAG,CAACxB,IAAI,CAAC;IAClB;IAEA,IAAIA,IAAI,KAAKd,KAAK,CAAC4C,kBAAkB,EAAE;MACrCrC,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOgC,QAAQ;IACjB;IAEA,OAAOH,KAAK,CAAC7B,IAAI,CAAC;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiB,WAAWA,CAACjB,IAAI,EAAE;IACzB,IAAIA,IAAI,KAAKd,KAAK,CAACkC,GAAG,IAAIpB,IAAI,KAAKd,KAAK,CAACqC,WAAW,EAAE;MACpD,OAAOC,GAAG,CAACxB,IAAI,CAAC;IAClB;IAEA,IAAIhB,kBAAkB,CAACgB,IAAI,CAAC,EAAE;MAC5BF,WAAW,GAAGmB,WAAW;MACzB,OAAOK,gBAAgB,CAACtB,IAAI,CAAC;IAC/B;IAEAP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;IACrB,OAAOiB,WAAW;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASN,WAAWA,CAACX,IAAI,EAAE;IACzB,IAAIA,IAAI,KAAKd,KAAK,CAACkC,GAAG,EAAE;MACtB,OAAO1B,GAAG,CAACM,IAAI,CAAC;IAClB;IAEA,IAAIA,IAAI,KAAKd,KAAK,CAACwB,YAAY,EAAE;MAC/BjB,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOiC,gBAAgB;IACzB;IAEA,IAAIjD,kBAAkB,CAACgB,IAAI,CAAC,EAAE;MAC5BF,WAAW,GAAGa,WAAW;MACzB,OAAOW,gBAAgB,CAACtB,IAAI,CAAC;IAC/B;IAEAP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;IACrB,OAAOW,WAAW;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsB,gBAAgBA,CAACjC,IAAI,EAAE;IAC9B,OAAOA,IAAI,KAAKd,KAAK,CAACqC,WAAW,GAAGC,GAAG,CAACxB,IAAI,CAAC,GAAGW,WAAW,CAACX,IAAI,CAAC;EACnE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASS,aAAaA,CAACT,IAAI,EAAE;IAC3B;IACA,IAAIlB,UAAU,CAACkB,IAAI,CAAC,EAAE;MACpBP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOkC,QAAQ;IACjB;IAEA,OAAOxC,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASkC,QAAQA,CAAClC,IAAI,EAAE;IACtB;IACA,IAAIA,IAAI,KAAKd,KAAK,CAAC2B,IAAI,IAAIhC,iBAAiB,CAACmB,IAAI,CAAC,EAAE;MAClDP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOkC,QAAQ;IACjB;IAEA,OAAOC,eAAe,CAACnC,IAAI,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASmC,eAAeA,CAACnC,IAAI,EAAE;IAC7B,IAAIhB,kBAAkB,CAACgB,IAAI,CAAC,EAAE;MAC5BF,WAAW,GAAGqC,eAAe;MAC7B,OAAOb,gBAAgB,CAACtB,IAAI,CAAC;IAC/B;IAEA,IAAIf,aAAa,CAACe,IAAI,CAAC,EAAE;MACvBP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOmC,eAAe;IACxB;IAEA,OAAOX,GAAG,CAACxB,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASY,OAAOA,CAACZ,IAAI,EAAE;IACrB;IACA,IAAIA,IAAI,KAAKd,KAAK,CAAC2B,IAAI,IAAIhC,iBAAiB,CAACmB,IAAI,CAAC,EAAE;MAClDP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOY,OAAO;IAChB;IAEA,IACEZ,IAAI,KAAKd,KAAK,CAACsB,KAAK,IACpBR,IAAI,KAAKd,KAAK,CAACqC,WAAW,IAC1BxC,yBAAyB,CAACiB,IAAI,CAAC,EAC/B;MACA,OAAOoC,cAAc,CAACpC,IAAI,CAAC;IAC7B;IAEA,OAAON,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASoC,cAAcA,CAACpC,IAAI,EAAE;IAC5B,IAAIA,IAAI,KAAKd,KAAK,CAACsB,KAAK,EAAE;MACxBf,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOwB,GAAG;IACZ;;IAEA;IACA,IAAIxB,IAAI,KAAKd,KAAK,CAACmD,KAAK,IAAIrC,IAAI,KAAKd,KAAK,CAACoD,UAAU,IAAIxD,UAAU,CAACkB,IAAI,CAAC,EAAE;MACzEP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOuC,oBAAoB;IAC7B;IAEA,IAAIvD,kBAAkB,CAACgB,IAAI,CAAC,EAAE;MAC5BF,WAAW,GAAGsC,cAAc;MAC5B,OAAOd,gBAAgB,CAACtB,IAAI,CAAC;IAC/B;IAEA,IAAIf,aAAa,CAACe,IAAI,CAAC,EAAE;MACvBP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOoC,cAAc;IACvB;IAEA,OAAOZ,GAAG,CAACxB,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASuC,oBAAoBA,CAACvC,IAAI,EAAE;IAClC;IACA,IACEA,IAAI,KAAKd,KAAK,CAAC2B,IAAI,IACnBb,IAAI,KAAKd,KAAK,CAACsD,GAAG,IAClBxC,IAAI,KAAKd,KAAK,CAACmD,KAAK,IACpBrC,IAAI,KAAKd,KAAK,CAACoD,UAAU,IACzBzD,iBAAiB,CAACmB,IAAI,CAAC,EACvB;MACAP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOuC,oBAAoB;IAC7B;IAEA,OAAOE,yBAAyB,CAACzC,IAAI,CAAC;EACxC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASyC,yBAAyBA,CAACzC,IAAI,EAAE;IACvC,IAAIA,IAAI,KAAKd,KAAK,CAACwD,QAAQ,EAAE;MAC3BjD,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAO2C,2BAA2B;IACpC;IAEA,IAAI3D,kBAAkB,CAACgB,IAAI,CAAC,EAAE;MAC5BF,WAAW,GAAG2C,yBAAyB;MACvC,OAAOnB,gBAAgB,CAACtB,IAAI,CAAC;IAC/B;IAEA,IAAIf,aAAa,CAACe,IAAI,CAAC,EAAE;MACvBP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOyC,yBAAyB;IAClC;IAEA,OAAOL,cAAc,CAACpC,IAAI,CAAC;EAC7B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS2C,2BAA2BA,CAAC3C,IAAI,EAAE;IACzC,IACEA,IAAI,KAAKd,KAAK,CAACkC,GAAG,IAClBpB,IAAI,KAAKd,KAAK,CAACe,QAAQ,IACvBD,IAAI,KAAKd,KAAK,CAACwD,QAAQ,IACvB1C,IAAI,KAAKd,KAAK,CAACqC,WAAW,IAC1BvB,IAAI,KAAKd,KAAK,CAAC0D,WAAW,EAC1B;MACA,OAAOlD,GAAG,CAACM,IAAI,CAAC;IAClB;IAEA,IAAIA,IAAI,KAAKd,KAAK,CAAC2D,aAAa,IAAI7C,IAAI,KAAKd,KAAK,CAAC4D,UAAU,EAAE;MAC7DrD,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrBJ,MAAM,GAAGI,IAAI;MACb,OAAO+C,2BAA2B;IACpC;IAEA,IAAI/D,kBAAkB,CAACgB,IAAI,CAAC,EAAE;MAC5BF,WAAW,GAAG6C,2BAA2B;MACzC,OAAOrB,gBAAgB,CAACtB,IAAI,CAAC;IAC/B;IAEA,IAAIf,aAAa,CAACe,IAAI,CAAC,EAAE;MACvBP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAO2C,2BAA2B;IACpC;IAEAlD,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;IACrB,OAAOgD,6BAA6B;EACtC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASD,2BAA2BA,CAAC/C,IAAI,EAAE;IACzC,IAAIA,IAAI,KAAKJ,MAAM,EAAE;MACnBH,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrBJ,MAAM,GAAGqD,SAAS;MAClB,OAAOC,gCAAgC;IACzC;IAEA,IAAIlD,IAAI,KAAKd,KAAK,CAACkC,GAAG,EAAE;MACtB,OAAO1B,GAAG,CAACM,IAAI,CAAC;IAClB;IAEA,IAAIhB,kBAAkB,CAACgB,IAAI,CAAC,EAAE;MAC5BF,WAAW,GAAGiD,2BAA2B;MACzC,OAAOzB,gBAAgB,CAACtB,IAAI,CAAC;IAC/B;IAEAP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;IACrB,OAAO+C,2BAA2B;EACpC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,6BAA6BA,CAAChD,IAAI,EAAE;IAC3C,IACEA,IAAI,KAAKd,KAAK,CAACkC,GAAG,IAClBpB,IAAI,KAAKd,KAAK,CAAC2D,aAAa,IAC5B7C,IAAI,KAAKd,KAAK,CAAC4D,UAAU,IACzB9C,IAAI,KAAKd,KAAK,CAACe,QAAQ,IACvBD,IAAI,KAAKd,KAAK,CAACwD,QAAQ,IACvB1C,IAAI,KAAKd,KAAK,CAAC0D,WAAW,EAC1B;MACA,OAAOlD,GAAG,CAACM,IAAI,CAAC;IAClB;IAEA,IACEA,IAAI,KAAKd,KAAK,CAACsB,KAAK,IACpBR,IAAI,KAAKd,KAAK,CAACqC,WAAW,IAC1BxC,yBAAyB,CAACiB,IAAI,CAAC,EAC/B;MACA,OAAOoC,cAAc,CAACpC,IAAI,CAAC;IAC7B;IAEAP,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;IACrB,OAAOgD,6BAA6B;EACtC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,gCAAgCA,CAAClD,IAAI,EAAE;IAC9C,IACEA,IAAI,KAAKd,KAAK,CAACsB,KAAK,IACpBR,IAAI,KAAKd,KAAK,CAACqC,WAAW,IAC1BxC,yBAAyB,CAACiB,IAAI,CAAC,EAC/B;MACA,OAAOoC,cAAc,CAACpC,IAAI,CAAC;IAC7B;IAEA,OAAON,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASwB,GAAGA,CAACxB,IAAI,EAAE;IACjB,IAAIA,IAAI,KAAKd,KAAK,CAACqC,WAAW,EAAE;MAC9B9B,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrBP,OAAO,CAAC0D,IAAI,CAAC/D,KAAK,CAACe,YAAY,CAAC;MAChCV,OAAO,CAAC0D,IAAI,CAAC/D,KAAK,CAACC,QAAQ,CAAC;MAC5B,OAAOX,EAAE;IACX;IAEA,OAAOgB,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsB,gBAAgBA,CAACtB,IAAI,EAAE;IAC9BrB,MAAM,CAACmB,WAAW,EAAE,uBAAuB,CAAC;IAC5CnB,MAAM,CAACK,kBAAkB,CAACgB,IAAI,CAAC,EAAE,cAAc,CAAC;IAChDP,OAAO,CAAC0D,IAAI,CAAC/D,KAAK,CAACe,YAAY,CAAC;IAChCV,OAAO,CAACS,KAAK,CAACd,KAAK,CAACgE,UAAU,CAAC;IAC/B3D,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;IACrBP,OAAO,CAAC0D,IAAI,CAAC/D,KAAK,CAACgE,UAAU,CAAC;IAC9B,OAAOC,eAAe;EACxB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,eAAeA,CAACrD,IAAI,EAAE;IAC7B;IACArB,MAAM,CACJgB,IAAI,CAAC2D,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,EACnC,yCACF,CAAC;IACD,OAAOxE,aAAa,CAACe,IAAI,CAAC,GACtBpB,YAAY,CACVa,OAAO,EACPiE,qBAAqB,EACrBtE,KAAK,CAACuE,UAAU,EAChBhE,IAAI,CAAC2D,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,CAACG,QAAQ,CAAC,cAAc,CAAC,GACxDX,SAAS,GACT9D,SAAS,CAAC0E,OAChB,CAAC,CAAC7D,IAAI,CAAC,GACP0D,qBAAqB,CAAC1D,IAAI,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS0D,qBAAqBA,CAAC1D,IAAI,EAAE;IACnCP,OAAO,CAACS,KAAK,CAACd,KAAK,CAACe,YAAY,CAAC;IACjC,OAAOL,WAAW,CAACE,IAAI,CAAC;EAC1B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}