{"ast": null, "code": "/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */\nexport const values = /** @type {const} */{\n  ht: '\\t',\n  lf: '\\n',\n  cr: '\\r',\n  space: ' ',\n  exclamationMark: '!',\n  quotationMark: '\"',\n  numberSign: '#',\n  dollarSign: '$',\n  percentSign: '%',\n  ampersand: '&',\n  apostrophe: \"'\",\n  leftParenthesis: '(',\n  rightParenthesis: ')',\n  asterisk: '*',\n  plusSign: '+',\n  comma: ',',\n  dash: '-',\n  dot: '.',\n  slash: '/',\n  digit0: '0',\n  digit1: '1',\n  digit2: '2',\n  digit3: '3',\n  digit4: '4',\n  digit5: '5',\n  digit6: '6',\n  digit7: '7',\n  digit8: '8',\n  digit9: '9',\n  colon: ':',\n  semicolon: ';',\n  lessThan: '<',\n  equalsTo: '=',\n  greaterThan: '>',\n  questionMark: '?',\n  atSign: '@',\n  uppercaseA: 'A',\n  uppercaseB: 'B',\n  uppercaseC: 'C',\n  uppercaseD: 'D',\n  uppercaseE: 'E',\n  uppercaseF: 'F',\n  uppercaseG: 'G',\n  uppercaseH: 'H',\n  uppercaseI: 'I',\n  uppercaseJ: 'J',\n  uppercaseK: 'K',\n  uppercaseL: 'L',\n  uppercaseM: 'M',\n  uppercaseN: 'N',\n  uppercaseO: 'O',\n  uppercaseP: 'P',\n  uppercaseQ: 'Q',\n  uppercaseR: 'R',\n  uppercaseS: 'S',\n  uppercaseT: 'T',\n  uppercaseU: 'U',\n  uppercaseV: 'V',\n  uppercaseW: 'W',\n  uppercaseX: 'X',\n  uppercaseY: 'Y',\n  uppercaseZ: 'Z',\n  leftSquareBracket: '[',\n  backslash: '\\\\',\n  rightSquareBracket: ']',\n  caret: '^',\n  underscore: '_',\n  graveAccent: '`',\n  lowercaseA: 'a',\n  lowercaseB: 'b',\n  lowercaseC: 'c',\n  lowercaseD: 'd',\n  lowercaseE: 'e',\n  lowercaseF: 'f',\n  lowercaseG: 'g',\n  lowercaseH: 'h',\n  lowercaseI: 'i',\n  lowercaseJ: 'j',\n  lowercaseK: 'k',\n  lowercaseL: 'l',\n  lowercaseM: 'm',\n  lowercaseN: 'n',\n  lowercaseO: 'o',\n  lowercaseP: 'p',\n  lowercaseQ: 'q',\n  lowercaseR: 'r',\n  lowercaseS: 's',\n  lowercaseT: 't',\n  lowercaseU: 'u',\n  lowercaseV: 'v',\n  lowercaseW: 'w',\n  lowercaseX: 'x',\n  lowercaseY: 'y',\n  lowercaseZ: 'z',\n  leftCurlyBrace: '{',\n  verticalBar: '|',\n  rightCurlyBrace: '}',\n  tilde: '~',\n  replacementCharacter: '�'\n};", "map": {"version": 3, "names": ["values", "ht", "lf", "cr", "space", "exclamationMark", "quotationMark", "numberSign", "dollarSign", "percentSign", "ampersand", "apostrophe", "leftParenthesis", "rightParenthesis", "asterisk", "plusSign", "comma", "dash", "dot", "slash", "digit0", "digit1", "digit2", "digit3", "digit4", "digit5", "digit6", "digit7", "digit8", "digit9", "colon", "semicolon", "lessThan", "equalsTo", "greaterThan", "questionMark", "atSign", "uppercaseA", "uppercaseB", "uppercaseC", "uppercaseD", "uppercaseE", "uppercaseF", "uppercaseG", "uppercaseH", "uppercaseI", "uppercaseJ", "uppercaseK", "uppercaseL", "uppercaseM", "uppercaseN", "uppercaseO", "uppercaseP", "uppercaseQ", "uppercaseR", "uppercaseS", "uppercaseT", "uppercaseU", "uppercaseV", "uppercaseW", "uppercaseX", "uppercaseY", "uppercaseZ", "leftSquareBracket", "backslash", "rightSquareBracket", "caret", "underscore", "graveAccent", "lowercaseA", "lowercaseB", "lowercaseC", "lowercaseD", "lowercaseE", "lowercaseF", "lowercaseG", "lowercaseH", "lowercaseI", "lowercaseJ", "lowercaseK", "lowercaseL", "lowercaseM", "lowercaseN", "lowercaseO", "lowercaseP", "lowercaseQ", "lowercaseR", "lowercaseS", "lowercaseT", "lowercaseU", "lowercaseV", "lowercaseW", "lowercaseX", "lowercaseY", "lowercaseZ", "leftCurlyBrace", "verticalBar", "right<PERSON>urly<PERSON><PERSON>", "tilde", "replacementCharacter"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark-util-symbol/lib/values.js"], "sourcesContent": ["/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */\nexport const values = /** @type {const} */ ({\n  ht: '\\t',\n  lf: '\\n',\n  cr: '\\r',\n  space: ' ',\n  exclamationMark: '!',\n  quotationMark: '\"',\n  numberSign: '#',\n  dollarSign: '$',\n  percentSign: '%',\n  ampersand: '&',\n  apostrophe: \"'\",\n  leftParenthesis: '(',\n  rightParenthesis: ')',\n  asterisk: '*',\n  plusSign: '+',\n  comma: ',',\n  dash: '-',\n  dot: '.',\n  slash: '/',\n  digit0: '0',\n  digit1: '1',\n  digit2: '2',\n  digit3: '3',\n  digit4: '4',\n  digit5: '5',\n  digit6: '6',\n  digit7: '7',\n  digit8: '8',\n  digit9: '9',\n  colon: ':',\n  semicolon: ';',\n  lessThan: '<',\n  equalsTo: '=',\n  greaterThan: '>',\n  questionMark: '?',\n  atSign: '@',\n  uppercaseA: 'A',\n  uppercaseB: 'B',\n  uppercaseC: 'C',\n  uppercaseD: 'D',\n  uppercaseE: 'E',\n  uppercaseF: 'F',\n  uppercaseG: 'G',\n  uppercaseH: 'H',\n  uppercaseI: 'I',\n  uppercaseJ: 'J',\n  uppercaseK: 'K',\n  uppercaseL: 'L',\n  uppercaseM: 'M',\n  uppercaseN: 'N',\n  uppercaseO: 'O',\n  uppercaseP: 'P',\n  uppercaseQ: 'Q',\n  uppercaseR: 'R',\n  uppercaseS: 'S',\n  uppercaseT: 'T',\n  uppercaseU: 'U',\n  uppercaseV: 'V',\n  uppercaseW: 'W',\n  uppercaseX: 'X',\n  uppercaseY: 'Y',\n  uppercaseZ: 'Z',\n  leftSquareBracket: '[',\n  backslash: '\\\\',\n  rightSquareBracket: ']',\n  caret: '^',\n  underscore: '_',\n  graveAccent: '`',\n  lowercaseA: 'a',\n  lowercaseB: 'b',\n  lowercaseC: 'c',\n  lowercaseD: 'd',\n  lowercaseE: 'e',\n  lowercaseF: 'f',\n  lowercaseG: 'g',\n  lowercaseH: 'h',\n  lowercaseI: 'i',\n  lowercaseJ: 'j',\n  lowercaseK: 'k',\n  lowercaseL: 'l',\n  lowercaseM: 'm',\n  lowercaseN: 'n',\n  lowercaseO: 'o',\n  lowercaseP: 'p',\n  lowercaseQ: 'q',\n  lowercaseR: 'r',\n  lowercaseS: 's',\n  lowercaseT: 't',\n  lowercaseU: 'u',\n  lowercaseV: 'v',\n  lowercaseW: 'w',\n  lowercaseX: 'x',\n  lowercaseY: 'y',\n  lowercaseZ: 'z',\n  leftCurlyBrace: '{',\n  verticalBar: '|',\n  rightCurlyBrace: '}',\n  tilde: '~',\n  replacementCharacter: '�'\n})\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,MAAM,GAAG,oBAAsB;EAC1CC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,KAAK,EAAE,GAAG;EACVC,eAAe,EAAE,GAAG;EACpBC,aAAa,EAAE,GAAG;EAClBC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,WAAW,EAAE,GAAG;EAChBC,SAAS,EAAE,GAAG;EACdC,UAAU,EAAE,GAAG;EACfC,eAAe,EAAE,GAAG;EACpBC,gBAAgB,EAAE,GAAG;EACrBC,QAAQ,EAAE,GAAG;EACbC,QAAQ,EAAE,GAAG;EACbC,KAAK,EAAE,GAAG;EACVC,IAAI,EAAE,GAAG;EACTC,GAAG,EAAE,GAAG;EACRC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,KAAK,EAAE,GAAG;EACVC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,GAAG;EACbC,QAAQ,EAAE,GAAG;EACbC,WAAW,EAAE,GAAG;EAChBC,YAAY,EAAE,GAAG;EACjBC,MAAM,EAAE,GAAG;EACXC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,iBAAiB,EAAE,GAAG;EACtBC,SAAS,EAAE,IAAI;EACfC,kBAAkB,EAAE,GAAG;EACvBC,KAAK,EAAE,GAAG;EACVC,UAAU,EAAE,GAAG;EACfC,WAAW,EAAE,GAAG;EAChBC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,cAAc,EAAE,GAAG;EACnBC,WAAW,EAAE,GAAG;EAChBC,eAAe,EAAE,GAAG;EACpBC,KAAK,EAAE,GAAG;EACVC,oBAAoB,EAAE;AACxB,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}