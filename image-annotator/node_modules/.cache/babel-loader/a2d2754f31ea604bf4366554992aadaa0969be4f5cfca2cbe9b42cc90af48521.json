{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */\n\nimport { normalizeUri } from 'micromark-util-sanitize-uri';\nimport { revert } from '../revert.js';\n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nexport function linkReference(state, node) {\n  const id = String(node.identifier).toUpperCase();\n  const definition = state.definitionById.get(id);\n  if (!definition) {\n    return revert(state, node);\n  }\n\n  /** @type {Properties} */\n  const properties = {\n    href: normalizeUri(definition.url || '')\n  };\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title;\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["normalizeUri", "revert", "linkReference", "state", "node", "id", "String", "identifier", "toUpperCase", "definition", "definitionById", "get", "properties", "href", "url", "title", "undefined", "result", "type", "tagName", "children", "all", "patch", "applyData"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/mdast-util-to-hast/lib/handlers/link-reference.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\nimport {revert} from '../revert.js'\n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nexport function linkReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const definition = state.definitionById.get(id)\n\n  if (!definition) {\n    return revert(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {href: normalizeUri(definition.url || '')}\n\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,6BAA6B;AACxD,SAAQC,MAAM,QAAO,cAAc;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACzC,MAAMC,EAAE,GAAGC,MAAM,CAACF,IAAI,CAACG,UAAU,CAAC,CAACC,WAAW,CAAC,CAAC;EAChD,MAAMC,UAAU,GAAGN,KAAK,CAACO,cAAc,CAACC,GAAG,CAACN,EAAE,CAAC;EAE/C,IAAI,CAACI,UAAU,EAAE;IACf,OAAOR,MAAM,CAACE,KAAK,EAAEC,IAAI,CAAC;EAC5B;;EAEA;EACA,MAAMQ,UAAU,GAAG;IAACC,IAAI,EAAEb,YAAY,CAACS,UAAU,CAACK,GAAG,IAAI,EAAE;EAAC,CAAC;EAE7D,IAAIL,UAAU,CAACM,KAAK,KAAK,IAAI,IAAIN,UAAU,CAACM,KAAK,KAAKC,SAAS,EAAE;IAC/DJ,UAAU,CAACG,KAAK,GAAGN,UAAU,CAACM,KAAK;EACrC;;EAEA;EACA,MAAME,MAAM,GAAG;IACbC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,GAAG;IACZP,UAAU;IACVQ,QAAQ,EAAEjB,KAAK,CAACkB,GAAG,CAACjB,IAAI;EAC1B,CAAC;EACDD,KAAK,CAACmB,KAAK,CAAClB,IAAI,EAAEa,MAAM,CAAC;EACzB,OAAOd,KAAK,CAACoB,SAAS,CAACnB,IAAI,EAAEa,MAAM,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}