{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/pages/ImageDetailPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useImageContext } from '../contexts/ImageContext';\nimport AnnotationViewer from '../components/AnnotationViewer';\nimport AnnotationEditor from '../components/AnnotationEditor';\nimport './ImageDetailPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageDetailPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    getImageById,\n    addAnnotation,\n    updateAnnotation,\n    deleteAnnotation\n  } = useImageContext();\n  const [isCreatingAnnotation, setIsCreatingAnnotation] = useState(false);\n  const [selectedAnnotation, setSelectedAnnotation] = useState(null);\n  const [hoveredAnnotation, setHoveredAnnotation] = useState(null);\n  const [startPoint, setStartPoint] = useState(null);\n  const [currentRect, setCurrentRect] = useState(null);\n  const imageRef = useRef(null);\n  const containerRef = useRef(null);\n  const image = id ? getImageById(id) : undefined;\n  useEffect(() => {\n    if (!image) {\n      navigate('/');\n    }\n  }, [image, navigate]);\n  if (!image) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Kh\\xF4ng t\\xECm th\\u1EA5y \\u1EA3nh\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 12\n    }, this);\n  }\n  const getImageCoordinates = (clientX, clientY) => {\n    if (!imageRef.current) return null;\n    const rect = imageRef.current.getBoundingClientRect();\n    const x = clientX - rect.left;\n    const y = clientY - rect.top;\n\n    // Convert to relative coordinates (0-1)\n    const relativeX = x / rect.width;\n    const relativeY = y / rect.height;\n    return {\n      x: relativeX,\n      y: relativeY\n    };\n  };\n  const handleMouseDown = e => {\n    if (!isCreatingAnnotation) return;\n    const coords = getImageCoordinates(e.clientX, e.clientY);\n    if (coords) {\n      setStartPoint(coords);\n    }\n  };\n  const handleMouseMove = e => {\n    if (!isCreatingAnnotation || !startPoint) return;\n    const coords = getImageCoordinates(e.clientX, e.clientY);\n    if (coords) {\n      const width = Math.abs(coords.x - startPoint.x);\n      const height = Math.abs(coords.y - startPoint.y);\n      const x = Math.min(startPoint.x, coords.x);\n      const y = Math.min(startPoint.y, coords.y);\n      setCurrentRect({\n        x,\n        y,\n        width,\n        height\n      });\n    }\n  };\n  const handleMouseUp = e => {\n    if (!isCreatingAnnotation || !startPoint || !currentRect) return;\n\n    // Only create annotation if rectangle is large enough\n    if (currentRect.width > 0.01 && currentRect.height > 0.01) {\n      const newAnnotation = {\n        x: currentRect.x,\n        y: currentRect.y,\n        width: currentRect.width,\n        height: currentRect.height,\n        content: '# Ghi chú mới\\n\\nNhập nội dung markdown tại đây...',\n        title: 'Ghi chú mới'\n      };\n      addAnnotation(image.id, newAnnotation);\n    }\n    setIsCreatingAnnotation(false);\n    setStartPoint(null);\n    setCurrentRect(null);\n  };\n  const handleAnnotationClick = annotation => {\n    setSelectedAnnotation(annotation);\n  };\n  const handleAnnotationSave = (content, title) => {\n    if (selectedAnnotation) {\n      updateAnnotation(image.id, selectedAnnotation.id, {\n        content,\n        title\n      });\n      setSelectedAnnotation(null);\n    }\n  };\n  const handleAnnotationDelete = () => {\n    if (selectedAnnotation) {\n      deleteAnnotation(image.id, selectedAnnotation.id);\n      setSelectedAnnotation(null);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"image-detail-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"detail-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/'),\n        className: \"back-button\",\n        children: \"\\u2190 Quay l\\u1EA1i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: image.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `create-annotation-btn ${isCreatingAnnotation ? 'active' : ''}`,\n          onClick: () => setIsCreatingAnnotation(!isCreatingAnnotation),\n          children: isCreatingAnnotation ? 'Hủy tạo vùng' : 'Tạo vùng ghi chú'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"detail-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"image-container\",\n        ref: containerRef,\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          ref: imageRef,\n          src: image.url,\n          alt: image.name,\n          className: `main-image ${isCreatingAnnotation ? 'creating' : ''}`,\n          onMouseDown: handleMouseDown,\n          onMouseMove: handleMouseMove,\n          onMouseUp: handleMouseUp,\n          draggable: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), image.annotations.map(annotation => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"annotation-overlay\",\n          style: {\n            left: `${annotation.x * 100}%`,\n            top: `${annotation.y * 100}%`,\n            width: `${annotation.width * 100}%`,\n            height: `${annotation.height * 100}%`\n          },\n          onClick: () => handleAnnotationClick(annotation),\n          onMouseEnter: () => setHoveredAnnotation(annotation),\n          onMouseLeave: () => setHoveredAnnotation(null)\n        }, annotation.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this)), currentRect && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"annotation-overlay creating\",\n          style: {\n            left: `${currentRect.x * 100}%`,\n            top: `${currentRect.y * 100}%`,\n            width: `${currentRect.width * 100}%`,\n            height: `${currentRect.height * 100}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), hoveredAnnotation && !selectedAnnotation && /*#__PURE__*/_jsxDEV(AnnotationViewer, {\n        annotation: hoveredAnnotation,\n        position: \"left\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this), selectedAnnotation && /*#__PURE__*/_jsxDEV(AnnotationEditor, {\n        annotation: selectedAnnotation,\n        onSave: handleAnnotationSave,\n        onDelete: handleAnnotationDelete,\n        onClose: () => setSelectedAnnotation(null)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n};\n_s(ImageDetailPage, \"DsEpB+IVmI9U4w7rmomCs7HnPTw=\", false, function () {\n  return [useParams, useNavigate, useImageContext];\n});\n_c = ImageDetailPage;\nexport default ImageDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ImageDetailPage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useParams", "useNavigate", "useImageContext", "AnnotationViewer", "AnnotationEditor", "jsxDEV", "_jsxDEV", "ImageDetailPage", "_s", "id", "navigate", "getImageById", "addAnnotation", "updateAnnotation", "deleteAnnotation", "isCreatingAnnotation", "setIsCreatingAnnotation", "selectedAnnotation", "setSelectedAnnotation", "hoveredAnnotation", "setHoveredAnnotation", "startPoint", "setStartPoint", "currentRect", "setCurrentRect", "imageRef", "containerRef", "image", "undefined", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getImageCoordinates", "clientX", "clientY", "current", "rect", "getBoundingClientRect", "x", "left", "y", "top", "relativeX", "width", "relativeY", "height", "handleMouseDown", "e", "coords", "handleMouseMove", "Math", "abs", "min", "handleMouseUp", "newAnnotation", "content", "title", "handleAnnotationClick", "annotation", "handleAnnotationSave", "handleAnnotationDelete", "className", "onClick", "name", "ref", "src", "url", "alt", "onMouseDown", "onMouseMove", "onMouseUp", "draggable", "annotations", "map", "style", "onMouseEnter", "onMouseLeave", "position", "onSave", "onDelete", "onClose", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/pages/ImageDetailPage.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useImageContext } from '../contexts/ImageContext';\nimport AnnotationViewer from '../components/AnnotationViewer';\nimport AnnotationEditor from '../components/AnnotationEditor';\nimport { Annotation, Point } from '../types';\nimport './ImageDetailPage.css';\n\nconst ImageDetailPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const { getImageById, addAnnotation, updateAnnotation, deleteAnnotation } = useImageContext();\n  const [isCreatingAnnotation, setIsCreatingAnnotation] = useState(false);\n  const [selectedAnnotation, setSelectedAnnotation] = useState<Annotation | null>(null);\n  const [hoveredAnnotation, setHoveredAnnotation] = useState<Annotation | null>(null);\n  const [startPoint, setStartPoint] = useState<Point | null>(null);\n  const [currentRect, setCurrentRect] = useState<{ x: number; y: number; width: number; height: number } | null>(null);\n  const imageRef = useRef<HTMLImageElement>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  const image = id ? getImageById(id) : undefined;\n\n  useEffect(() => {\n    if (!image) {\n      navigate('/');\n    }\n  }, [image, navigate]);\n\n  if (!image) {\n    return <div>Không tìm thấy ảnh</div>;\n  }\n\n  const getImageCoordinates = (clientX: number, clientY: number) => {\n    if (!imageRef.current) return null;\n\n    const rect = imageRef.current.getBoundingClientRect();\n\n    const x = clientX - rect.left;\n    const y = clientY - rect.top;\n\n    // Convert to relative coordinates (0-1)\n    const relativeX = x / rect.width;\n    const relativeY = y / rect.height;\n\n    return { x: relativeX, y: relativeY };\n  };\n\n  const handleMouseDown = (e: React.MouseEvent) => {\n    if (!isCreatingAnnotation) return;\n    \n    const coords = getImageCoordinates(e.clientX, e.clientY);\n    if (coords) {\n      setStartPoint(coords);\n    }\n  };\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    if (!isCreatingAnnotation || !startPoint) return;\n    \n    const coords = getImageCoordinates(e.clientX, e.clientY);\n    if (coords) {\n      const width = Math.abs(coords.x - startPoint.x);\n      const height = Math.abs(coords.y - startPoint.y);\n      const x = Math.min(startPoint.x, coords.x);\n      const y = Math.min(startPoint.y, coords.y);\n      \n      setCurrentRect({ x, y, width, height });\n    }\n  };\n\n  const handleMouseUp = (e: React.MouseEvent) => {\n    if (!isCreatingAnnotation || !startPoint || !currentRect) return;\n    \n    // Only create annotation if rectangle is large enough\n    if (currentRect.width > 0.01 && currentRect.height > 0.01) {\n      const newAnnotation = {\n        x: currentRect.x,\n        y: currentRect.y,\n        width: currentRect.width,\n        height: currentRect.height,\n        content: '# Ghi chú mới\\n\\nNhập nội dung markdown tại đây...',\n        title: 'Ghi chú mới'\n      };\n      \n      addAnnotation(image.id, newAnnotation);\n    }\n    \n    setIsCreatingAnnotation(false);\n    setStartPoint(null);\n    setCurrentRect(null);\n  };\n\n  const handleAnnotationClick = (annotation: Annotation) => {\n    setSelectedAnnotation(annotation);\n  };\n\n  const handleAnnotationSave = (content: string, title?: string) => {\n    if (selectedAnnotation) {\n      updateAnnotation(image.id, selectedAnnotation.id, { content, title });\n      setSelectedAnnotation(null);\n    }\n  };\n\n  const handleAnnotationDelete = () => {\n    if (selectedAnnotation) {\n      deleteAnnotation(image.id, selectedAnnotation.id);\n      setSelectedAnnotation(null);\n    }\n  };\n\n  return (\n    <div className=\"image-detail-page\">\n      <header className=\"detail-header\">\n        <button onClick={() => navigate('/')} className=\"back-button\">\n          ← Quay lại\n        </button>\n        <h1>{image.name}</h1>\n        <div className=\"toolbar\">\n          <button \n            className={`create-annotation-btn ${isCreatingAnnotation ? 'active' : ''}`}\n            onClick={() => setIsCreatingAnnotation(!isCreatingAnnotation)}\n          >\n            {isCreatingAnnotation ? 'Hủy tạo vùng' : 'Tạo vùng ghi chú'}\n          </button>\n        </div>\n      </header>\n\n      <div className=\"detail-content\">\n        <div className=\"image-container\" ref={containerRef}>\n          <img \n            ref={imageRef}\n            src={image.url} \n            alt={image.name}\n            className={`main-image ${isCreatingAnnotation ? 'creating' : ''}`}\n            onMouseDown={handleMouseDown}\n            onMouseMove={handleMouseMove}\n            onMouseUp={handleMouseUp}\n            draggable={false}\n          />\n          \n          {/* Render existing annotations */}\n          {image.annotations.map(annotation => (\n            <div\n              key={annotation.id}\n              className=\"annotation-overlay\"\n              style={{\n                left: `${annotation.x * 100}%`,\n                top: `${annotation.y * 100}%`,\n                width: `${annotation.width * 100}%`,\n                height: `${annotation.height * 100}%`,\n              }}\n              onClick={() => handleAnnotationClick(annotation)}\n              onMouseEnter={() => setHoveredAnnotation(annotation)}\n              onMouseLeave={() => setHoveredAnnotation(null)}\n            />\n          ))}\n          \n          {/* Render current drawing rectangle */}\n          {currentRect && (\n            <div\n              className=\"annotation-overlay creating\"\n              style={{\n                left: `${currentRect.x * 100}%`,\n                top: `${currentRect.y * 100}%`,\n                width: `${currentRect.width * 100}%`,\n                height: `${currentRect.height * 100}%`,\n              }}\n            />\n          )}\n        </div>\n\n        {/* Annotation viewer for hover */}\n        {hoveredAnnotation && !selectedAnnotation && (\n          <AnnotationViewer \n            annotation={hoveredAnnotation}\n            position=\"left\"\n          />\n        )}\n\n        {/* Annotation editor */}\n        {selectedAnnotation && (\n          <AnnotationEditor\n            annotation={selectedAnnotation}\n            onSave={handleAnnotationSave}\n            onDelete={handleAnnotationDelete}\n            onClose={() => setSelectedAnnotation(null)}\n          />\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ImageDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,gBAAgB,MAAM,gCAAgC;AAE7D,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAG,CAAC,GAAGT,SAAS,CAAiB,CAAC;EAC1C,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU,YAAY;IAAEC,aAAa;IAAEC,gBAAgB;IAAEC;EAAiB,CAAC,GAAGZ,eAAe,CAAC,CAAC;EAC7F,MAAM,CAACa,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACoB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrB,QAAQ,CAAoB,IAAI,CAAC;EACrF,MAAM,CAACsB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvB,QAAQ,CAAoB,IAAI,CAAC;EACnF,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAe,IAAI,CAAC;EAChE,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAiE,IAAI,CAAC;EACpH,MAAM4B,QAAQ,GAAG3B,MAAM,CAAmB,IAAI,CAAC;EAC/C,MAAM4B,YAAY,GAAG5B,MAAM,CAAiB,IAAI,CAAC;EAEjD,MAAM6B,KAAK,GAAGlB,EAAE,GAAGE,YAAY,CAACF,EAAE,CAAC,GAAGmB,SAAS;EAE/C7B,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4B,KAAK,EAAE;MACVjB,QAAQ,CAAC,GAAG,CAAC;IACf;EACF,CAAC,EAAE,CAACiB,KAAK,EAAEjB,QAAQ,CAAC,CAAC;EAErB,IAAI,CAACiB,KAAK,EAAE;IACV,oBAAOrB,OAAA;MAAAuB,QAAA,EAAK;IAAkB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACtC;EAEA,MAAMC,mBAAmB,GAAGA,CAACC,OAAe,EAAEC,OAAe,KAAK;IAChE,IAAI,CAACX,QAAQ,CAACY,OAAO,EAAE,OAAO,IAAI;IAElC,MAAMC,IAAI,GAAGb,QAAQ,CAACY,OAAO,CAACE,qBAAqB,CAAC,CAAC;IAErD,MAAMC,CAAC,GAAGL,OAAO,GAAGG,IAAI,CAACG,IAAI;IAC7B,MAAMC,CAAC,GAAGN,OAAO,GAAGE,IAAI,CAACK,GAAG;;IAE5B;IACA,MAAMC,SAAS,GAAGJ,CAAC,GAAGF,IAAI,CAACO,KAAK;IAChC,MAAMC,SAAS,GAAGJ,CAAC,GAAGJ,IAAI,CAACS,MAAM;IAEjC,OAAO;MAAEP,CAAC,EAAEI,SAAS;MAAEF,CAAC,EAAEI;IAAU,CAAC;EACvC,CAAC;EAED,MAAME,eAAe,GAAIC,CAAmB,IAAK;IAC/C,IAAI,CAAClC,oBAAoB,EAAE;IAE3B,MAAMmC,MAAM,GAAGhB,mBAAmB,CAACe,CAAC,CAACd,OAAO,EAAEc,CAAC,CAACb,OAAO,CAAC;IACxD,IAAIc,MAAM,EAAE;MACV5B,aAAa,CAAC4B,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMC,eAAe,GAAIF,CAAmB,IAAK;IAC/C,IAAI,CAAClC,oBAAoB,IAAI,CAACM,UAAU,EAAE;IAE1C,MAAM6B,MAAM,GAAGhB,mBAAmB,CAACe,CAAC,CAACd,OAAO,EAAEc,CAAC,CAACb,OAAO,CAAC;IACxD,IAAIc,MAAM,EAAE;MACV,MAAML,KAAK,GAAGO,IAAI,CAACC,GAAG,CAACH,MAAM,CAACV,CAAC,GAAGnB,UAAU,CAACmB,CAAC,CAAC;MAC/C,MAAMO,MAAM,GAAGK,IAAI,CAACC,GAAG,CAACH,MAAM,CAACR,CAAC,GAAGrB,UAAU,CAACqB,CAAC,CAAC;MAChD,MAAMF,CAAC,GAAGY,IAAI,CAACE,GAAG,CAACjC,UAAU,CAACmB,CAAC,EAAEU,MAAM,CAACV,CAAC,CAAC;MAC1C,MAAME,CAAC,GAAGU,IAAI,CAACE,GAAG,CAACjC,UAAU,CAACqB,CAAC,EAAEQ,MAAM,CAACR,CAAC,CAAC;MAE1ClB,cAAc,CAAC;QAAEgB,CAAC;QAAEE,CAAC;QAAEG,KAAK;QAAEE;MAAO,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAMQ,aAAa,GAAIN,CAAmB,IAAK;IAC7C,IAAI,CAAClC,oBAAoB,IAAI,CAACM,UAAU,IAAI,CAACE,WAAW,EAAE;;IAE1D;IACA,IAAIA,WAAW,CAACsB,KAAK,GAAG,IAAI,IAAItB,WAAW,CAACwB,MAAM,GAAG,IAAI,EAAE;MACzD,MAAMS,aAAa,GAAG;QACpBhB,CAAC,EAAEjB,WAAW,CAACiB,CAAC;QAChBE,CAAC,EAAEnB,WAAW,CAACmB,CAAC;QAChBG,KAAK,EAAEtB,WAAW,CAACsB,KAAK;QACxBE,MAAM,EAAExB,WAAW,CAACwB,MAAM;QAC1BU,OAAO,EAAE,oDAAoD;QAC7DC,KAAK,EAAE;MACT,CAAC;MAED9C,aAAa,CAACe,KAAK,CAAClB,EAAE,EAAE+C,aAAa,CAAC;IACxC;IAEAxC,uBAAuB,CAAC,KAAK,CAAC;IAC9BM,aAAa,CAAC,IAAI,CAAC;IACnBE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMmC,qBAAqB,GAAIC,UAAsB,IAAK;IACxD1C,qBAAqB,CAAC0C,UAAU,CAAC;EACnC,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAACJ,OAAe,EAAEC,KAAc,KAAK;IAChE,IAAIzC,kBAAkB,EAAE;MACtBJ,gBAAgB,CAACc,KAAK,CAAClB,EAAE,EAAEQ,kBAAkB,CAACR,EAAE,EAAE;QAAEgD,OAAO;QAAEC;MAAM,CAAC,CAAC;MACrExC,qBAAqB,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC;EAED,MAAM4C,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI7C,kBAAkB,EAAE;MACtBH,gBAAgB,CAACa,KAAK,CAAClB,EAAE,EAAEQ,kBAAkB,CAACR,EAAE,CAAC;MACjDS,qBAAqB,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC;EAED,oBACEZ,OAAA;IAAKyD,SAAS,EAAC,mBAAmB;IAAAlC,QAAA,gBAChCvB,OAAA;MAAQyD,SAAS,EAAC,eAAe;MAAAlC,QAAA,gBAC/BvB,OAAA;QAAQ0D,OAAO,EAAEA,CAAA,KAAMtD,QAAQ,CAAC,GAAG,CAAE;QAACqD,SAAS,EAAC,aAAa;QAAAlC,QAAA,EAAC;MAE9D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3B,OAAA;QAAAuB,QAAA,EAAKF,KAAK,CAACsC;MAAI;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrB3B,OAAA;QAAKyD,SAAS,EAAC,SAAS;QAAAlC,QAAA,eACtBvB,OAAA;UACEyD,SAAS,EAAE,yBAAyBhD,oBAAoB,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC3EiD,OAAO,EAAEA,CAAA,KAAMhD,uBAAuB,CAAC,CAACD,oBAAoB,CAAE;UAAAc,QAAA,EAE7Dd,oBAAoB,GAAG,cAAc,GAAG;QAAkB;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET3B,OAAA;MAAKyD,SAAS,EAAC,gBAAgB;MAAAlC,QAAA,gBAC7BvB,OAAA;QAAKyD,SAAS,EAAC,iBAAiB;QAACG,GAAG,EAAExC,YAAa;QAAAG,QAAA,gBACjDvB,OAAA;UACE4D,GAAG,EAAEzC,QAAS;UACd0C,GAAG,EAAExC,KAAK,CAACyC,GAAI;UACfC,GAAG,EAAE1C,KAAK,CAACsC,IAAK;UAChBF,SAAS,EAAE,cAAchD,oBAAoB,GAAG,UAAU,GAAG,EAAE,EAAG;UAClEuD,WAAW,EAAEtB,eAAgB;UAC7BuB,WAAW,EAAEpB,eAAgB;UAC7BqB,SAAS,EAAEjB,aAAc;UACzBkB,SAAS,EAAE;QAAM;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,EAGDN,KAAK,CAAC+C,WAAW,CAACC,GAAG,CAACf,UAAU,iBAC/BtD,OAAA;UAEEyD,SAAS,EAAC,oBAAoB;UAC9Ba,KAAK,EAAE;YACLnC,IAAI,EAAE,GAAGmB,UAAU,CAACpB,CAAC,GAAG,GAAG,GAAG;YAC9BG,GAAG,EAAE,GAAGiB,UAAU,CAAClB,CAAC,GAAG,GAAG,GAAG;YAC7BG,KAAK,EAAE,GAAGe,UAAU,CAACf,KAAK,GAAG,GAAG,GAAG;YACnCE,MAAM,EAAE,GAAGa,UAAU,CAACb,MAAM,GAAG,GAAG;UACpC,CAAE;UACFiB,OAAO,EAAEA,CAAA,KAAML,qBAAqB,CAACC,UAAU,CAAE;UACjDiB,YAAY,EAAEA,CAAA,KAAMzD,oBAAoB,CAACwC,UAAU,CAAE;UACrDkB,YAAY,EAAEA,CAAA,KAAM1D,oBAAoB,CAAC,IAAI;QAAE,GAV1CwC,UAAU,CAACnD,EAAE;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWnB,CACF,CAAC,EAGDV,WAAW,iBACVjB,OAAA;UACEyD,SAAS,EAAC,6BAA6B;UACvCa,KAAK,EAAE;YACLnC,IAAI,EAAE,GAAGlB,WAAW,CAACiB,CAAC,GAAG,GAAG,GAAG;YAC/BG,GAAG,EAAE,GAAGpB,WAAW,CAACmB,CAAC,GAAG,GAAG,GAAG;YAC9BG,KAAK,EAAE,GAAGtB,WAAW,CAACsB,KAAK,GAAG,GAAG,GAAG;YACpCE,MAAM,EAAE,GAAGxB,WAAW,CAACwB,MAAM,GAAG,GAAG;UACrC;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLd,iBAAiB,IAAI,CAACF,kBAAkB,iBACvCX,OAAA,CAACH,gBAAgB;QACfyD,UAAU,EAAEzC,iBAAkB;QAC9B4D,QAAQ,EAAC;MAAM;QAAAjD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CACF,EAGAhB,kBAAkB,iBACjBX,OAAA,CAACF,gBAAgB;QACfwD,UAAU,EAAE3C,kBAAmB;QAC/B+D,MAAM,EAAEnB,oBAAqB;QAC7BoB,QAAQ,EAAEnB,sBAAuB;QACjCoB,OAAO,EAAEA,CAAA,KAAMhE,qBAAqB,CAAC,IAAI;MAAE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CAvLID,eAAyB;EAAA,QACdP,SAAS,EACPC,WAAW,EACgDC,eAAe;AAAA;AAAAiF,EAAA,GAHvF5E,eAAyB;AAyL/B,eAAeA,eAAe;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}