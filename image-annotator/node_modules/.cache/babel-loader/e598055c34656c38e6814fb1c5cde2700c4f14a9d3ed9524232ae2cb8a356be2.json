{"ast": null, "code": "// Note: types exposed from `index.d.ts`.\nexport { handlers as defaultHandlers } from './lib/handlers/index.js';\nexport { toHast } from './lib/index.js';\nexport { defaultFootnoteBackContent, defaultFootnoteBackLabel } from './lib/footer.js';", "map": {"version": 3, "names": ["handlers", "defaultHandlers", "toHast", "defaultFootnoteBackContent", "defaultFootnoteBackLabel"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/mdast-util-to-hast/index.js"], "sourcesContent": ["// Note: types exposed from `index.d.ts`.\nexport {handlers as defaultHandlers} from './lib/handlers/index.js'\nexport {toHast} from './lib/index.js'\nexport {\n  defaultFootnoteBackContent,\n  defaultFootnoteBackLabel\n} from './lib/footer.js'\n"], "mappings": "AAAA;AACA,SAAQA,QAAQ,IAAIC,eAAe,QAAO,yBAAyB;AACnE,SAAQC,MAAM,QAAO,gBAAgB;AACrC,SACEC,0BAA0B,EAC1BC,wBAAwB,QACnB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}