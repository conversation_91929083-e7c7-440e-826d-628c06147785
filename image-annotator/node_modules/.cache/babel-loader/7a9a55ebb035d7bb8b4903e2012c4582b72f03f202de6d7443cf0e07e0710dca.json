{"ast": null, "code": "/**\n * @param {string} d\n * @returns {string}\n */\nexport function color(d) {\n  return d;\n}", "map": {"version": 3, "names": ["color", "d"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/unist-util-visit-parents/lib/color.js"], "sourcesContent": ["/**\n * @param {string} d\n * @returns {string}\n */\nexport function color(d) {\n  return d\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,KAAKA,CAACC,CAAC,EAAE;EACvB,OAAOA,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}