{"ast": null, "code": "/**\n * @typedef {import('./lib/index.js').Options} Options\n */\n\nexport { defaultFootnoteBackContent, defaultFootnoteBackLabel, defaultHandlers } from 'mdast-util-to-hast';\nexport { default } from './lib/index.js';", "map": {"version": 3, "names": ["defaultFootnoteBackContent", "defaultFootnoteBackLabel", "defaultHandlers", "default"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/remark-rehype/index.js"], "sourcesContent": ["/**\n * @typedef {import('./lib/index.js').Options} Options\n */\n\nexport {\n  defaultFootnoteBackContent,\n  defaultFootnoteBackLabel,\n  defaultHandlers\n} from 'mdast-util-to-hast'\nexport {default} from './lib/index.js'\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SACEA,0BAA0B,EAC1BC,wBAAwB,EACxBC,eAAe,QACV,oBAAoB;AAC3B,SAAQC,OAAO,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}