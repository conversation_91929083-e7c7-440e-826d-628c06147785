{"ast": null, "code": "export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;", "map": {"version": 3, "names": [], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/uuid/dist/esm-browser/regex.js"], "sourcesContent": ["export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;\n"], "mappings": "AAAA,eAAe,0JAA0J", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}