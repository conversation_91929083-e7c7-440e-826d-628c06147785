{"ast": null, "code": "/**\n * @import {\n *   Create,\n *   FullNormalizedExtension,\n *   InitialConstruct,\n *   ParseContext,\n *   ParseOptions\n * } from 'micromark-util-types'\n */\n\nimport { combineExtensions } from 'micromark-util-combine-extensions';\nimport { content } from './initialize/content.js';\nimport { document } from './initialize/document.js';\nimport { flow } from './initialize/flow.js';\nimport { string, text } from './initialize/text.js';\nimport * as defaultConstructs from './constructs.js';\nimport { createTokenizer } from './create-tokenizer.js';\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ParseContext}\n *   Parser.\n */\nexport function parse(options) {\n  const settings = options || {};\n  const constructs = /** @type {FullNormalizedExtension} */\n  combineExtensions([defaultConstructs, ...(settings.extensions || [])]);\n\n  /** @type {ParseContext} */\n  const parser = {\n    constructs,\n    content: create(content),\n    defined: [],\n    document: create(document),\n    flow: create(flow),\n    lazy: {},\n    string: create(string),\n    text: create(text)\n  };\n  return parser;\n\n  /**\n   * @param {InitialConstruct} initial\n   *   Construct to start with.\n   * @returns {Create}\n   *   Create a tokenizer.\n   */\n  function create(initial) {\n    return creator;\n    /** @type {Create} */\n    function creator(from) {\n      return createTokenizer(parser, initial, from);\n    }\n  }\n}", "map": {"version": 3, "names": ["combineExtensions", "content", "document", "flow", "string", "text", "defaultConstructs", "createTokenizer", "parse", "options", "settings", "constructs", "extensions", "parser", "create", "defined", "lazy", "initial", "creator", "from"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark/dev/lib/parse.js"], "sourcesContent": ["/**\n * @import {\n *   Create,\n *   FullNormalizedExtension,\n *   InitialConstruct,\n *   ParseContext,\n *   ParseOptions\n * } from 'micromark-util-types'\n */\n\nimport {combineExtensions} from 'micromark-util-combine-extensions'\nimport {content} from './initialize/content.js'\nimport {document} from './initialize/document.js'\nimport {flow} from './initialize/flow.js'\nimport {string, text} from './initialize/text.js'\nimport * as defaultConstructs from './constructs.js'\nimport {createTokenizer} from './create-tokenizer.js'\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ParseContext}\n *   Parser.\n */\nexport function parse(options) {\n  const settings = options || {}\n  const constructs = /** @type {FullNormalizedExtension} */ (\n    combineExtensions([defaultConstructs, ...(settings.extensions || [])])\n  )\n\n  /** @type {ParseContext} */\n  const parser = {\n    constructs,\n    content: create(content),\n    defined: [],\n    document: create(document),\n    flow: create(flow),\n    lazy: {},\n    string: create(string),\n    text: create(text)\n  }\n\n  return parser\n\n  /**\n   * @param {InitialConstruct} initial\n   *   Construct to start with.\n   * @returns {Create}\n   *   Create a tokenizer.\n   */\n  function create(initial) {\n    return creator\n    /** @type {Create} */\n    function creator(from) {\n      return createTokenizer(parser, initial, from)\n    }\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,iBAAiB,QAAO,mCAAmC;AACnE,SAAQC,OAAO,QAAO,yBAAyB;AAC/C,SAAQC,QAAQ,QAAO,0BAA0B;AACjD,SAAQC,IAAI,QAAO,sBAAsB;AACzC,SAAQC,MAAM,EAAEC,IAAI,QAAO,sBAAsB;AACjD,OAAO,KAAKC,iBAAiB,MAAM,iBAAiB;AACpD,SAAQC,eAAe,QAAO,uBAAuB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,OAAO,EAAE;EAC7B,MAAMC,QAAQ,GAAGD,OAAO,IAAI,CAAC,CAAC;EAC9B,MAAME,UAAU,GAAG;EACjBX,iBAAiB,CAAC,CAACM,iBAAiB,EAAE,IAAII,QAAQ,CAACE,UAAU,IAAI,EAAE,CAAC,CAAC,CACtE;;EAED;EACA,MAAMC,MAAM,GAAG;IACbF,UAAU;IACVV,OAAO,EAAEa,MAAM,CAACb,OAAO,CAAC;IACxBc,OAAO,EAAE,EAAE;IACXb,QAAQ,EAAEY,MAAM,CAACZ,QAAQ,CAAC;IAC1BC,IAAI,EAAEW,MAAM,CAACX,IAAI,CAAC;IAClBa,IAAI,EAAE,CAAC,CAAC;IACRZ,MAAM,EAAEU,MAAM,CAACV,MAAM,CAAC;IACtBC,IAAI,EAAES,MAAM,CAACT,IAAI;EACnB,CAAC;EAED,OAAOQ,MAAM;;EAEb;AACF;AACA;AACA;AACA;AACA;EACE,SAASC,MAAMA,CAACG,OAAO,EAAE;IACvB,OAAOC,OAAO;IACd;IACA,SAASA,OAAOA,CAACC,IAAI,EAAE;MACrB,OAAOZ,eAAe,CAACM,MAAM,EAAEI,OAAO,EAAEE,IAAI,CAAC;IAC/C;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}