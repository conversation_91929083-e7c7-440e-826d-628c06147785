{"ast": null, "code": "/**\n * @import {Info, Space} from 'property-information'\n */\n\nimport { Schema } from './schema.js';\n\n/**\n * @param {ReadonlyArray<Schema>} definitions\n *   Definitions.\n * @param {Space | undefined} [space]\n *   Space.\n * @returns {Schema}\n *   Schema.\n */\nexport function merge(definitions, space) {\n  /** @type {Record<string, Info>} */\n  const property = {};\n  /** @type {Record<string, string>} */\n  const normal = {};\n  for (const definition of definitions) {\n    Object.assign(property, definition.property);\n    Object.assign(normal, definition.normal);\n  }\n  return new Schema(property, normal, space);\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "merge", "definitions", "space", "property", "normal", "definition", "Object", "assign"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/property-information/lib/util/merge.js"], "sourcesContent": ["/**\n * @import {Info, Space} from 'property-information'\n */\n\nimport {Schema} from './schema.js'\n\n/**\n * @param {ReadonlyArray<Schema>} definitions\n *   Definitions.\n * @param {Space | undefined} [space]\n *   Space.\n * @returns {Schema}\n *   Schema.\n */\nexport function merge(definitions, space) {\n  /** @type {Record<string, Info>} */\n  const property = {}\n  /** @type {Record<string, string>} */\n  const normal = {}\n\n  for (const definition of definitions) {\n    Object.assign(property, definition.property)\n    Object.assign(normal, definition.normal)\n  }\n\n  return new Schema(property, normal, space)\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAAQA,MAAM,QAAO,aAAa;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,WAAW,EAAEC,KAAK,EAAE;EACxC;EACA,MAAMC,QAAQ,GAAG,CAAC,CAAC;EACnB;EACA,MAAMC,MAAM,GAAG,CAAC,CAAC;EAEjB,KAAK,MAAMC,UAAU,IAAIJ,WAAW,EAAE;IACpCK,MAAM,CAACC,MAAM,CAACJ,QAAQ,EAAEE,UAAU,CAACF,QAAQ,CAAC;IAC5CG,MAAM,CAACC,MAAM,CAACH,MAAM,EAAEC,UAAU,CAACD,MAAM,CAAC;EAC1C;EAEA,OAAO,IAAIL,MAAM,CAACI,QAAQ,EAAEC,MAAM,EAAEF,KAAK,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}