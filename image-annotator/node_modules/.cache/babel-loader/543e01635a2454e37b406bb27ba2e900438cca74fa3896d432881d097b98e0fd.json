{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */\n\nimport { normalizeUri } from 'micromark-util-sanitize-uri';\nimport { revert } from '../revert.js';\n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nexport function imageReference(state, node) {\n  const id = String(node.identifier).toUpperCase();\n  const definition = state.definitionById.get(id);\n  if (!definition) {\n    return revert(state, node);\n  }\n\n  /** @type {Properties} */\n  const properties = {\n    src: normalizeUri(definition.url || ''),\n    alt: node.alt\n  };\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title;\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'img',\n    properties,\n    children: []\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["normalizeUri", "revert", "imageReference", "state", "node", "id", "String", "identifier", "toUpperCase", "definition", "definitionById", "get", "properties", "src", "url", "alt", "title", "undefined", "result", "type", "tagName", "children", "patch", "applyData"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/mdast-util-to-hast/lib/handlers/image-reference.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\nimport {revert} from '../revert.js'\n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nexport function imageReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const definition = state.definitionById.get(id)\n\n  if (!definition) {\n    return revert(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {src: normalizeUri(definition.url || ''), alt: node.alt}\n\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,6BAA6B;AACxD,SAAQC,MAAM,QAAO,cAAc;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC1C,MAAMC,EAAE,GAAGC,MAAM,CAACF,IAAI,CAACG,UAAU,CAAC,CAACC,WAAW,CAAC,CAAC;EAChD,MAAMC,UAAU,GAAGN,KAAK,CAACO,cAAc,CAACC,GAAG,CAACN,EAAE,CAAC;EAE/C,IAAI,CAACI,UAAU,EAAE;IACf,OAAOR,MAAM,CAACE,KAAK,EAAEC,IAAI,CAAC;EAC5B;;EAEA;EACA,MAAMQ,UAAU,GAAG;IAACC,GAAG,EAAEb,YAAY,CAACS,UAAU,CAACK,GAAG,IAAI,EAAE,CAAC;IAAEC,GAAG,EAAEX,IAAI,CAACW;EAAG,CAAC;EAE3E,IAAIN,UAAU,CAACO,KAAK,KAAK,IAAI,IAAIP,UAAU,CAACO,KAAK,KAAKC,SAAS,EAAE;IAC/DL,UAAU,CAACI,KAAK,GAAGP,UAAU,CAACO,KAAK;EACrC;;EAEA;EACA,MAAME,MAAM,GAAG;IAACC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,KAAK;IAAER,UAAU;IAAES,QAAQ,EAAE;EAAE,CAAC;EAC1ElB,KAAK,CAACmB,KAAK,CAAClB,IAAI,EAAEc,MAAM,CAAC;EACzB,OAAOf,KAAK,CAACoB,SAAS,CAACnB,IAAI,EAAEc,MAAM,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}