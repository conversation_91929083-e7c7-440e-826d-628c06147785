{"ast": null, "code": "import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n  return (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset);\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n  return uuid;\n}\nexport default stringify;", "map": {"version": 3, "names": ["validate", "byteToHex", "i", "push", "toString", "slice", "unsafeStringify", "arr", "offset", "toLowerCase", "stringify", "uuid", "TypeError"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/uuid/dist/esm-browser/stringify.js"], "sourcesContent": ["import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,MAAMC,SAAS,GAAG,EAAE;AACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAAE;EAC1BD,SAAS,CAACE,IAAI,CAAC,CAACD,CAAC,GAAG,KAAK,EAAEE,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;AACrD;AACA,OAAO,SAASC,eAAeA,CAACC,GAAG,EAAEC,MAAM,GAAG,CAAC,EAAE;EAC7C,OAAO,CAACP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAC9BP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAC1BP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAC1BP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAC1B,GAAG,GACHP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAC1BP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAC1B,GAAG,GACHP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAC1BP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAC1B,GAAG,GACHP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAC1BP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAC1B,GAAG,GACHP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAC3BP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAC3BP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAC3BP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAC3BP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAC3BP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,EAAEC,WAAW,CAAC,CAAC;AAClD;AACA,SAASC,SAASA,CAACH,GAAG,EAAEC,MAAM,GAAG,CAAC,EAAE;EAChC,MAAMG,IAAI,GAAGL,eAAe,CAACC,GAAG,EAAEC,MAAM,CAAC;EACzC,IAAI,CAACR,QAAQ,CAACW,IAAI,CAAC,EAAE;IACjB,MAAMC,SAAS,CAAC,6BAA6B,CAAC;EAClD;EACA,OAAOD,IAAI;AACf;AACA,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}