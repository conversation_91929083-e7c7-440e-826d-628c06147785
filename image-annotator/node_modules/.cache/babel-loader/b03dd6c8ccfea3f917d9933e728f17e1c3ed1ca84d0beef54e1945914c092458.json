{"ast": null, "code": "/**\n * @import {\n *   Extension,\n *   Handles,\n *   HtmlExtension,\n *   NormalizedExtension\n * } from 'micromark-util-types'\n */\n\nimport { splice } from 'micromark-util-chunked';\nconst hasOwnProperty = {}.hasOwnProperty;\n\n/**\n * Combine multiple syntax extensions into one.\n *\n * @param {ReadonlyArray<Extension>} extensions\n *   List of syntax extensions.\n * @returns {NormalizedExtension}\n *   A single combined extension.\n */\nexport function combineExtensions(extensions) {\n  /** @type {NormalizedExtension} */\n  const all = {};\n  let index = -1;\n  while (++index < extensions.length) {\n    syntaxExtension(all, extensions[index]);\n  }\n  return all;\n}\n\n/**\n * Merge `extension` into `all`.\n *\n * @param {NormalizedExtension} all\n *   Extension to merge into.\n * @param {Extension} extension\n *   Extension to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction syntaxExtension(all, extension) {\n  /** @type {keyof Extension} */\n  let hook;\n  for (hook in extension) {\n    const maybe = hasOwnProperty.call(all, hook) ? all[hook] : undefined;\n    /** @type {Record<string, unknown>} */\n    const left = maybe || (all[hook] = {});\n    /** @type {Record<string, unknown> | undefined} */\n    const right = extension[hook];\n    /** @type {string} */\n    let code;\n    if (right) {\n      for (code in right) {\n        if (!hasOwnProperty.call(left, code)) left[code] = [];\n        const value = right[code];\n        constructs(\n        // @ts-expect-error Looks like a list.\n        left[code], Array.isArray(value) ? value : value ? [value] : []);\n      }\n    }\n  }\n}\n\n/**\n * Merge `list` into `existing` (both lists of constructs).\n * Mutates `existing`.\n *\n * @param {Array<unknown>} existing\n *   List of constructs to merge into.\n * @param {Array<unknown>} list\n *   List of constructs to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction constructs(existing, list) {\n  let index = -1;\n  /** @type {Array<unknown>} */\n  const before = [];\n  while (++index < list.length) {\n    // @ts-expect-error Looks like an object.\n    ;\n    (list[index].add === 'after' ? existing : before).push(list[index]);\n  }\n  splice(existing, 0, 0, before);\n}\n\n/**\n * Combine multiple HTML extensions into one.\n *\n * @param {ReadonlyArray<HtmlExtension>} htmlExtensions\n *   List of HTML extensions.\n * @returns {HtmlExtension}\n *   Single combined HTML extension.\n */\nexport function combineHtmlExtensions(htmlExtensions) {\n  /** @type {HtmlExtension} */\n  const handlers = {};\n  let index = -1;\n  while (++index < htmlExtensions.length) {\n    htmlExtension(handlers, htmlExtensions[index]);\n  }\n  return handlers;\n}\n\n/**\n * Merge `extension` into `all`.\n *\n * @param {HtmlExtension} all\n *   Extension to merge into.\n * @param {HtmlExtension} extension\n *   Extension to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction htmlExtension(all, extension) {\n  /** @type {keyof HtmlExtension} */\n  let hook;\n  for (hook in extension) {\n    const maybe = hasOwnProperty.call(all, hook) ? all[hook] : undefined;\n    const left = maybe || (all[hook] = {});\n    const right = extension[hook];\n    /** @type {keyof Handles} */\n    let type;\n    if (right) {\n      for (type in right) {\n        // @ts-expect-error assume document vs regular handler are managed correctly.\n        left[type] = right[type];\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["splice", "hasOwnProperty", "combineExtensions", "extensions", "all", "index", "length", "syntaxExtension", "extension", "hook", "maybe", "call", "undefined", "left", "right", "code", "value", "constructs", "Array", "isArray", "existing", "list", "before", "add", "push", "combineHtmlExtensions", "htmlExtensions", "handlers", "htmlExtension", "type"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark-util-combine-extensions/index.js"], "sourcesContent": ["/**\n * @import {\n *   Extension,\n *   Handles,\n *   HtmlExtension,\n *   NormalizedExtension\n * } from 'micromark-util-types'\n */\n\nimport {splice} from 'micromark-util-chunked'\n\nconst hasOwnProperty = {}.hasOwnProperty\n\n/**\n * Combine multiple syntax extensions into one.\n *\n * @param {ReadonlyArray<Extension>} extensions\n *   List of syntax extensions.\n * @returns {NormalizedExtension}\n *   A single combined extension.\n */\nexport function combineExtensions(extensions) {\n  /** @type {NormalizedExtension} */\n  const all = {}\n  let index = -1\n\n  while (++index < extensions.length) {\n    syntaxExtension(all, extensions[index])\n  }\n\n  return all\n}\n\n/**\n * Merge `extension` into `all`.\n *\n * @param {NormalizedExtension} all\n *   Extension to merge into.\n * @param {Extension} extension\n *   Extension to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction syntaxExtension(all, extension) {\n  /** @type {keyof Extension} */\n  let hook\n\n  for (hook in extension) {\n    const maybe = hasOwnProperty.call(all, hook) ? all[hook] : undefined\n    /** @type {Record<string, unknown>} */\n    const left = maybe || (all[hook] = {})\n    /** @type {Record<string, unknown> | undefined} */\n    const right = extension[hook]\n    /** @type {string} */\n    let code\n\n    if (right) {\n      for (code in right) {\n        if (!hasOwnProperty.call(left, code)) left[code] = []\n        const value = right[code]\n        constructs(\n          // @ts-expect-error Looks like a list.\n          left[code],\n          Array.isArray(value) ? value : value ? [value] : []\n        )\n      }\n    }\n  }\n}\n\n/**\n * Merge `list` into `existing` (both lists of constructs).\n * Mutates `existing`.\n *\n * @param {Array<unknown>} existing\n *   List of constructs to merge into.\n * @param {Array<unknown>} list\n *   List of constructs to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction constructs(existing, list) {\n  let index = -1\n  /** @type {Array<unknown>} */\n  const before = []\n\n  while (++index < list.length) {\n    // @ts-expect-error Looks like an object.\n    ;(list[index].add === 'after' ? existing : before).push(list[index])\n  }\n\n  splice(existing, 0, 0, before)\n}\n\n/**\n * Combine multiple HTML extensions into one.\n *\n * @param {ReadonlyArray<HtmlExtension>} htmlExtensions\n *   List of HTML extensions.\n * @returns {HtmlExtension}\n *   Single combined HTML extension.\n */\nexport function combineHtmlExtensions(htmlExtensions) {\n  /** @type {HtmlExtension} */\n  const handlers = {}\n  let index = -1\n\n  while (++index < htmlExtensions.length) {\n    htmlExtension(handlers, htmlExtensions[index])\n  }\n\n  return handlers\n}\n\n/**\n * Merge `extension` into `all`.\n *\n * @param {HtmlExtension} all\n *   Extension to merge into.\n * @param {HtmlExtension} extension\n *   Extension to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction htmlExtension(all, extension) {\n  /** @type {keyof HtmlExtension} */\n  let hook\n\n  for (hook in extension) {\n    const maybe = hasOwnProperty.call(all, hook) ? all[hook] : undefined\n    const left = maybe || (all[hook] = {})\n    const right = extension[hook]\n    /** @type {keyof Handles} */\n    let type\n\n    if (right) {\n      for (type in right) {\n        // @ts-expect-error assume document vs regular handler are managed correctly.\n        left[type] = right[type]\n      }\n    }\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,MAAM,QAAO,wBAAwB;AAE7C,MAAMC,cAAc,GAAG,CAAC,CAAC,CAACA,cAAc;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,UAAU,EAAE;EAC5C;EACA,MAAMC,GAAG,GAAG,CAAC,CAAC;EACd,IAAIC,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAGF,UAAU,CAACG,MAAM,EAAE;IAClCC,eAAe,CAACH,GAAG,EAAED,UAAU,CAACE,KAAK,CAAC,CAAC;EACzC;EAEA,OAAOD,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,eAAeA,CAACH,GAAG,EAAEI,SAAS,EAAE;EACvC;EACA,IAAIC,IAAI;EAER,KAAKA,IAAI,IAAID,SAAS,EAAE;IACtB,MAAME,KAAK,GAAGT,cAAc,CAACU,IAAI,CAACP,GAAG,EAAEK,IAAI,CAAC,GAAGL,GAAG,CAACK,IAAI,CAAC,GAAGG,SAAS;IACpE;IACA,MAAMC,IAAI,GAAGH,KAAK,KAAKN,GAAG,CAACK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACtC;IACA,MAAMK,KAAK,GAAGN,SAAS,CAACC,IAAI,CAAC;IAC7B;IACA,IAAIM,IAAI;IAER,IAAID,KAAK,EAAE;MACT,KAAKC,IAAI,IAAID,KAAK,EAAE;QAClB,IAAI,CAACb,cAAc,CAACU,IAAI,CAACE,IAAI,EAAEE,IAAI,CAAC,EAAEF,IAAI,CAACE,IAAI,CAAC,GAAG,EAAE;QACrD,MAAMC,KAAK,GAAGF,KAAK,CAACC,IAAI,CAAC;QACzBE,UAAU;QACR;QACAJ,IAAI,CAACE,IAAI,CAAC,EACVG,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC,GAAG,EACnD,CAAC;MACH;IACF;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACG,QAAQ,EAAEC,IAAI,EAAE;EAClC,IAAIhB,KAAK,GAAG,CAAC,CAAC;EACd;EACA,MAAMiB,MAAM,GAAG,EAAE;EAEjB,OAAO,EAAEjB,KAAK,GAAGgB,IAAI,CAACf,MAAM,EAAE;IAC5B;IACA;IAAC,CAACe,IAAI,CAAChB,KAAK,CAAC,CAACkB,GAAG,KAAK,OAAO,GAAGH,QAAQ,GAAGE,MAAM,EAAEE,IAAI,CAACH,IAAI,CAAChB,KAAK,CAAC,CAAC;EACtE;EAEAL,MAAM,CAACoB,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAEE,MAAM,CAAC;AAChC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,qBAAqBA,CAACC,cAAc,EAAE;EACpD;EACA,MAAMC,QAAQ,GAAG,CAAC,CAAC;EACnB,IAAItB,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAGqB,cAAc,CAACpB,MAAM,EAAE;IACtCsB,aAAa,CAACD,QAAQ,EAAED,cAAc,CAACrB,KAAK,CAAC,CAAC;EAChD;EAEA,OAAOsB,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACxB,GAAG,EAAEI,SAAS,EAAE;EACrC;EACA,IAAIC,IAAI;EAER,KAAKA,IAAI,IAAID,SAAS,EAAE;IACtB,MAAME,KAAK,GAAGT,cAAc,CAACU,IAAI,CAACP,GAAG,EAAEK,IAAI,CAAC,GAAGL,GAAG,CAACK,IAAI,CAAC,GAAGG,SAAS;IACpE,MAAMC,IAAI,GAAGH,KAAK,KAAKN,GAAG,CAACK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACtC,MAAMK,KAAK,GAAGN,SAAS,CAACC,IAAI,CAAC;IAC7B;IACA,IAAIoB,IAAI;IAER,IAAIf,KAAK,EAAE;MACT,KAAKe,IAAI,IAAIf,KAAK,EAAE;QAClB;QACAD,IAAI,CAACgB,IAAI,CAAC,GAAGf,KAAK,CAACe,IAAI,CAAC;MAC1B;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}