{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/pages/HomePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useImageContext } from '../contexts/ImageContext';\nimport CollectionTree from '../components/CollectionTree';\nimport ImageUpload from '../components/ImageUpload';\nimport './HomePage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    collections,\n    images,\n    deleteImage\n  } = useImageContext();\n  const [selectedCollectionId, setSelectedCollectionId] = useState('root');\n  const handleImageClick = imageId => {\n    navigate(`/image/${imageId}`);\n  };\n  const getImagesInCollection = collectionId => {\n    return images.filter(img => img.collectionId === collectionId);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"home-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Image Annotator\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"T\\u1EA1o ghi ch\\xFA chi ti\\u1EBFt cho t\\u1EEBng v\\xF9ng nh\\u1ECF tr\\xEAn \\u1EA3nh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"home-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"collection-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"B\\u1ED9 s\\u01B0u t\\u1EADp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CollectionTree, {\n            collections: collections,\n            selectedId: selectedCollectionId,\n            onSelect: setSelectedCollectionId\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"upload-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"T\\u1EA3i \\u1EA3nh l\\xEAn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ImageUpload, {\n            collectionId: selectedCollectionId\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"images-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u1EA2nh trong b\\u1ED9 s\\u01B0u t\\u1EADp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-grid\",\n            children: getImagesInCollection(selectedCollectionId).map(image => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"image-card\",\n              onClick: () => handleImageClick(image.id),\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: image.url,\n                alt: image.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"image-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: image.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [image.annotations.length, \" ghi ch\\xFA\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 58,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 19\n              }, this)]\n            }, image.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"sP7kSH6CZmvAs2a+/mrNUlZXUXE=\", false, function () {\n  return [useNavigate, useImageContext];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useImageContext", "CollectionTree", "ImageUpload", "jsxDEV", "_jsxDEV", "HomePage", "_s", "navigate", "collections", "images", "deleteImage", "selectedCollectionId", "setSelectedCollectionId", "handleImageClick", "imageId", "getImagesInCollection", "collectionId", "filter", "img", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selectedId", "onSelect", "map", "image", "onClick", "id", "src", "url", "alt", "name", "annotations", "length", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/pages/HomePage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useImageContext } from '../contexts/ImageContext';\nimport CollectionTree from '../components/CollectionTree';\nimport ImageUpload from '../components/ImageUpload';\nimport './HomePage.css';\n\nconst HomePage: React.FC = () => {\n  const navigate = useNavigate();\n  const { collections, images, deleteImage } = useImageContext();\n  const [selectedCollectionId, setSelectedCollectionId] = useState<string>('root');\n\n  const handleImageClick = (imageId: string) => {\n    navigate(`/image/${imageId}`);\n  };\n\n  const getImagesInCollection = (collectionId: string) => {\n    return images.filter(img => img.collectionId === collectionId);\n  };\n\n  return (\n    <div className=\"home-page\">\n      <header className=\"home-header\">\n        <h1>Image Annotator</h1>\n        <p>Tạo ghi chú chi tiết cho từng vùng nhỏ trên ảnh</p>\n      </header>\n\n      <div className=\"home-content\">\n        <div className=\"sidebar\">\n          <div className=\"collection-section\">\n            <h3>Bộ sưu tập</h3>\n            <CollectionTree \n              collections={collections}\n              selectedId={selectedCollectionId}\n              onSelect={setSelectedCollectionId}\n            />\n          </div>\n          \n          <div className=\"upload-section\">\n            <h3>Tải ảnh lên</h3>\n            <ImageUpload collectionId={selectedCollectionId} />\n          </div>\n        </div>\n\n        <div className=\"main-content\">\n          <div className=\"images-grid\">\n            <h3>Ảnh trong bộ sưu tập</h3>\n            <div className=\"image-grid\">\n              {getImagesInCollection(selectedCollectionId).map(image => (\n                <div \n                  key={image.id} \n                  className=\"image-card\"\n                  onClick={() => handleImageClick(image.id)}\n                >\n                  <img src={image.url} alt={image.name} />\n                  <div className=\"image-info\">\n                    <h4>{image.name}</h4>\n                    <p>{image.annotations.length} ghi chú</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,WAAW;IAAEC,MAAM;IAAEC;EAAY,CAAC,GAAGV,eAAe,CAAC,CAAC;EAC9D,MAAM,CAACW,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGd,QAAQ,CAAS,MAAM,CAAC;EAEhF,MAAMe,gBAAgB,GAAIC,OAAe,IAAK;IAC5CP,QAAQ,CAAC,UAAUO,OAAO,EAAE,CAAC;EAC/B,CAAC;EAED,MAAMC,qBAAqB,GAAIC,YAAoB,IAAK;IACtD,OAAOP,MAAM,CAACQ,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACF,YAAY,KAAKA,YAAY,CAAC;EAChE,CAAC;EAED,oBACEZ,OAAA;IAAKe,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBhB,OAAA;MAAQe,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC7BhB,OAAA;QAAAgB,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBpB,OAAA;QAAAgB,QAAA,EAAG;MAA+C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,eAETpB,OAAA;MAAKe,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BhB,OAAA;QAAKe,SAAS,EAAC,SAAS;QAAAC,QAAA,gBACtBhB,OAAA;UAAKe,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjChB,OAAA;YAAAgB,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBpB,OAAA,CAACH,cAAc;YACbO,WAAW,EAAEA,WAAY;YACzBiB,UAAU,EAAEd,oBAAqB;YACjCe,QAAQ,EAAEd;UAAwB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpB,OAAA;UAAKe,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhB,OAAA;YAAAgB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBpB,OAAA,CAACF,WAAW;YAACc,YAAY,EAAEL;UAAqB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA;QAAKe,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BhB,OAAA;UAAKe,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BhB,OAAA;YAAAgB,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BpB,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,EACxBL,qBAAqB,CAACJ,oBAAoB,CAAC,CAACgB,GAAG,CAACC,KAAK,iBACpDxB,OAAA;cAEEe,SAAS,EAAC,YAAY;cACtBU,OAAO,EAAEA,CAAA,KAAMhB,gBAAgB,CAACe,KAAK,CAACE,EAAE,CAAE;cAAAV,QAAA,gBAE1ChB,OAAA;gBAAK2B,GAAG,EAAEH,KAAK,CAACI,GAAI;gBAACC,GAAG,EAAEL,KAAK,CAACM;cAAK;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxCpB,OAAA;gBAAKe,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhB,OAAA;kBAAAgB,QAAA,EAAKQ,KAAK,CAACM;gBAAI;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrBpB,OAAA;kBAAAgB,QAAA,GAAIQ,KAAK,CAACO,WAAW,CAACC,MAAM,EAAC,aAAQ;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA,GARDI,KAAK,CAACE,EAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CA5DID,QAAkB;EAAA,QACLN,WAAW,EACiBC,eAAe;AAAA;AAAAqC,EAAA,GAFxDhC,QAAkB;AA8DxB,eAAeA,QAAQ;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}