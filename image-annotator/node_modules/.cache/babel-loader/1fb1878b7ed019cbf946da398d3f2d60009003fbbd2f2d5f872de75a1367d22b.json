{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/pages/ImageDetailPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useImageContext } from '../contexts/ImageContext';\nimport AnnotationViewer from '../components/AnnotationViewer';\nimport AnnotationEditor from '../components/AnnotationEditor';\nimport './ImageDetailPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageDetailPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    getImageById,\n    addAnnotation,\n    updateAnnotation,\n    deleteAnnotation\n  } = useImageContext();\n  const [isCreatingAnnotation, setIsCreatingAnnotation] = useState(false);\n  const [selectedAnnotation, setSelectedAnnotation] = useState(null);\n  const [hoveredAnnotation, setHoveredAnnotation] = useState(null);\n  const [startPoint, setStartPoint] = useState(null);\n  const [currentRect, setCurrentRect] = useState(null);\n  const imageRef = useRef(null);\n  const containerRef = useRef(null);\n  const image = id ? getImageById(id) : undefined;\n  useEffect(() => {\n    if (!image) {\n      navigate('/');\n    }\n  }, [image, navigate]);\n  if (!image) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Kh\\xF4ng t\\xECm th\\u1EA5y \\u1EA3nh\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 12\n    }, this);\n  }\n  const getImageCoordinates = (clientX, clientY) => {\n    if (!imageRef.current || !containerRef.current) return null;\n    const imageRect = imageRef.current.getBoundingClientRect();\n    const containerRect = containerRef.current.getBoundingClientRect();\n\n    // Calculate coordinates relative to the image element\n    const x = clientX - imageRect.left;\n    const y = clientY - imageRect.top;\n\n    // Convert to relative coordinates (0-1) based on actual image dimensions\n    const relativeX = x / imageRect.width;\n    const relativeY = y / imageRect.height;\n\n    // Ensure coordinates are within bounds\n    const clampedX = Math.max(0, Math.min(1, relativeX));\n    const clampedY = Math.max(0, Math.min(1, relativeY));\n    return {\n      x: clampedX,\n      y: clampedY\n    };\n  };\n  const handleMouseDown = e => {\n    if (!isCreatingAnnotation) return;\n    const coords = getImageCoordinates(e.clientX, e.clientY);\n    if (coords) {\n      setStartPoint(coords);\n    }\n  };\n  const handleMouseMove = e => {\n    if (!isCreatingAnnotation || !startPoint) return;\n    const coords = getImageCoordinates(e.clientX, e.clientY);\n    if (coords) {\n      const width = Math.abs(coords.x - startPoint.x);\n      const height = Math.abs(coords.y - startPoint.y);\n      const x = Math.min(startPoint.x, coords.x);\n      const y = Math.min(startPoint.y, coords.y);\n      setCurrentRect({\n        x,\n        y,\n        width,\n        height\n      });\n    }\n  };\n  const handleMouseUp = e => {\n    if (!isCreatingAnnotation || !startPoint || !currentRect) return;\n\n    // Only create annotation if rectangle is large enough\n    if (currentRect.width > 0.01 && currentRect.height > 0.01) {\n      const newAnnotation = {\n        x: currentRect.x,\n        y: currentRect.y,\n        width: currentRect.width,\n        height: currentRect.height,\n        content: '# Ghi chú mới\\n\\nNhập nội dung markdown tại đây...',\n        title: 'Ghi chú mới'\n      };\n      addAnnotation(image.id, newAnnotation);\n    }\n    setIsCreatingAnnotation(false);\n    setStartPoint(null);\n    setCurrentRect(null);\n  };\n  const handleAnnotationClick = annotation => {\n    setSelectedAnnotation(annotation);\n  };\n  const handleAnnotationSave = (content, title) => {\n    if (selectedAnnotation) {\n      updateAnnotation(image.id, selectedAnnotation.id, {\n        content,\n        title\n      });\n      setSelectedAnnotation(null);\n    }\n  };\n  const handleAnnotationDelete = () => {\n    if (selectedAnnotation) {\n      deleteAnnotation(image.id, selectedAnnotation.id);\n      setSelectedAnnotation(null);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"image-detail-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"detail-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/'),\n        className: \"back-button\",\n        children: \"\\u2190 Quay l\\u1EA1i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: image.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `create-annotation-btn ${isCreatingAnnotation ? 'active' : ''}`,\n          onClick: () => setIsCreatingAnnotation(!isCreatingAnnotation),\n          children: isCreatingAnnotation ? 'Hủy tạo vùng' : 'Tạo vùng ghi chú'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"detail-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"image-container\",\n        ref: containerRef,\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          ref: imageRef,\n          src: image.url,\n          alt: image.name,\n          className: `main-image ${isCreatingAnnotation ? 'creating' : ''}`,\n          onMouseDown: handleMouseDown,\n          onMouseMove: handleMouseMove,\n          onMouseUp: handleMouseUp,\n          draggable: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), image.annotations.map(annotation => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"annotation-overlay\",\n          style: {\n            left: `${annotation.x * 100}%`,\n            top: `${annotation.y * 100}%`,\n            width: `${annotation.width * 100}%`,\n            height: `${annotation.height * 100}%`\n          },\n          onClick: () => handleAnnotationClick(annotation),\n          onMouseEnter: () => setHoveredAnnotation(annotation),\n          onMouseLeave: () => setHoveredAnnotation(null)\n        }, annotation.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this)), currentRect && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"annotation-overlay creating\",\n          style: {\n            left: `${currentRect.x * 100}%`,\n            top: `${currentRect.y * 100}%`,\n            width: `${currentRect.width * 100}%`,\n            height: `${currentRect.height * 100}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), hoveredAnnotation && !selectedAnnotation && /*#__PURE__*/_jsxDEV(AnnotationViewer, {\n        annotation: hoveredAnnotation,\n        position: \"left\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this), selectedAnnotation && /*#__PURE__*/_jsxDEV(AnnotationEditor, {\n        annotation: selectedAnnotation,\n        onSave: handleAnnotationSave,\n        onDelete: handleAnnotationDelete,\n        onClose: () => setSelectedAnnotation(null)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this);\n};\n_s(ImageDetailPage, \"DsEpB+IVmI9U4w7rmomCs7HnPTw=\", false, function () {\n  return [useParams, useNavigate, useImageContext];\n});\n_c = ImageDetailPage;\nexport default ImageDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ImageDetailPage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useParams", "useNavigate", "useImageContext", "AnnotationViewer", "AnnotationEditor", "jsxDEV", "_jsxDEV", "ImageDetailPage", "_s", "id", "navigate", "getImageById", "addAnnotation", "updateAnnotation", "deleteAnnotation", "isCreatingAnnotation", "setIsCreatingAnnotation", "selectedAnnotation", "setSelectedAnnotation", "hoveredAnnotation", "setHoveredAnnotation", "startPoint", "setStartPoint", "currentRect", "setCurrentRect", "imageRef", "containerRef", "image", "undefined", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getImageCoordinates", "clientX", "clientY", "current", "imageRect", "getBoundingClientRect", "containerRect", "x", "left", "y", "top", "relativeX", "width", "relativeY", "height", "clampedX", "Math", "max", "min", "clampedY", "handleMouseDown", "e", "coords", "handleMouseMove", "abs", "handleMouseUp", "newAnnotation", "content", "title", "handleAnnotationClick", "annotation", "handleAnnotationSave", "handleAnnotationDelete", "className", "onClick", "name", "ref", "src", "url", "alt", "onMouseDown", "onMouseMove", "onMouseUp", "draggable", "annotations", "map", "style", "onMouseEnter", "onMouseLeave", "position", "onSave", "onDelete", "onClose", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/pages/ImageDetailPage.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useImageContext } from '../contexts/ImageContext';\nimport AnnotationViewer from '../components/AnnotationViewer';\nimport AnnotationEditor from '../components/AnnotationEditor';\nimport { Annotation, Point } from '../types';\nimport './ImageDetailPage.css';\n\nconst ImageDetailPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const { getImageById, addAnnotation, updateAnnotation, deleteAnnotation } = useImageContext();\n  const [isCreatingAnnotation, setIsCreatingAnnotation] = useState(false);\n  const [selectedAnnotation, setSelectedAnnotation] = useState<Annotation | null>(null);\n  const [hoveredAnnotation, setHoveredAnnotation] = useState<Annotation | null>(null);\n  const [startPoint, setStartPoint] = useState<Point | null>(null);\n  const [currentRect, setCurrentRect] = useState<{ x: number; y: number; width: number; height: number } | null>(null);\n  const imageRef = useRef<HTMLImageElement>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  const image = id ? getImageById(id) : undefined;\n\n  useEffect(() => {\n    if (!image) {\n      navigate('/');\n    }\n  }, [image, navigate]);\n\n  if (!image) {\n    return <div>Không tìm thấy ảnh</div>;\n  }\n\n  const getImageCoordinates = (clientX: number, clientY: number) => {\n    if (!imageRef.current || !containerRef.current) return null;\n\n    const imageRect = imageRef.current.getBoundingClientRect();\n    const containerRect = containerRef.current.getBoundingClientRect();\n\n    // Calculate coordinates relative to the image element\n    const x = clientX - imageRect.left;\n    const y = clientY - imageRect.top;\n\n    // Convert to relative coordinates (0-1) based on actual image dimensions\n    const relativeX = x / imageRect.width;\n    const relativeY = y / imageRect.height;\n\n    // Ensure coordinates are within bounds\n    const clampedX = Math.max(0, Math.min(1, relativeX));\n    const clampedY = Math.max(0, Math.min(1, relativeY));\n\n    return { x: clampedX, y: clampedY };\n  };\n\n  const handleMouseDown = (e: React.MouseEvent) => {\n    if (!isCreatingAnnotation) return;\n    \n    const coords = getImageCoordinates(e.clientX, e.clientY);\n    if (coords) {\n      setStartPoint(coords);\n    }\n  };\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    if (!isCreatingAnnotation || !startPoint) return;\n    \n    const coords = getImageCoordinates(e.clientX, e.clientY);\n    if (coords) {\n      const width = Math.abs(coords.x - startPoint.x);\n      const height = Math.abs(coords.y - startPoint.y);\n      const x = Math.min(startPoint.x, coords.x);\n      const y = Math.min(startPoint.y, coords.y);\n      \n      setCurrentRect({ x, y, width, height });\n    }\n  };\n\n  const handleMouseUp = (e: React.MouseEvent) => {\n    if (!isCreatingAnnotation || !startPoint || !currentRect) return;\n    \n    // Only create annotation if rectangle is large enough\n    if (currentRect.width > 0.01 && currentRect.height > 0.01) {\n      const newAnnotation = {\n        x: currentRect.x,\n        y: currentRect.y,\n        width: currentRect.width,\n        height: currentRect.height,\n        content: '# Ghi chú mới\\n\\nNhập nội dung markdown tại đây...',\n        title: 'Ghi chú mới'\n      };\n      \n      addAnnotation(image.id, newAnnotation);\n    }\n    \n    setIsCreatingAnnotation(false);\n    setStartPoint(null);\n    setCurrentRect(null);\n  };\n\n  const handleAnnotationClick = (annotation: Annotation) => {\n    setSelectedAnnotation(annotation);\n  };\n\n  const handleAnnotationSave = (content: string, title?: string) => {\n    if (selectedAnnotation) {\n      updateAnnotation(image.id, selectedAnnotation.id, { content, title });\n      setSelectedAnnotation(null);\n    }\n  };\n\n  const handleAnnotationDelete = () => {\n    if (selectedAnnotation) {\n      deleteAnnotation(image.id, selectedAnnotation.id);\n      setSelectedAnnotation(null);\n    }\n  };\n\n  return (\n    <div className=\"image-detail-page\">\n      <header className=\"detail-header\">\n        <button onClick={() => navigate('/')} className=\"back-button\">\n          ← Quay lại\n        </button>\n        <h1>{image.name}</h1>\n        <div className=\"toolbar\">\n          <button \n            className={`create-annotation-btn ${isCreatingAnnotation ? 'active' : ''}`}\n            onClick={() => setIsCreatingAnnotation(!isCreatingAnnotation)}\n          >\n            {isCreatingAnnotation ? 'Hủy tạo vùng' : 'Tạo vùng ghi chú'}\n          </button>\n        </div>\n      </header>\n\n      <div className=\"detail-content\">\n        <div className=\"image-container\" ref={containerRef}>\n          <img \n            ref={imageRef}\n            src={image.url} \n            alt={image.name}\n            className={`main-image ${isCreatingAnnotation ? 'creating' : ''}`}\n            onMouseDown={handleMouseDown}\n            onMouseMove={handleMouseMove}\n            onMouseUp={handleMouseUp}\n            draggable={false}\n          />\n          \n          {/* Render existing annotations */}\n          {image.annotations.map(annotation => (\n            <div\n              key={annotation.id}\n              className=\"annotation-overlay\"\n              style={{\n                left: `${annotation.x * 100}%`,\n                top: `${annotation.y * 100}%`,\n                width: `${annotation.width * 100}%`,\n                height: `${annotation.height * 100}%`,\n              }}\n              onClick={() => handleAnnotationClick(annotation)}\n              onMouseEnter={() => setHoveredAnnotation(annotation)}\n              onMouseLeave={() => setHoveredAnnotation(null)}\n            />\n          ))}\n          \n          {/* Render current drawing rectangle */}\n          {currentRect && (\n            <div\n              className=\"annotation-overlay creating\"\n              style={{\n                left: `${currentRect.x * 100}%`,\n                top: `${currentRect.y * 100}%`,\n                width: `${currentRect.width * 100}%`,\n                height: `${currentRect.height * 100}%`,\n              }}\n            />\n          )}\n        </div>\n\n        {/* Annotation viewer for hover */}\n        {hoveredAnnotation && !selectedAnnotation && (\n          <AnnotationViewer \n            annotation={hoveredAnnotation}\n            position=\"left\"\n          />\n        )}\n\n        {/* Annotation editor */}\n        {selectedAnnotation && (\n          <AnnotationEditor\n            annotation={selectedAnnotation}\n            onSave={handleAnnotationSave}\n            onDelete={handleAnnotationDelete}\n            onClose={() => setSelectedAnnotation(null)}\n          />\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ImageDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,gBAAgB,MAAM,gCAAgC;AAE7D,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAG,CAAC,GAAGT,SAAS,CAAiB,CAAC;EAC1C,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU,YAAY;IAAEC,aAAa;IAAEC,gBAAgB;IAAEC;EAAiB,CAAC,GAAGZ,eAAe,CAAC,CAAC;EAC7F,MAAM,CAACa,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACoB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrB,QAAQ,CAAoB,IAAI,CAAC;EACrF,MAAM,CAACsB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvB,QAAQ,CAAoB,IAAI,CAAC;EACnF,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAe,IAAI,CAAC;EAChE,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAiE,IAAI,CAAC;EACpH,MAAM4B,QAAQ,GAAG3B,MAAM,CAAmB,IAAI,CAAC;EAC/C,MAAM4B,YAAY,GAAG5B,MAAM,CAAiB,IAAI,CAAC;EAEjD,MAAM6B,KAAK,GAAGlB,EAAE,GAAGE,YAAY,CAACF,EAAE,CAAC,GAAGmB,SAAS;EAE/C7B,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4B,KAAK,EAAE;MACVjB,QAAQ,CAAC,GAAG,CAAC;IACf;EACF,CAAC,EAAE,CAACiB,KAAK,EAAEjB,QAAQ,CAAC,CAAC;EAErB,IAAI,CAACiB,KAAK,EAAE;IACV,oBAAOrB,OAAA;MAAAuB,QAAA,EAAK;IAAkB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACtC;EAEA,MAAMC,mBAAmB,GAAGA,CAACC,OAAe,EAAEC,OAAe,KAAK;IAChE,IAAI,CAACX,QAAQ,CAACY,OAAO,IAAI,CAACX,YAAY,CAACW,OAAO,EAAE,OAAO,IAAI;IAE3D,MAAMC,SAAS,GAAGb,QAAQ,CAACY,OAAO,CAACE,qBAAqB,CAAC,CAAC;IAC1D,MAAMC,aAAa,GAAGd,YAAY,CAACW,OAAO,CAACE,qBAAqB,CAAC,CAAC;;IAElE;IACA,MAAME,CAAC,GAAGN,OAAO,GAAGG,SAAS,CAACI,IAAI;IAClC,MAAMC,CAAC,GAAGP,OAAO,GAAGE,SAAS,CAACM,GAAG;;IAEjC;IACA,MAAMC,SAAS,GAAGJ,CAAC,GAAGH,SAAS,CAACQ,KAAK;IACrC,MAAMC,SAAS,GAAGJ,CAAC,GAAGL,SAAS,CAACU,MAAM;;IAEtC;IACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEP,SAAS,CAAC,CAAC;IACpD,MAAMQ,QAAQ,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEL,SAAS,CAAC,CAAC;IAEpD,OAAO;MAAEN,CAAC,EAAEQ,QAAQ;MAAEN,CAAC,EAAEU;IAAS,CAAC;EACrC,CAAC;EAED,MAAMC,eAAe,GAAIC,CAAmB,IAAK;IAC/C,IAAI,CAACxC,oBAAoB,EAAE;IAE3B,MAAMyC,MAAM,GAAGtB,mBAAmB,CAACqB,CAAC,CAACpB,OAAO,EAAEoB,CAAC,CAACnB,OAAO,CAAC;IACxD,IAAIoB,MAAM,EAAE;MACVlC,aAAa,CAACkC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMC,eAAe,GAAIF,CAAmB,IAAK;IAC/C,IAAI,CAACxC,oBAAoB,IAAI,CAACM,UAAU,EAAE;IAE1C,MAAMmC,MAAM,GAAGtB,mBAAmB,CAACqB,CAAC,CAACpB,OAAO,EAAEoB,CAAC,CAACnB,OAAO,CAAC;IACxD,IAAIoB,MAAM,EAAE;MACV,MAAMV,KAAK,GAAGI,IAAI,CAACQ,GAAG,CAACF,MAAM,CAACf,CAAC,GAAGpB,UAAU,CAACoB,CAAC,CAAC;MAC/C,MAAMO,MAAM,GAAGE,IAAI,CAACQ,GAAG,CAACF,MAAM,CAACb,CAAC,GAAGtB,UAAU,CAACsB,CAAC,CAAC;MAChD,MAAMF,CAAC,GAAGS,IAAI,CAACE,GAAG,CAAC/B,UAAU,CAACoB,CAAC,EAAEe,MAAM,CAACf,CAAC,CAAC;MAC1C,MAAME,CAAC,GAAGO,IAAI,CAACE,GAAG,CAAC/B,UAAU,CAACsB,CAAC,EAAEa,MAAM,CAACb,CAAC,CAAC;MAE1CnB,cAAc,CAAC;QAAEiB,CAAC;QAAEE,CAAC;QAAEG,KAAK;QAAEE;MAAO,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAMW,aAAa,GAAIJ,CAAmB,IAAK;IAC7C,IAAI,CAACxC,oBAAoB,IAAI,CAACM,UAAU,IAAI,CAACE,WAAW,EAAE;;IAE1D;IACA,IAAIA,WAAW,CAACuB,KAAK,GAAG,IAAI,IAAIvB,WAAW,CAACyB,MAAM,GAAG,IAAI,EAAE;MACzD,MAAMY,aAAa,GAAG;QACpBnB,CAAC,EAAElB,WAAW,CAACkB,CAAC;QAChBE,CAAC,EAAEpB,WAAW,CAACoB,CAAC;QAChBG,KAAK,EAAEvB,WAAW,CAACuB,KAAK;QACxBE,MAAM,EAAEzB,WAAW,CAACyB,MAAM;QAC1Ba,OAAO,EAAE,oDAAoD;QAC7DC,KAAK,EAAE;MACT,CAAC;MAEDlD,aAAa,CAACe,KAAK,CAAClB,EAAE,EAAEmD,aAAa,CAAC;IACxC;IAEA5C,uBAAuB,CAAC,KAAK,CAAC;IAC9BM,aAAa,CAAC,IAAI,CAAC;IACnBE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMuC,qBAAqB,GAAIC,UAAsB,IAAK;IACxD9C,qBAAqB,CAAC8C,UAAU,CAAC;EACnC,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAACJ,OAAe,EAAEC,KAAc,KAAK;IAChE,IAAI7C,kBAAkB,EAAE;MACtBJ,gBAAgB,CAACc,KAAK,CAAClB,EAAE,EAAEQ,kBAAkB,CAACR,EAAE,EAAE;QAAEoD,OAAO;QAAEC;MAAM,CAAC,CAAC;MACrE5C,qBAAqB,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC;EAED,MAAMgD,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAIjD,kBAAkB,EAAE;MACtBH,gBAAgB,CAACa,KAAK,CAAClB,EAAE,EAAEQ,kBAAkB,CAACR,EAAE,CAAC;MACjDS,qBAAqB,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC;EAED,oBACEZ,OAAA;IAAK6D,SAAS,EAAC,mBAAmB;IAAAtC,QAAA,gBAChCvB,OAAA;MAAQ6D,SAAS,EAAC,eAAe;MAAAtC,QAAA,gBAC/BvB,OAAA;QAAQ8D,OAAO,EAAEA,CAAA,KAAM1D,QAAQ,CAAC,GAAG,CAAE;QAACyD,SAAS,EAAC,aAAa;QAAAtC,QAAA,EAAC;MAE9D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3B,OAAA;QAAAuB,QAAA,EAAKF,KAAK,CAAC0C;MAAI;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrB3B,OAAA;QAAK6D,SAAS,EAAC,SAAS;QAAAtC,QAAA,eACtBvB,OAAA;UACE6D,SAAS,EAAE,yBAAyBpD,oBAAoB,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC3EqD,OAAO,EAAEA,CAAA,KAAMpD,uBAAuB,CAAC,CAACD,oBAAoB,CAAE;UAAAc,QAAA,EAE7Dd,oBAAoB,GAAG,cAAc,GAAG;QAAkB;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET3B,OAAA;MAAK6D,SAAS,EAAC,gBAAgB;MAAAtC,QAAA,gBAC7BvB,OAAA;QAAK6D,SAAS,EAAC,iBAAiB;QAACG,GAAG,EAAE5C,YAAa;QAAAG,QAAA,gBACjDvB,OAAA;UACEgE,GAAG,EAAE7C,QAAS;UACd8C,GAAG,EAAE5C,KAAK,CAAC6C,GAAI;UACfC,GAAG,EAAE9C,KAAK,CAAC0C,IAAK;UAChBF,SAAS,EAAE,cAAcpD,oBAAoB,GAAG,UAAU,GAAG,EAAE,EAAG;UAClE2D,WAAW,EAAEpB,eAAgB;UAC7BqB,WAAW,EAAElB,eAAgB;UAC7BmB,SAAS,EAAEjB,aAAc;UACzBkB,SAAS,EAAE;QAAM;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,EAGDN,KAAK,CAACmD,WAAW,CAACC,GAAG,CAACf,UAAU,iBAC/B1D,OAAA;UAEE6D,SAAS,EAAC,oBAAoB;UAC9Ba,KAAK,EAAE;YACLtC,IAAI,EAAE,GAAGsB,UAAU,CAACvB,CAAC,GAAG,GAAG,GAAG;YAC9BG,GAAG,EAAE,GAAGoB,UAAU,CAACrB,CAAC,GAAG,GAAG,GAAG;YAC7BG,KAAK,EAAE,GAAGkB,UAAU,CAAClB,KAAK,GAAG,GAAG,GAAG;YACnCE,MAAM,EAAE,GAAGgB,UAAU,CAAChB,MAAM,GAAG,GAAG;UACpC,CAAE;UACFoB,OAAO,EAAEA,CAAA,KAAML,qBAAqB,CAACC,UAAU,CAAE;UACjDiB,YAAY,EAAEA,CAAA,KAAM7D,oBAAoB,CAAC4C,UAAU,CAAE;UACrDkB,YAAY,EAAEA,CAAA,KAAM9D,oBAAoB,CAAC,IAAI;QAAE,GAV1C4C,UAAU,CAACvD,EAAE;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWnB,CACF,CAAC,EAGDV,WAAW,iBACVjB,OAAA;UACE6D,SAAS,EAAC,6BAA6B;UACvCa,KAAK,EAAE;YACLtC,IAAI,EAAE,GAAGnB,WAAW,CAACkB,CAAC,GAAG,GAAG,GAAG;YAC/BG,GAAG,EAAE,GAAGrB,WAAW,CAACoB,CAAC,GAAG,GAAG,GAAG;YAC9BG,KAAK,EAAE,GAAGvB,WAAW,CAACuB,KAAK,GAAG,GAAG,GAAG;YACpCE,MAAM,EAAE,GAAGzB,WAAW,CAACyB,MAAM,GAAG,GAAG;UACrC;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLd,iBAAiB,IAAI,CAACF,kBAAkB,iBACvCX,OAAA,CAACH,gBAAgB;QACf6D,UAAU,EAAE7C,iBAAkB;QAC9BgE,QAAQ,EAAC;MAAM;QAAArD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CACF,EAGAhB,kBAAkB,iBACjBX,OAAA,CAACF,gBAAgB;QACf4D,UAAU,EAAE/C,kBAAmB;QAC/BmE,MAAM,EAAEnB,oBAAqB;QAC7BoB,QAAQ,EAAEnB,sBAAuB;QACjCoB,OAAO,EAAEA,CAAA,KAAMpE,qBAAqB,CAAC,IAAI;MAAE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CA7LID,eAAyB;EAAA,QACdP,SAAS,EACPC,WAAW,EACgDC,eAAe;AAAA;AAAAqF,EAAA,GAHvFhF,eAAyB;AA+L/B,eAAeA,eAAe;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}