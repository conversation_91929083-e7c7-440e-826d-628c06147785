{"ast": null, "code": "/**\n * Utility with info on URL attributes.\n *\n * ## What is this?\n *\n * This package contains info on attributes that have URLs as values.\n *\n * ## When should I use this?\n *\n * You can use this package any time you’re rewriting URLs.\n *\n * ## Use\n *\n * ```js\n * import {urlAttributes} from 'html-url-attributes'\n *\n * console.log(urlAttributes.formAction)\n * //=> ['button', 'input']\n * console.log(urlAttributes.href)\n * //=> ['a', 'area', 'base', 'link']\n * ```\n *\n * ## API\n *\n * ### `urlAttributes`\n *\n * HTML URL properties (`Record<string, Array<string> | null>`).\n *\n * Each key is a property name and each value is a list of tag names it applies\n * to or `null` if it applies to all elements.\n */\n\nexport { urlAttributes } from './lib/index.js';", "map": {"version": 3, "names": ["urlAttributes"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/html-url-attributes/index.js"], "sourcesContent": ["/**\n * Utility with info on URL attributes.\n *\n * ## What is this?\n *\n * This package contains info on attributes that have URLs as values.\n *\n * ## When should I use this?\n *\n * You can use this package any time you’re rewriting URLs.\n *\n * ## Use\n *\n * ```js\n * import {urlAttributes} from 'html-url-attributes'\n *\n * console.log(urlAttributes.formAction)\n * //=> ['button', 'input']\n * console.log(urlAttributes.href)\n * //=> ['a', 'area', 'base', 'link']\n * ```\n *\n * ## API\n *\n * ### `urlAttributes`\n *\n * HTML URL properties (`Record<string, Array<string> | null>`).\n *\n * Each key is a property name and each value is a list of tag names it applies\n * to or `null` if it applies to all elements.\n */\n\nexport {urlAttributes} from './lib/index.js'\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,aAAa,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}