{"ast": null, "code": "/**\n * @import {Event, Resolver, TokenizeContext} from 'micromark-util-types'\n */\n\n/**\n * Call all `resolveAll`s.\n *\n * @param {ReadonlyArray<{resolveAll?: Resolver | undefined}>} constructs\n *   List of constructs, optionally with `resolveAll`s.\n * @param {Array<Event>} events\n *   List of events.\n * @param {TokenizeContext} context\n *   Context used by `tokenize`.\n * @returns {Array<Event>}\n *   Changed events.\n */\nexport function resolveAll(constructs, events, context) {\n  /** @type {Array<Resolver>} */\n  const called = [];\n  let index = -1;\n  while (++index < constructs.length) {\n    const resolve = constructs[index].resolveAll;\n    if (resolve && !called.includes(resolve)) {\n      events = resolve(events, context);\n      called.push(resolve);\n    }\n  }\n  return events;\n}", "map": {"version": 3, "names": ["resolveAll", "constructs", "events", "context", "called", "index", "length", "resolve", "includes", "push"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark-util-resolve-all/index.js"], "sourcesContent": ["/**\n * @import {Event, Resolver, TokenizeContext} from 'micromark-util-types'\n */\n\n/**\n * Call all `resolveAll`s.\n *\n * @param {ReadonlyArray<{resolveAll?: Resolver | undefined}>} constructs\n *   List of constructs, optionally with `resolveAll`s.\n * @param {Array<Event>} events\n *   List of events.\n * @param {TokenizeContext} context\n *   Context used by `tokenize`.\n * @returns {Array<Event>}\n *   Changed events.\n */\nexport function resolveAll(constructs, events, context) {\n  /** @type {Array<Resolver>} */\n  const called = []\n  let index = -1\n\n  while (++index < constructs.length) {\n    const resolve = constructs[index].resolveAll\n\n    if (resolve && !called.includes(resolve)) {\n      events = resolve(events, context)\n      called.push(resolve)\n    }\n  }\n\n  return events\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,UAAUA,CAACC,UAAU,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACtD;EACA,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIC,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAGJ,UAAU,CAACK,MAAM,EAAE;IAClC,MAAMC,OAAO,GAAGN,UAAU,CAACI,KAAK,CAAC,CAACL,UAAU;IAE5C,IAAIO,OAAO,IAAI,CAACH,MAAM,CAACI,QAAQ,CAACD,OAAO,CAAC,EAAE;MACxCL,MAAM,GAAGK,OAAO,CAACL,MAAM,EAAEC,OAAO,CAAC;MACjCC,MAAM,CAACK,IAAI,CAACF,OAAO,CAAC;IACtB;EACF;EAEA,OAAOL,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}