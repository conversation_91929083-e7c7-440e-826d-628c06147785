{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/CollectionTree.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useImageContext } from '../contexts/ImageContext';\nimport './CollectionTree.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CollectionTree = ({\n  collections,\n  selectedId,\n  onSelect\n}) => {\n  _s();\n  const {\n    addCollection\n  } = useImageContext();\n  const [expandedIds, setExpandedIds] = useState(new Set(['root']));\n  const [newCollectionName, setNewCollectionName] = useState('');\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [parentId, setParentId] = useState('');\n  const toggleExpanded = id => {\n    const newExpanded = new Set(expandedIds);\n    if (newExpanded.has(id)) {\n      newExpanded.delete(id);\n    } else {\n      newExpanded.add(id);\n    }\n    setExpandedIds(newExpanded);\n  };\n  const handleAddCollection = () => {\n    if (newCollectionName.trim()) {\n      addCollection(newCollectionName.trim(), parentId || undefined);\n      setNewCollectionName('');\n      setShowAddForm(false);\n      setParentId('');\n    }\n  };\n  const renderCollection = (collection, level = 0) => {\n    const hasChildren = collection.children.length > 0;\n    const isExpanded = expandedIds.has(collection.id);\n    const isSelected = selectedId === collection.id;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"collection-item\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `collection-header ${isSelected ? 'selected' : ''}`,\n        style: {\n          paddingLeft: `${level * 20}px`\n        },\n        children: [hasChildren && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"expand-button\",\n          onClick: () => toggleExpanded(collection.id),\n          children: isExpanded ? '▼' : '▶'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"collection-name\",\n          onClick: () => onSelect(collection.id),\n          children: [\"\\uD83D\\uDCC1 \", collection.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"add-subcollection-btn\",\n          onClick: () => {\n            setParentId(collection.id);\n            setShowAddForm(true);\n          },\n          title: \"Th\\xEAm th\\u01B0 m\\u1EE5c con\",\n          children: \"+\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), hasChildren && isExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"collection-children\",\n        children: collection.children.map(child => renderCollection(child, level + 1))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 11\n      }, this)]\n    }, collection.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"collection-tree\",\n    children: [collections.map(collection => renderCollection(collection)), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"add-collection-section\",\n      children: !showAddForm ? /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"add-collection-btn\",\n        onClick: () => setShowAddForm(true),\n        children: \"+ Th\\xEAm b\\u1ED9 s\\u01B0u t\\u1EADp\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"add-collection-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"T\\xEAn b\\u1ED9 s\\u01B0u t\\u1EADp\",\n          value: newCollectionName,\n          onChange: e => setNewCollectionName(e.target.value),\n          onKeyPress: e => e.key === 'Enter' && handleAddCollection(),\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddCollection,\n            children: \"Th\\xEAm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowAddForm(false);\n              setNewCollectionName('');\n              setParentId('');\n            },\n            children: \"H\\u1EE7y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_s(CollectionTree, \"HjhUBbyjmIceojhPQw+vIzo/YLQ=\", false, function () {\n  return [useImageContext];\n});\n_c = CollectionTree;\nexport default CollectionTree;\nvar _c;\n$RefreshReg$(_c, \"CollectionTree\");", "map": {"version": 3, "names": ["React", "useState", "useImageContext", "jsxDEV", "_jsxDEV", "CollectionTree", "collections", "selectedId", "onSelect", "_s", "addCollection", "expandedIds", "setExpandedIds", "Set", "newCollectionName", "setNewCollectionName", "showAddForm", "setShowAddForm", "parentId", "setParentId", "toggleExpanded", "id", "newExpanded", "has", "delete", "add", "handleAddCollection", "trim", "undefined", "renderCollection", "collection", "level", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "length", "isExpanded", "isSelected", "className", "style", "paddingLeft", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "title", "map", "child", "type", "placeholder", "value", "onChange", "e", "target", "onKeyPress", "key", "autoFocus", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/CollectionTree.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useImageContext } from '../contexts/ImageContext';\nimport { Collection } from '../types';\nimport './CollectionTree.css';\n\ninterface CollectionTreeProps {\n  collections: Collection[];\n  selectedId: string;\n  onSelect: (id: string) => void;\n}\n\nconst CollectionTree: React.FC<CollectionTreeProps> = ({ collections, selectedId, onSelect }) => {\n  const { addCollection } = useImageContext();\n  const [expandedIds, setExpandedIds] = useState<Set<string>>(new Set(['root']));\n  const [newCollectionName, setNewCollectionName] = useState('');\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [parentId, setParentId] = useState<string>('');\n\n  const toggleExpanded = (id: string) => {\n    const newExpanded = new Set(expandedIds);\n    if (newExpanded.has(id)) {\n      newExpanded.delete(id);\n    } else {\n      newExpanded.add(id);\n    }\n    setExpandedIds(newExpanded);\n  };\n\n  const handleAddCollection = () => {\n    if (newCollectionName.trim()) {\n      addCollection(newCollectionName.trim(), parentId || undefined);\n      setNewCollectionName('');\n      setShowAddForm(false);\n      setParentId('');\n    }\n  };\n\n  const renderCollection = (collection: Collection, level: number = 0) => {\n    const hasChildren = collection.children.length > 0;\n    const isExpanded = expandedIds.has(collection.id);\n    const isSelected = selectedId === collection.id;\n\n    return (\n      <div key={collection.id} className=\"collection-item\">\n        <div \n          className={`collection-header ${isSelected ? 'selected' : ''}`}\n          style={{ paddingLeft: `${level * 20}px` }}\n        >\n          {hasChildren && (\n            <button \n              className=\"expand-button\"\n              onClick={() => toggleExpanded(collection.id)}\n            >\n              {isExpanded ? '▼' : '▶'}\n            </button>\n          )}\n          <span \n            className=\"collection-name\"\n            onClick={() => onSelect(collection.id)}\n          >\n            📁 {collection.name}\n          </span>\n          <button \n            className=\"add-subcollection-btn\"\n            onClick={() => {\n              setParentId(collection.id);\n              setShowAddForm(true);\n            }}\n            title=\"Thêm thư mục con\"\n          >\n            +\n          </button>\n        </div>\n        \n        {hasChildren && isExpanded && (\n          <div className=\"collection-children\">\n            {collection.children.map(child => renderCollection(child, level + 1))}\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"collection-tree\">\n      {collections.map(collection => renderCollection(collection))}\n      \n      <div className=\"add-collection-section\">\n        {!showAddForm ? (\n          <button \n            className=\"add-collection-btn\"\n            onClick={() => setShowAddForm(true)}\n          >\n            + Thêm bộ sưu tập\n          </button>\n        ) : (\n          <div className=\"add-collection-form\">\n            <input\n              type=\"text\"\n              placeholder=\"Tên bộ sưu tập\"\n              value={newCollectionName}\n              onChange={(e) => setNewCollectionName(e.target.value)}\n              onKeyPress={(e) => e.key === 'Enter' && handleAddCollection()}\n              autoFocus\n            />\n            <div className=\"form-buttons\">\n              <button onClick={handleAddCollection}>Thêm</button>\n              <button onClick={() => {\n                setShowAddForm(false);\n                setNewCollectionName('');\n                setParentId('');\n              }}>\n                Hủy\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CollectionTree;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ9B,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,WAAW;EAAEC,UAAU;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/F,MAAM;IAAEC;EAAc,CAAC,GAAGR,eAAe,CAAC,CAAC;EAC3C,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAc,IAAIY,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;EAC9E,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAS,EAAE,CAAC;EAEpD,MAAMmB,cAAc,GAAIC,EAAU,IAAK;IACrC,MAAMC,WAAW,GAAG,IAAIT,GAAG,CAACF,WAAW,CAAC;IACxC,IAAIW,WAAW,CAACC,GAAG,CAACF,EAAE,CAAC,EAAE;MACvBC,WAAW,CAACE,MAAM,CAACH,EAAE,CAAC;IACxB,CAAC,MAAM;MACLC,WAAW,CAACG,GAAG,CAACJ,EAAE,CAAC;IACrB;IACAT,cAAc,CAACU,WAAW,CAAC;EAC7B,CAAC;EAED,MAAMI,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIZ,iBAAiB,CAACa,IAAI,CAAC,CAAC,EAAE;MAC5BjB,aAAa,CAACI,iBAAiB,CAACa,IAAI,CAAC,CAAC,EAAET,QAAQ,IAAIU,SAAS,CAAC;MAC9Db,oBAAoB,CAAC,EAAE,CAAC;MACxBE,cAAc,CAAC,KAAK,CAAC;MACrBE,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC;EAED,MAAMU,gBAAgB,GAAGA,CAACC,UAAsB,EAAEC,KAAa,GAAG,CAAC,KAAK;IACtE,MAAMC,WAAW,GAAGF,UAAU,CAACG,QAAQ,CAACC,MAAM,GAAG,CAAC;IAClD,MAAMC,UAAU,GAAGxB,WAAW,CAACY,GAAG,CAACO,UAAU,CAACT,EAAE,CAAC;IACjD,MAAMe,UAAU,GAAG7B,UAAU,KAAKuB,UAAU,CAACT,EAAE;IAE/C,oBACEjB,OAAA;MAAyBiC,SAAS,EAAC,iBAAiB;MAAAJ,QAAA,gBAClD7B,OAAA;QACEiC,SAAS,EAAE,qBAAqBD,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;QAC/DE,KAAK,EAAE;UAAEC,WAAW,EAAE,GAAGR,KAAK,GAAG,EAAE;QAAK,CAAE;QAAAE,QAAA,GAEzCD,WAAW,iBACV5B,OAAA;UACEiC,SAAS,EAAC,eAAe;UACzBG,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAACU,UAAU,CAACT,EAAE,CAAE;UAAAY,QAAA,EAE5CE,UAAU,GAAG,GAAG,GAAG;QAAG;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CACT,eACDxC,OAAA;UACEiC,SAAS,EAAC,iBAAiB;UAC3BG,OAAO,EAAEA,CAAA,KAAMhC,QAAQ,CAACsB,UAAU,CAACT,EAAE,CAAE;UAAAY,QAAA,GACxC,eACI,EAACH,UAAU,CAACe,IAAI;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACPxC,OAAA;UACEiC,SAAS,EAAC,uBAAuB;UACjCG,OAAO,EAAEA,CAAA,KAAM;YACbrB,WAAW,CAACW,UAAU,CAACT,EAAE,CAAC;YAC1BJ,cAAc,CAAC,IAAI,CAAC;UACtB,CAAE;UACF6B,KAAK,EAAC,+BAAkB;UAAAb,QAAA,EACzB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELZ,WAAW,IAAIG,UAAU,iBACxB/B,OAAA;QAAKiC,SAAS,EAAC,qBAAqB;QAAAJ,QAAA,EACjCH,UAAU,CAACG,QAAQ,CAACc,GAAG,CAACC,KAAK,IAAInB,gBAAgB,CAACmB,KAAK,EAAEjB,KAAK,GAAG,CAAC,CAAC;MAAC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CACN;IAAA,GAnCOd,UAAU,CAACT,EAAE;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAoClB,CAAC;EAEV,CAAC;EAED,oBACExC,OAAA;IAAKiC,SAAS,EAAC,iBAAiB;IAAAJ,QAAA,GAC7B3B,WAAW,CAACyC,GAAG,CAACjB,UAAU,IAAID,gBAAgB,CAACC,UAAU,CAAC,CAAC,eAE5D1B,OAAA;MAAKiC,SAAS,EAAC,wBAAwB;MAAAJ,QAAA,EACpC,CAACjB,WAAW,gBACXZ,OAAA;QACEiC,SAAS,EAAC,oBAAoB;QAC9BG,OAAO,EAAEA,CAAA,KAAMvB,cAAc,CAAC,IAAI,CAAE;QAAAgB,QAAA,EACrC;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAETxC,OAAA;QAAKiC,SAAS,EAAC,qBAAqB;QAAAJ,QAAA,gBAClC7B,OAAA;UACE6C,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,kCAAgB;UAC5BC,KAAK,EAAErC,iBAAkB;UACzBsC,QAAQ,EAAGC,CAAC,IAAKtC,oBAAoB,CAACsC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACtDI,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAI9B,mBAAmB,CAAC,CAAE;UAC9D+B,SAAS;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACFxC,OAAA;UAAKiC,SAAS,EAAC,cAAc;UAAAJ,QAAA,gBAC3B7B,OAAA;YAAQoC,OAAO,EAAEd,mBAAoB;YAAAO,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACnDxC,OAAA;YAAQoC,OAAO,EAAEA,CAAA,KAAM;cACrBvB,cAAc,CAAC,KAAK,CAAC;cACrBF,oBAAoB,CAAC,EAAE,CAAC;cACxBI,WAAW,CAAC,EAAE,CAAC;YACjB,CAAE;YAAAc,QAAA,EAAC;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnC,EAAA,CA7GIJ,cAA6C;EAAA,QACvBH,eAAe;AAAA;AAAAwD,EAAA,GADrCrD,cAA6C;AA+GnD,eAAeA,cAAc;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}