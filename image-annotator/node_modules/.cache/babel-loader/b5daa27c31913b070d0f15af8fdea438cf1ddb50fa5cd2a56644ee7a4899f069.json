{"ast": null, "code": "/**\n * @import {\n *   Construct,\n *   Previous,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { codes, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const codeText = {\n  name: 'codeText',\n  previous,\n  resolve: resolveCodeText,\n  tokenize: tokenizeCodeText\n};\n\n// To do: next major: don’t resolve, like `markdown-rs`.\n/** @type {Resolver} */\nfunction resolveCodeText(events) {\n  let tailExitIndex = events.length - 4;\n  let headEnterIndex = 3;\n  /** @type {number} */\n  let index;\n  /** @type {number | undefined} */\n  let enter;\n\n  // If we start and end with an EOL or a space.\n  if ((events[headEnterIndex][1].type === types.lineEnding || events[headEnterIndex][1].type === 'space') && (events[tailExitIndex][1].type === types.lineEnding || events[tailExitIndex][1].type === 'space')) {\n    index = headEnterIndex;\n\n    // And we have data.\n    while (++index < tailExitIndex) {\n      if (events[index][1].type === types.codeTextData) {\n        // Then we have padding.\n        events[headEnterIndex][1].type = types.codeTextPadding;\n        events[tailExitIndex][1].type = types.codeTextPadding;\n        headEnterIndex += 2;\n        tailExitIndex -= 2;\n        break;\n      }\n    }\n  }\n\n  // Merge adjacent spaces and data.\n  index = headEnterIndex - 1;\n  tailExitIndex++;\n  while (++index <= tailExitIndex) {\n    if (enter === undefined) {\n      if (index !== tailExitIndex && events[index][1].type !== types.lineEnding) {\n        enter = index;\n      }\n    } else if (index === tailExitIndex || events[index][1].type === types.lineEnding) {\n      events[enter][1].type = types.codeTextData;\n      if (index !== enter + 2) {\n        events[enter][1].end = events[index - 1][1].end;\n        events.splice(enter + 2, index - enter - 2);\n        tailExitIndex -= index - enter - 2;\n        index = enter + 2;\n      }\n      enter = undefined;\n    }\n  }\n  return events;\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Previous}\n */\nfunction previous(code) {\n  // If there is a previous code, there will always be a tail.\n  return code !== codes.graveAccent || this.events[this.events.length - 1][1].type === types.characterEscape;\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeText(effects, ok, nok) {\n  const self = this;\n  let sizeOpen = 0;\n  /** @type {number} */\n  let size;\n  /** @type {Token} */\n  let token;\n  return start;\n\n  /**\n   * Start of code (text).\n   *\n   * ```markdown\n   * > | `a`\n   *     ^\n   * > | \\`a`\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.graveAccent, 'expected `` ` ``');\n    assert(previous.call(self, self.previous), 'expected correct previous');\n    effects.enter(types.codeText);\n    effects.enter(types.codeTextSequence);\n    return sequenceOpen(code);\n  }\n\n  /**\n   * In opening sequence.\n   *\n   * ```markdown\n   * > | `a`\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === codes.graveAccent) {\n      effects.consume(code);\n      sizeOpen++;\n      return sequenceOpen;\n    }\n    effects.exit(types.codeTextSequence);\n    return between(code);\n  }\n\n  /**\n   * Between something and something else.\n   *\n   * ```markdown\n   * > | `a`\n   *      ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function between(code) {\n    // EOF.\n    if (code === codes.eof) {\n      return nok(code);\n    }\n\n    // To do: next major: don’t do spaces in resolve, but when compiling,\n    // like `markdown-rs`.\n    // Tabs don’t work, and virtual spaces don’t make sense.\n    if (code === codes.space) {\n      effects.enter('space');\n      effects.consume(code);\n      effects.exit('space');\n      return between;\n    }\n\n    // Closing fence? Could also be data.\n    if (code === codes.graveAccent) {\n      token = effects.enter(types.codeTextSequence);\n      size = 0;\n      return sequenceClose(code);\n    }\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding);\n      effects.consume(code);\n      effects.exit(types.lineEnding);\n      return between;\n    }\n\n    // Data.\n    effects.enter(types.codeTextData);\n    return data(code);\n  }\n\n  /**\n   * In data.\n   *\n   * ```markdown\n   * > | `a`\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function data(code) {\n    if (code === codes.eof || code === codes.space || code === codes.graveAccent || markdownLineEnding(code)) {\n      effects.exit(types.codeTextData);\n      return between(code);\n    }\n    effects.consume(code);\n    return data;\n  }\n\n  /**\n   * In closing sequence.\n   *\n   * ```markdown\n   * > | `a`\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceClose(code) {\n    // More.\n    if (code === codes.graveAccent) {\n      effects.consume(code);\n      size++;\n      return sequenceClose;\n    }\n\n    // Done!\n    if (size === sizeOpen) {\n      effects.exit(types.codeTextSequence);\n      effects.exit(types.codeText);\n      return ok(code);\n    }\n\n    // More or less accents: mark as data.\n    token.type = types.codeTextData;\n    return data(code);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "markdownLineEnding", "codes", "types", "codeText", "name", "previous", "resolve", "resolveCodeText", "tokenize", "tokenizeCodeText", "events", "tailExitIndex", "length", "headEnterIndex", "index", "enter", "type", "lineEnding", "codeTextData", "codeTextPadding", "undefined", "end", "splice", "code", "graveAccent", "characterEscape", "effects", "nok", "self", "sizeOpen", "size", "token", "start", "call", "codeTextSequence", "sequenceOpen", "consume", "exit", "between", "eof", "space", "sequenceClose", "data"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark-core-commonmark/dev/lib/code-text.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   Previous,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const codeText = {\n  name: 'codeText',\n  previous,\n  resolve: resolveCodeText,\n  tokenize: tokenizeCodeText\n}\n\n// To do: next major: don’t resolve, like `markdown-rs`.\n/** @type {Resolver} */\nfunction resolveCodeText(events) {\n  let tailExitIndex = events.length - 4\n  let headEnterIndex = 3\n  /** @type {number} */\n  let index\n  /** @type {number | undefined} */\n  let enter\n\n  // If we start and end with an EOL or a space.\n  if (\n    (events[headEnterIndex][1].type === types.lineEnding ||\n      events[headEnterIndex][1].type === 'space') &&\n    (events[tailExitIndex][1].type === types.lineEnding ||\n      events[tailExitIndex][1].type === 'space')\n  ) {\n    index = headEnterIndex\n\n    // And we have data.\n    while (++index < tailExitIndex) {\n      if (events[index][1].type === types.codeTextData) {\n        // Then we have padding.\n        events[headEnterIndex][1].type = types.codeTextPadding\n        events[tailExitIndex][1].type = types.codeTextPadding\n        headEnterIndex += 2\n        tailExitIndex -= 2\n        break\n      }\n    }\n  }\n\n  // Merge adjacent spaces and data.\n  index = headEnterIndex - 1\n  tailExitIndex++\n\n  while (++index <= tailExitIndex) {\n    if (enter === undefined) {\n      if (\n        index !== tailExitIndex &&\n        events[index][1].type !== types.lineEnding\n      ) {\n        enter = index\n      }\n    } else if (\n      index === tailExitIndex ||\n      events[index][1].type === types.lineEnding\n    ) {\n      events[enter][1].type = types.codeTextData\n\n      if (index !== enter + 2) {\n        events[enter][1].end = events[index - 1][1].end\n        events.splice(enter + 2, index - enter - 2)\n        tailExitIndex -= index - enter - 2\n        index = enter + 2\n      }\n\n      enter = undefined\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Previous}\n */\nfunction previous(code) {\n  // If there is a previous code, there will always be a tail.\n  return (\n    code !== codes.graveAccent ||\n    this.events[this.events.length - 1][1].type === types.characterEscape\n  )\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeText(effects, ok, nok) {\n  const self = this\n  let sizeOpen = 0\n  /** @type {number} */\n  let size\n  /** @type {Token} */\n  let token\n\n  return start\n\n  /**\n   * Start of code (text).\n   *\n   * ```markdown\n   * > | `a`\n   *     ^\n   * > | \\`a`\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.graveAccent, 'expected `` ` ``')\n    assert(previous.call(self, self.previous), 'expected correct previous')\n    effects.enter(types.codeText)\n    effects.enter(types.codeTextSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening sequence.\n   *\n   * ```markdown\n   * > | `a`\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === codes.graveAccent) {\n      effects.consume(code)\n      sizeOpen++\n      return sequenceOpen\n    }\n\n    effects.exit(types.codeTextSequence)\n    return between(code)\n  }\n\n  /**\n   * Between something and something else.\n   *\n   * ```markdown\n   * > | `a`\n   *      ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function between(code) {\n    // EOF.\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    // To do: next major: don’t do spaces in resolve, but when compiling,\n    // like `markdown-rs`.\n    // Tabs don’t work, and virtual spaces don’t make sense.\n    if (code === codes.space) {\n      effects.enter('space')\n      effects.consume(code)\n      effects.exit('space')\n      return between\n    }\n\n    // Closing fence? Could also be data.\n    if (code === codes.graveAccent) {\n      token = effects.enter(types.codeTextSequence)\n      size = 0\n      return sequenceClose(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return between\n    }\n\n    // Data.\n    effects.enter(types.codeTextData)\n    return data(code)\n  }\n\n  /**\n   * In data.\n   *\n   * ```markdown\n   * > | `a`\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function data(code) {\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.graveAccent ||\n      markdownLineEnding(code)\n    ) {\n      effects.exit(types.codeTextData)\n      return between(code)\n    }\n\n    effects.consume(code)\n    return data\n  }\n\n  /**\n   * In closing sequence.\n   *\n   * ```markdown\n   * > | `a`\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceClose(code) {\n    // More.\n    if (code === codes.graveAccent) {\n      effects.consume(code)\n      size++\n      return sequenceClose\n    }\n\n    // Done!\n    if (size === sizeOpen) {\n      effects.exit(types.codeTextSequence)\n      effects.exit(types.codeText)\n      return ok(code)\n    }\n\n    // More or less accents: mark as data.\n    token.type = types.codeTextData\n    return data(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,KAAK,EAAEC,KAAK,QAAO,uBAAuB;;AAElD;AACA,OAAO,MAAMC,QAAQ,GAAG;EACtBC,IAAI,EAAE,UAAU;EAChBC,QAAQ;EACRC,OAAO,EAAEC,eAAe;EACxBC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA;AACA,SAASF,eAAeA,CAACG,MAAM,EAAE;EAC/B,IAAIC,aAAa,GAAGD,MAAM,CAACE,MAAM,GAAG,CAAC;EACrC,IAAIC,cAAc,GAAG,CAAC;EACtB;EACA,IAAIC,KAAK;EACT;EACA,IAAIC,KAAK;;EAET;EACA,IACE,CAACL,MAAM,CAACG,cAAc,CAAC,CAAC,CAAC,CAAC,CAACG,IAAI,KAAKd,KAAK,CAACe,UAAU,IAClDP,MAAM,CAACG,cAAc,CAAC,CAAC,CAAC,CAAC,CAACG,IAAI,KAAK,OAAO,MAC3CN,MAAM,CAACC,aAAa,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKd,KAAK,CAACe,UAAU,IACjDP,MAAM,CAACC,aAAa,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,KAAK,OAAO,CAAC,EAC5C;IACAF,KAAK,GAAGD,cAAc;;IAEtB;IACA,OAAO,EAAEC,KAAK,GAAGH,aAAa,EAAE;MAC9B,IAAID,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,CAACE,IAAI,KAAKd,KAAK,CAACgB,YAAY,EAAE;QAChD;QACAR,MAAM,CAACG,cAAc,CAAC,CAAC,CAAC,CAAC,CAACG,IAAI,GAAGd,KAAK,CAACiB,eAAe;QACtDT,MAAM,CAACC,aAAa,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,GAAGd,KAAK,CAACiB,eAAe;QACrDN,cAAc,IAAI,CAAC;QACnBF,aAAa,IAAI,CAAC;QAClB;MACF;IACF;EACF;;EAEA;EACAG,KAAK,GAAGD,cAAc,GAAG,CAAC;EAC1BF,aAAa,EAAE;EAEf,OAAO,EAAEG,KAAK,IAAIH,aAAa,EAAE;IAC/B,IAAII,KAAK,KAAKK,SAAS,EAAE;MACvB,IACEN,KAAK,KAAKH,aAAa,IACvBD,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,CAACE,IAAI,KAAKd,KAAK,CAACe,UAAU,EAC1C;QACAF,KAAK,GAAGD,KAAK;MACf;IACF,CAAC,MAAM,IACLA,KAAK,KAAKH,aAAa,IACvBD,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,CAACE,IAAI,KAAKd,KAAK,CAACe,UAAU,EAC1C;MACAP,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,GAAGd,KAAK,CAACgB,YAAY;MAE1C,IAAIJ,KAAK,KAAKC,KAAK,GAAG,CAAC,EAAE;QACvBL,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,CAACM,GAAG,GAAGX,MAAM,CAACI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACO,GAAG;QAC/CX,MAAM,CAACY,MAAM,CAACP,KAAK,GAAG,CAAC,EAAED,KAAK,GAAGC,KAAK,GAAG,CAAC,CAAC;QAC3CJ,aAAa,IAAIG,KAAK,GAAGC,KAAK,GAAG,CAAC;QAClCD,KAAK,GAAGC,KAAK,GAAG,CAAC;MACnB;MAEAA,KAAK,GAAGK,SAAS;IACnB;EACF;EAEA,OAAOV,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASL,QAAQA,CAACkB,IAAI,EAAE;EACtB;EACA,OACEA,IAAI,KAAKtB,KAAK,CAACuB,WAAW,IAC1B,IAAI,CAACd,MAAM,CAAC,IAAI,CAACA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,IAAI,KAAKd,KAAK,CAACuB,eAAe;AAEzE;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAShB,gBAAgBA,CAACiB,OAAO,EAAE5B,EAAE,EAAE6B,GAAG,EAAE;EAC1C,MAAMC,IAAI,GAAG,IAAI;EACjB,IAAIC,QAAQ,GAAG,CAAC;EAChB;EACA,IAAIC,IAAI;EACR;EACA,IAAIC,KAAK;EAET,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACT,IAAI,EAAE;IACnBxB,MAAM,CAACwB,IAAI,KAAKtB,KAAK,CAACuB,WAAW,EAAE,kBAAkB,CAAC;IACtDzB,MAAM,CAACM,QAAQ,CAAC4B,IAAI,CAACL,IAAI,EAAEA,IAAI,CAACvB,QAAQ,CAAC,EAAE,2BAA2B,CAAC;IACvEqB,OAAO,CAACX,KAAK,CAACb,KAAK,CAACC,QAAQ,CAAC;IAC7BuB,OAAO,CAACX,KAAK,CAACb,KAAK,CAACgC,gBAAgB,CAAC;IACrC,OAAOC,YAAY,CAACZ,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASY,YAAYA,CAACZ,IAAI,EAAE;IAC1B,IAAIA,IAAI,KAAKtB,KAAK,CAACuB,WAAW,EAAE;MAC9BE,OAAO,CAACU,OAAO,CAACb,IAAI,CAAC;MACrBM,QAAQ,EAAE;MACV,OAAOM,YAAY;IACrB;IAEAT,OAAO,CAACW,IAAI,CAACnC,KAAK,CAACgC,gBAAgB,CAAC;IACpC,OAAOI,OAAO,CAACf,IAAI,CAAC;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASe,OAAOA,CAACf,IAAI,EAAE;IACrB;IACA,IAAIA,IAAI,KAAKtB,KAAK,CAACsC,GAAG,EAAE;MACtB,OAAOZ,GAAG,CAACJ,IAAI,CAAC;IAClB;;IAEA;IACA;IACA;IACA,IAAIA,IAAI,KAAKtB,KAAK,CAACuC,KAAK,EAAE;MACxBd,OAAO,CAACX,KAAK,CAAC,OAAO,CAAC;MACtBW,OAAO,CAACU,OAAO,CAACb,IAAI,CAAC;MACrBG,OAAO,CAACW,IAAI,CAAC,OAAO,CAAC;MACrB,OAAOC,OAAO;IAChB;;IAEA;IACA,IAAIf,IAAI,KAAKtB,KAAK,CAACuB,WAAW,EAAE;MAC9BO,KAAK,GAAGL,OAAO,CAACX,KAAK,CAACb,KAAK,CAACgC,gBAAgB,CAAC;MAC7CJ,IAAI,GAAG,CAAC;MACR,OAAOW,aAAa,CAAClB,IAAI,CAAC;IAC5B;IAEA,IAAIvB,kBAAkB,CAACuB,IAAI,CAAC,EAAE;MAC5BG,OAAO,CAACX,KAAK,CAACb,KAAK,CAACe,UAAU,CAAC;MAC/BS,OAAO,CAACU,OAAO,CAACb,IAAI,CAAC;MACrBG,OAAO,CAACW,IAAI,CAACnC,KAAK,CAACe,UAAU,CAAC;MAC9B,OAAOqB,OAAO;IAChB;;IAEA;IACAZ,OAAO,CAACX,KAAK,CAACb,KAAK,CAACgB,YAAY,CAAC;IACjC,OAAOwB,IAAI,CAACnB,IAAI,CAAC;EACnB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASmB,IAAIA,CAACnB,IAAI,EAAE;IAClB,IACEA,IAAI,KAAKtB,KAAK,CAACsC,GAAG,IAClBhB,IAAI,KAAKtB,KAAK,CAACuC,KAAK,IACpBjB,IAAI,KAAKtB,KAAK,CAACuB,WAAW,IAC1BxB,kBAAkB,CAACuB,IAAI,CAAC,EACxB;MACAG,OAAO,CAACW,IAAI,CAACnC,KAAK,CAACgB,YAAY,CAAC;MAChC,OAAOoB,OAAO,CAACf,IAAI,CAAC;IACtB;IAEAG,OAAO,CAACU,OAAO,CAACb,IAAI,CAAC;IACrB,OAAOmB,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASD,aAAaA,CAAClB,IAAI,EAAE;IAC3B;IACA,IAAIA,IAAI,KAAKtB,KAAK,CAACuB,WAAW,EAAE;MAC9BE,OAAO,CAACU,OAAO,CAACb,IAAI,CAAC;MACrBO,IAAI,EAAE;MACN,OAAOW,aAAa;IACtB;;IAEA;IACA,IAAIX,IAAI,KAAKD,QAAQ,EAAE;MACrBH,OAAO,CAACW,IAAI,CAACnC,KAAK,CAACgC,gBAAgB,CAAC;MACpCR,OAAO,CAACW,IAAI,CAACnC,KAAK,CAACC,QAAQ,CAAC;MAC5B,OAAOL,EAAE,CAACyB,IAAI,CAAC;IACjB;;IAEA;IACAQ,KAAK,CAACf,IAAI,GAAGd,KAAK,CAACgB,YAAY;IAC/B,OAAOwB,IAAI,CAACnB,IAAI,CAAC;EACnB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}