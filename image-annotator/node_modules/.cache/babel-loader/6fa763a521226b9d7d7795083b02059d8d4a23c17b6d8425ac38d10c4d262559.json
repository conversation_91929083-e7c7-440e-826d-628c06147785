{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/pages/ImageDetailPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useImageContext } from '../contexts/ImageContext';\nimport AnnotationViewer from '../components/AnnotationViewer';\nimport AnnotationEditor from '../components/AnnotationEditor';\nimport './ImageDetailPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageDetailPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    getImageById,\n    addAnnotation,\n    updateAnnotation,\n    deleteAnnotation,\n    deleteImage\n  } = useImageContext();\n  const [isCreatingAnnotation, setIsCreatingAnnotation] = useState(false);\n  const [selectedAnnotation, setSelectedAnnotation] = useState(null);\n  const [hoveredAnnotation, setHoveredAnnotation] = useState(null);\n  const [startPoint, setStartPoint] = useState(null);\n  const [currentRect, setCurrentRect] = useState(null);\n  const imageRef = useRef(null);\n  const containerRef = useRef(null);\n  const image = id ? getImageById(id) : undefined;\n  useEffect(() => {\n    if (!image) {\n      navigate('/');\n    }\n  }, [image, navigate]);\n  if (!image) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Kh\\xF4ng t\\xECm th\\u1EA5y \\u1EA3nh\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 12\n    }, this);\n  }\n  const getImageCoordinates = (clientX, clientY) => {\n    if (!imageRef.current || !containerRef.current) return null;\n    const imageRect = imageRef.current.getBoundingClientRect();\n    const containerRect = containerRef.current.getBoundingClientRect();\n\n    // Calculate coordinates relative to the image element\n    const x = clientX - imageRect.left;\n    const y = clientY - imageRect.top;\n\n    // Convert to relative coordinates (0-1) based on actual image dimensions\n    const relativeX = x / imageRect.width;\n    const relativeY = y / imageRect.height;\n\n    // Ensure coordinates are within bounds\n    const clampedX = Math.max(0, Math.min(1, relativeX));\n    const clampedY = Math.max(0, Math.min(1, relativeY));\n    return {\n      x: clampedX,\n      y: clampedY\n    };\n  };\n  const handleMouseDown = e => {\n    if (!isCreatingAnnotation) return;\n    const coords = getImageCoordinates(e.clientX, e.clientY);\n    if (coords) {\n      setStartPoint(coords);\n    }\n  };\n  const handleMouseMove = e => {\n    if (!isCreatingAnnotation || !startPoint) return;\n    const coords = getImageCoordinates(e.clientX, e.clientY);\n    if (coords) {\n      const width = Math.abs(coords.x - startPoint.x);\n      const height = Math.abs(coords.y - startPoint.y);\n      const x = Math.min(startPoint.x, coords.x);\n      const y = Math.min(startPoint.y, coords.y);\n      setCurrentRect({\n        x,\n        y,\n        width,\n        height\n      });\n    }\n  };\n  const handleMouseUp = e => {\n    if (!isCreatingAnnotation || !startPoint || !currentRect) return;\n\n    // Only create annotation if rectangle is large enough\n    if (currentRect.width > 0.01 && currentRect.height > 0.01) {\n      const newAnnotation = {\n        x: currentRect.x,\n        y: currentRect.y,\n        width: currentRect.width,\n        height: currentRect.height,\n        content: '# Ghi chú mới\\n\\nNhập nội dung markdown tại đây...',\n        title: 'Ghi chú mới'\n      };\n      addAnnotation(image.id, newAnnotation);\n    }\n    setIsCreatingAnnotation(false);\n    setStartPoint(null);\n    setCurrentRect(null);\n  };\n  const handleAnnotationClick = annotation => {\n    setSelectedAnnotation(annotation);\n  };\n  const handleAnnotationSave = (content, title) => {\n    if (selectedAnnotation) {\n      updateAnnotation(image.id, selectedAnnotation.id, {\n        content,\n        title\n      });\n      setSelectedAnnotation(null);\n    }\n  };\n  const handleAnnotationDelete = () => {\n    if (selectedAnnotation) {\n      deleteAnnotation(image.id, selectedAnnotation.id);\n      setSelectedAnnotation(null);\n    }\n  };\n  const handleImageDelete = () => {\n    if (window.confirm('Bạn có chắc chắn muốn xóa ảnh này? Tất cả ghi chú sẽ bị mất.')) {\n      deleteImage(image.id);\n      navigate('/');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"image-detail-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"detail-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/'),\n        className: \"back-button\",\n        children: \"\\u2190 Quay l\\u1EA1i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: image.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `create-annotation-btn ${isCreatingAnnotation ? 'active' : ''}`,\n          onClick: () => setIsCreatingAnnotation(!isCreatingAnnotation),\n          children: isCreatingAnnotation ? 'Hủy tạo vùng' : 'Tạo vùng ghi chú'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"delete-image-btn\",\n          onClick: handleImageDelete,\n          children: \"X\\xF3a \\u1EA3nh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"detail-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"image-container\",\n        ref: containerRef,\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          ref: imageRef,\n          src: image.url,\n          alt: image.name,\n          className: `main-image ${isCreatingAnnotation ? 'creating' : ''}`,\n          onMouseDown: handleMouseDown,\n          onMouseMove: handleMouseMove,\n          onMouseUp: handleMouseUp,\n          draggable: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), image.annotations.map(annotation => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"annotation-overlay\",\n          style: {\n            left: `${annotation.x * 100}%`,\n            top: `${annotation.y * 100}%`,\n            width: `${annotation.width * 100}%`,\n            height: `${annotation.height * 100}%`\n          },\n          onClick: () => handleAnnotationClick(annotation),\n          onMouseEnter: () => setHoveredAnnotation(annotation),\n          onMouseLeave: () => setHoveredAnnotation(null)\n        }, annotation.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this)), currentRect && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"annotation-overlay creating\",\n          style: {\n            left: `${currentRect.x * 100}%`,\n            top: `${currentRect.y * 100}%`,\n            width: `${currentRect.width * 100}%`,\n            height: `${currentRect.height * 100}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), hoveredAnnotation && !selectedAnnotation && /*#__PURE__*/_jsxDEV(AnnotationViewer, {\n        annotation: hoveredAnnotation,\n        position: \"left\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this), selectedAnnotation && /*#__PURE__*/_jsxDEV(AnnotationEditor, {\n        annotation: selectedAnnotation,\n        onSave: handleAnnotationSave,\n        onDelete: handleAnnotationDelete,\n        onClose: () => setSelectedAnnotation(null)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n};\n_s(ImageDetailPage, \"PWv/xK+9m4wgCpTqOR3ZvW3smTQ=\", false, function () {\n  return [useParams, useNavigate, useImageContext];\n});\n_c = ImageDetailPage;\nexport default ImageDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ImageDetailPage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useParams", "useNavigate", "useImageContext", "AnnotationViewer", "AnnotationEditor", "jsxDEV", "_jsxDEV", "ImageDetailPage", "_s", "id", "navigate", "getImageById", "addAnnotation", "updateAnnotation", "deleteAnnotation", "deleteImage", "isCreatingAnnotation", "setIsCreatingAnnotation", "selectedAnnotation", "setSelectedAnnotation", "hoveredAnnotation", "setHoveredAnnotation", "startPoint", "setStartPoint", "currentRect", "setCurrentRect", "imageRef", "containerRef", "image", "undefined", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getImageCoordinates", "clientX", "clientY", "current", "imageRect", "getBoundingClientRect", "containerRect", "x", "left", "y", "top", "relativeX", "width", "relativeY", "height", "clampedX", "Math", "max", "min", "clampedY", "handleMouseDown", "e", "coords", "handleMouseMove", "abs", "handleMouseUp", "newAnnotation", "content", "title", "handleAnnotationClick", "annotation", "handleAnnotationSave", "handleAnnotationDelete", "handleImageDelete", "window", "confirm", "className", "onClick", "name", "ref", "src", "url", "alt", "onMouseDown", "onMouseMove", "onMouseUp", "draggable", "annotations", "map", "style", "onMouseEnter", "onMouseLeave", "position", "onSave", "onDelete", "onClose", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/pages/ImageDetailPage.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useImageContext } from '../contexts/ImageContext';\nimport AnnotationViewer from '../components/AnnotationViewer';\nimport AnnotationEditor from '../components/AnnotationEditor';\nimport { Annotation, Point } from '../types';\nimport './ImageDetailPage.css';\n\nconst ImageDetailPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const { getImageById, addAnnotation, updateAnnotation, deleteAnnotation, deleteImage } = useImageContext();\n  const [isCreatingAnnotation, setIsCreatingAnnotation] = useState(false);\n  const [selectedAnnotation, setSelectedAnnotation] = useState<Annotation | null>(null);\n  const [hoveredAnnotation, setHoveredAnnotation] = useState<Annotation | null>(null);\n  const [startPoint, setStartPoint] = useState<Point | null>(null);\n  const [currentRect, setCurrentRect] = useState<{ x: number; y: number; width: number; height: number } | null>(null);\n  const imageRef = useRef<HTMLImageElement>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  const image = id ? getImageById(id) : undefined;\n\n  useEffect(() => {\n    if (!image) {\n      navigate('/');\n    }\n  }, [image, navigate]);\n\n  if (!image) {\n    return <div>Không tìm thấy ảnh</div>;\n  }\n\n  const getImageCoordinates = (clientX: number, clientY: number) => {\n    if (!imageRef.current || !containerRef.current) return null;\n\n    const imageRect = imageRef.current.getBoundingClientRect();\n    const containerRect = containerRef.current.getBoundingClientRect();\n\n    // Calculate coordinates relative to the image element\n    const x = clientX - imageRect.left;\n    const y = clientY - imageRect.top;\n\n    // Convert to relative coordinates (0-1) based on actual image dimensions\n    const relativeX = x / imageRect.width;\n    const relativeY = y / imageRect.height;\n\n    // Ensure coordinates are within bounds\n    const clampedX = Math.max(0, Math.min(1, relativeX));\n    const clampedY = Math.max(0, Math.min(1, relativeY));\n\n    return { x: clampedX, y: clampedY };\n  };\n\n  const handleMouseDown = (e: React.MouseEvent) => {\n    if (!isCreatingAnnotation) return;\n    \n    const coords = getImageCoordinates(e.clientX, e.clientY);\n    if (coords) {\n      setStartPoint(coords);\n    }\n  };\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    if (!isCreatingAnnotation || !startPoint) return;\n    \n    const coords = getImageCoordinates(e.clientX, e.clientY);\n    if (coords) {\n      const width = Math.abs(coords.x - startPoint.x);\n      const height = Math.abs(coords.y - startPoint.y);\n      const x = Math.min(startPoint.x, coords.x);\n      const y = Math.min(startPoint.y, coords.y);\n      \n      setCurrentRect({ x, y, width, height });\n    }\n  };\n\n  const handleMouseUp = (e: React.MouseEvent) => {\n    if (!isCreatingAnnotation || !startPoint || !currentRect) return;\n    \n    // Only create annotation if rectangle is large enough\n    if (currentRect.width > 0.01 && currentRect.height > 0.01) {\n      const newAnnotation = {\n        x: currentRect.x,\n        y: currentRect.y,\n        width: currentRect.width,\n        height: currentRect.height,\n        content: '# Ghi chú mới\\n\\nNhập nội dung markdown tại đây...',\n        title: 'Ghi chú mới'\n      };\n      \n      addAnnotation(image.id, newAnnotation);\n    }\n    \n    setIsCreatingAnnotation(false);\n    setStartPoint(null);\n    setCurrentRect(null);\n  };\n\n  const handleAnnotationClick = (annotation: Annotation) => {\n    setSelectedAnnotation(annotation);\n  };\n\n  const handleAnnotationSave = (content: string, title?: string) => {\n    if (selectedAnnotation) {\n      updateAnnotation(image.id, selectedAnnotation.id, { content, title });\n      setSelectedAnnotation(null);\n    }\n  };\n\n  const handleAnnotationDelete = () => {\n    if (selectedAnnotation) {\n      deleteAnnotation(image.id, selectedAnnotation.id);\n      setSelectedAnnotation(null);\n    }\n  };\n\n  const handleImageDelete = () => {\n    if (window.confirm('Bạn có chắc chắn muốn xóa ảnh này? Tất cả ghi chú sẽ bị mất.')) {\n      deleteImage(image.id);\n      navigate('/');\n    }\n  };\n\n  return (\n    <div className=\"image-detail-page\">\n      <header className=\"detail-header\">\n        <button onClick={() => navigate('/')} className=\"back-button\">\n          ← Quay lại\n        </button>\n        <h1>{image.name}</h1>\n        <div className=\"toolbar\">\n          <button\n            className={`create-annotation-btn ${isCreatingAnnotation ? 'active' : ''}`}\n            onClick={() => setIsCreatingAnnotation(!isCreatingAnnotation)}\n          >\n            {isCreatingAnnotation ? 'Hủy tạo vùng' : 'Tạo vùng ghi chú'}\n          </button>\n          <button\n            className=\"delete-image-btn\"\n            onClick={handleImageDelete}\n          >\n            Xóa ảnh\n          </button>\n        </div>\n      </header>\n\n      <div className=\"detail-content\">\n        <div className=\"image-container\" ref={containerRef}>\n          <img \n            ref={imageRef}\n            src={image.url} \n            alt={image.name}\n            className={`main-image ${isCreatingAnnotation ? 'creating' : ''}`}\n            onMouseDown={handleMouseDown}\n            onMouseMove={handleMouseMove}\n            onMouseUp={handleMouseUp}\n            draggable={false}\n          />\n          \n          {/* Render existing annotations */}\n          {image.annotations.map(annotation => (\n            <div\n              key={annotation.id}\n              className=\"annotation-overlay\"\n              style={{\n                left: `${annotation.x * 100}%`,\n                top: `${annotation.y * 100}%`,\n                width: `${annotation.width * 100}%`,\n                height: `${annotation.height * 100}%`,\n              }}\n              onClick={() => handleAnnotationClick(annotation)}\n              onMouseEnter={() => setHoveredAnnotation(annotation)}\n              onMouseLeave={() => setHoveredAnnotation(null)}\n            />\n          ))}\n          \n          {/* Render current drawing rectangle */}\n          {currentRect && (\n            <div\n              className=\"annotation-overlay creating\"\n              style={{\n                left: `${currentRect.x * 100}%`,\n                top: `${currentRect.y * 100}%`,\n                width: `${currentRect.width * 100}%`,\n                height: `${currentRect.height * 100}%`,\n              }}\n            />\n          )}\n        </div>\n\n        {/* Annotation viewer for hover */}\n        {hoveredAnnotation && !selectedAnnotation && (\n          <AnnotationViewer \n            annotation={hoveredAnnotation}\n            position=\"left\"\n          />\n        )}\n\n        {/* Annotation editor */}\n        {selectedAnnotation && (\n          <AnnotationEditor\n            annotation={selectedAnnotation}\n            onSave={handleAnnotationSave}\n            onDelete={handleAnnotationDelete}\n            onClose={() => setSelectedAnnotation(null)}\n          />\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ImageDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,gBAAgB,MAAM,gCAAgC;AAE7D,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAG,CAAC,GAAGT,SAAS,CAAiB,CAAC;EAC1C,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU,YAAY;IAAEC,aAAa;IAAEC,gBAAgB;IAAEC,gBAAgB;IAAEC;EAAY,CAAC,GAAGb,eAAe,CAAC,CAAC;EAC1G,MAAM,CAACc,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACqB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtB,QAAQ,CAAoB,IAAI,CAAC;EACrF,MAAM,CAACuB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAoB,IAAI,CAAC;EACnF,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAe,IAAI,CAAC;EAChE,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAiE,IAAI,CAAC;EACpH,MAAM6B,QAAQ,GAAG5B,MAAM,CAAmB,IAAI,CAAC;EAC/C,MAAM6B,YAAY,GAAG7B,MAAM,CAAiB,IAAI,CAAC;EAEjD,MAAM8B,KAAK,GAAGnB,EAAE,GAAGE,YAAY,CAACF,EAAE,CAAC,GAAGoB,SAAS;EAE/C9B,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6B,KAAK,EAAE;MACVlB,QAAQ,CAAC,GAAG,CAAC;IACf;EACF,CAAC,EAAE,CAACkB,KAAK,EAAElB,QAAQ,CAAC,CAAC;EAErB,IAAI,CAACkB,KAAK,EAAE;IACV,oBAAOtB,OAAA;MAAAwB,QAAA,EAAK;IAAkB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACtC;EAEA,MAAMC,mBAAmB,GAAGA,CAACC,OAAe,EAAEC,OAAe,KAAK;IAChE,IAAI,CAACX,QAAQ,CAACY,OAAO,IAAI,CAACX,YAAY,CAACW,OAAO,EAAE,OAAO,IAAI;IAE3D,MAAMC,SAAS,GAAGb,QAAQ,CAACY,OAAO,CAACE,qBAAqB,CAAC,CAAC;IAC1D,MAAMC,aAAa,GAAGd,YAAY,CAACW,OAAO,CAACE,qBAAqB,CAAC,CAAC;;IAElE;IACA,MAAME,CAAC,GAAGN,OAAO,GAAGG,SAAS,CAACI,IAAI;IAClC,MAAMC,CAAC,GAAGP,OAAO,GAAGE,SAAS,CAACM,GAAG;;IAEjC;IACA,MAAMC,SAAS,GAAGJ,CAAC,GAAGH,SAAS,CAACQ,KAAK;IACrC,MAAMC,SAAS,GAAGJ,CAAC,GAAGL,SAAS,CAACU,MAAM;;IAEtC;IACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEP,SAAS,CAAC,CAAC;IACpD,MAAMQ,QAAQ,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEL,SAAS,CAAC,CAAC;IAEpD,OAAO;MAAEN,CAAC,EAAEQ,QAAQ;MAAEN,CAAC,EAAEU;IAAS,CAAC;EACrC,CAAC;EAED,MAAMC,eAAe,GAAIC,CAAmB,IAAK;IAC/C,IAAI,CAACxC,oBAAoB,EAAE;IAE3B,MAAMyC,MAAM,GAAGtB,mBAAmB,CAACqB,CAAC,CAACpB,OAAO,EAAEoB,CAAC,CAACnB,OAAO,CAAC;IACxD,IAAIoB,MAAM,EAAE;MACVlC,aAAa,CAACkC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMC,eAAe,GAAIF,CAAmB,IAAK;IAC/C,IAAI,CAACxC,oBAAoB,IAAI,CAACM,UAAU,EAAE;IAE1C,MAAMmC,MAAM,GAAGtB,mBAAmB,CAACqB,CAAC,CAACpB,OAAO,EAAEoB,CAAC,CAACnB,OAAO,CAAC;IACxD,IAAIoB,MAAM,EAAE;MACV,MAAMV,KAAK,GAAGI,IAAI,CAACQ,GAAG,CAACF,MAAM,CAACf,CAAC,GAAGpB,UAAU,CAACoB,CAAC,CAAC;MAC/C,MAAMO,MAAM,GAAGE,IAAI,CAACQ,GAAG,CAACF,MAAM,CAACb,CAAC,GAAGtB,UAAU,CAACsB,CAAC,CAAC;MAChD,MAAMF,CAAC,GAAGS,IAAI,CAACE,GAAG,CAAC/B,UAAU,CAACoB,CAAC,EAAEe,MAAM,CAACf,CAAC,CAAC;MAC1C,MAAME,CAAC,GAAGO,IAAI,CAACE,GAAG,CAAC/B,UAAU,CAACsB,CAAC,EAAEa,MAAM,CAACb,CAAC,CAAC;MAE1CnB,cAAc,CAAC;QAAEiB,CAAC;QAAEE,CAAC;QAAEG,KAAK;QAAEE;MAAO,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAMW,aAAa,GAAIJ,CAAmB,IAAK;IAC7C,IAAI,CAACxC,oBAAoB,IAAI,CAACM,UAAU,IAAI,CAACE,WAAW,EAAE;;IAE1D;IACA,IAAIA,WAAW,CAACuB,KAAK,GAAG,IAAI,IAAIvB,WAAW,CAACyB,MAAM,GAAG,IAAI,EAAE;MACzD,MAAMY,aAAa,GAAG;QACpBnB,CAAC,EAAElB,WAAW,CAACkB,CAAC;QAChBE,CAAC,EAAEpB,WAAW,CAACoB,CAAC;QAChBG,KAAK,EAAEvB,WAAW,CAACuB,KAAK;QACxBE,MAAM,EAAEzB,WAAW,CAACyB,MAAM;QAC1Ba,OAAO,EAAE,oDAAoD;QAC7DC,KAAK,EAAE;MACT,CAAC;MAEDnD,aAAa,CAACgB,KAAK,CAACnB,EAAE,EAAEoD,aAAa,CAAC;IACxC;IAEA5C,uBAAuB,CAAC,KAAK,CAAC;IAC9BM,aAAa,CAAC,IAAI,CAAC;IACnBE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMuC,qBAAqB,GAAIC,UAAsB,IAAK;IACxD9C,qBAAqB,CAAC8C,UAAU,CAAC;EACnC,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAACJ,OAAe,EAAEC,KAAc,KAAK;IAChE,IAAI7C,kBAAkB,EAAE;MACtBL,gBAAgB,CAACe,KAAK,CAACnB,EAAE,EAAES,kBAAkB,CAACT,EAAE,EAAE;QAAEqD,OAAO;QAAEC;MAAM,CAAC,CAAC;MACrE5C,qBAAqB,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC;EAED,MAAMgD,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAIjD,kBAAkB,EAAE;MACtBJ,gBAAgB,CAACc,KAAK,CAACnB,EAAE,EAAES,kBAAkB,CAACT,EAAE,CAAC;MACjDU,qBAAqB,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC;EAED,MAAMiD,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIC,MAAM,CAACC,OAAO,CAAC,8DAA8D,CAAC,EAAE;MAClFvD,WAAW,CAACa,KAAK,CAACnB,EAAE,CAAC;MACrBC,QAAQ,CAAC,GAAG,CAAC;IACf;EACF,CAAC;EAED,oBACEJ,OAAA;IAAKiE,SAAS,EAAC,mBAAmB;IAAAzC,QAAA,gBAChCxB,OAAA;MAAQiE,SAAS,EAAC,eAAe;MAAAzC,QAAA,gBAC/BxB,OAAA;QAAQkE,OAAO,EAAEA,CAAA,KAAM9D,QAAQ,CAAC,GAAG,CAAE;QAAC6D,SAAS,EAAC,aAAa;QAAAzC,QAAA,EAAC;MAE9D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT5B,OAAA;QAAAwB,QAAA,EAAKF,KAAK,CAAC6C;MAAI;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrB5B,OAAA;QAAKiE,SAAS,EAAC,SAAS;QAAAzC,QAAA,gBACtBxB,OAAA;UACEiE,SAAS,EAAE,yBAAyBvD,oBAAoB,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC3EwD,OAAO,EAAEA,CAAA,KAAMvD,uBAAuB,CAAC,CAACD,oBAAoB,CAAE;UAAAc,QAAA,EAE7Dd,oBAAoB,GAAG,cAAc,GAAG;QAAkB;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACT5B,OAAA;UACEiE,SAAS,EAAC,kBAAkB;UAC5BC,OAAO,EAAEJ,iBAAkB;UAAAtC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET5B,OAAA;MAAKiE,SAAS,EAAC,gBAAgB;MAAAzC,QAAA,gBAC7BxB,OAAA;QAAKiE,SAAS,EAAC,iBAAiB;QAACG,GAAG,EAAE/C,YAAa;QAAAG,QAAA,gBACjDxB,OAAA;UACEoE,GAAG,EAAEhD,QAAS;UACdiD,GAAG,EAAE/C,KAAK,CAACgD,GAAI;UACfC,GAAG,EAAEjD,KAAK,CAAC6C,IAAK;UAChBF,SAAS,EAAE,cAAcvD,oBAAoB,GAAG,UAAU,GAAG,EAAE,EAAG;UAClE8D,WAAW,EAAEvB,eAAgB;UAC7BwB,WAAW,EAAErB,eAAgB;UAC7BsB,SAAS,EAAEpB,aAAc;UACzBqB,SAAS,EAAE;QAAM;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,EAGDN,KAAK,CAACsD,WAAW,CAACC,GAAG,CAAClB,UAAU,iBAC/B3D,OAAA;UAEEiE,SAAS,EAAC,oBAAoB;UAC9Ba,KAAK,EAAE;YACLzC,IAAI,EAAE,GAAGsB,UAAU,CAACvB,CAAC,GAAG,GAAG,GAAG;YAC9BG,GAAG,EAAE,GAAGoB,UAAU,CAACrB,CAAC,GAAG,GAAG,GAAG;YAC7BG,KAAK,EAAE,GAAGkB,UAAU,CAAClB,KAAK,GAAG,GAAG,GAAG;YACnCE,MAAM,EAAE,GAAGgB,UAAU,CAAChB,MAAM,GAAG,GAAG;UACpC,CAAE;UACFuB,OAAO,EAAEA,CAAA,KAAMR,qBAAqB,CAACC,UAAU,CAAE;UACjDoB,YAAY,EAAEA,CAAA,KAAMhE,oBAAoB,CAAC4C,UAAU,CAAE;UACrDqB,YAAY,EAAEA,CAAA,KAAMjE,oBAAoB,CAAC,IAAI;QAAE,GAV1C4C,UAAU,CAACxD,EAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWnB,CACF,CAAC,EAGDV,WAAW,iBACVlB,OAAA;UACEiE,SAAS,EAAC,6BAA6B;UACvCa,KAAK,EAAE;YACLzC,IAAI,EAAE,GAAGnB,WAAW,CAACkB,CAAC,GAAG,GAAG,GAAG;YAC/BG,GAAG,EAAE,GAAGrB,WAAW,CAACoB,CAAC,GAAG,GAAG,GAAG;YAC9BG,KAAK,EAAE,GAAGvB,WAAW,CAACuB,KAAK,GAAG,GAAG,GAAG;YACpCE,MAAM,EAAE,GAAGzB,WAAW,CAACyB,MAAM,GAAG,GAAG;UACrC;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLd,iBAAiB,IAAI,CAACF,kBAAkB,iBACvCZ,OAAA,CAACH,gBAAgB;QACf8D,UAAU,EAAE7C,iBAAkB;QAC9BmE,QAAQ,EAAC;MAAM;QAAAxD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CACF,EAGAhB,kBAAkB,iBACjBZ,OAAA,CAACF,gBAAgB;QACf6D,UAAU,EAAE/C,kBAAmB;QAC/BsE,MAAM,EAAEtB,oBAAqB;QAC7BuB,QAAQ,EAAEtB,sBAAuB;QACjCuB,OAAO,EAAEA,CAAA,KAAMvE,qBAAqB,CAAC,IAAI;MAAE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA1MID,eAAyB;EAAA,QACdP,SAAS,EACPC,WAAW,EAC6DC,eAAe;AAAA;AAAAyF,EAAA,GAHpGpF,eAAyB;AA4M/B,eAAeA,eAAe;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}