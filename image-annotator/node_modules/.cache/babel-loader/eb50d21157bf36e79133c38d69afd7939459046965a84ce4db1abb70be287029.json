{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/AnnotationViewer.tsx\";\nimport React from 'react';\nimport ReactMarkdown from 'react-markdown';\nimport './AnnotationViewer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnnotationViewer = ({\n  annotation,\n  position\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `annotation-viewer ${position}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"annotation-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: annotation.title || 'Ghi chú'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"annotation-content\",\n      children: /*#__PURE__*/_jsxDEV(ReactMarkdown, {\n        children: annotation.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_c = AnnotationViewer;\nexport default AnnotationViewer;\nvar _c;\n$RefreshReg$(_c, \"AnnotationViewer\");", "map": {"version": 3, "names": ["React", "ReactMarkdown", "jsxDEV", "_jsxDEV", "AnnotationViewer", "annotation", "position", "className", "children", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/AnnotationViewer.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactMarkdown from 'react-markdown';\nimport { Annotation } from '../types';\nimport './AnnotationViewer.css';\n\ninterface AnnotationViewerProps {\n  annotation: Annotation;\n  position: 'left' | 'right';\n}\n\nconst AnnotationViewer: React.FC<AnnotationViewerProps> = ({ annotation, position }) => {\n  return (\n    <div className={`annotation-viewer ${position}`}>\n      <div className=\"annotation-header\">\n        <h3>{annotation.title || 'Ghi chú'}</h3>\n      </div>\n      <div className=\"annotation-content\">\n        <ReactMarkdown>{annotation.content}</ReactMarkdown>\n      </div>\n    </div>\n  );\n};\n\nexport default AnnotationViewer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,gBAAgB;AAE1C,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOhC,MAAMC,gBAAiD,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAS,CAAC,KAAK;EACtF,oBACEH,OAAA;IAAKI,SAAS,EAAE,qBAAqBD,QAAQ,EAAG;IAAAE,QAAA,gBAC9CL,OAAA;MAAKI,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCL,OAAA;QAAAK,QAAA,EAAKH,UAAU,CAACI,KAAK,IAAI;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eACNV,OAAA;MAAKI,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjCL,OAAA,CAACF,aAAa;QAAAO,QAAA,EAAEH,UAAU,CAACS;MAAO;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GAXIX,gBAAiD;AAavD,eAAeA,gBAAgB;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}