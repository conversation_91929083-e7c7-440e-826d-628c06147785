{"ast": null, "code": "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { asciiAlphanumeric, asciiAlpha, asciiAtext, asciiControl } from 'micromark-util-character';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const autolink = {\n  name: 'autolink',\n  tokenize: tokenizeAutolink\n};\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeAutolink(effects, ok, nok) {\n  let size = 0;\n  return start;\n\n  /**\n   * Start of an autolink.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *      ^\n   * > | a<<EMAIL>>b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.lessThan, 'expected `<`');\n    effects.enter(types.autolink);\n    effects.enter(types.autolinkMarker);\n    effects.consume(code);\n    effects.exit(types.autolinkMarker);\n    effects.enter(types.autolinkProtocol);\n    return open;\n  }\n\n  /**\n   * After `<`, at protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *       ^\n   * > | a<<EMAIL>>b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (asciiAlpha(code)) {\n      effects.consume(code);\n      return schemeOrEmailAtext;\n    }\n    if (code === codes.atSign) {\n      return nok(code);\n    }\n    return emailAtext(code);\n  }\n\n  /**\n   * At second byte of protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeOrEmailAtext(code) {\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (code === codes.plusSign || code === codes.dash || code === codes.dot || asciiAlphanumeric(code)) {\n      // Count the previous alphabetical from `open` too.\n      size = 1;\n      return schemeInsideOrEmailAtext(code);\n    }\n    return emailAtext(code);\n  }\n\n  /**\n   * In ambiguous protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeInsideOrEmailAtext(code) {\n    if (code === codes.colon) {\n      effects.consume(code);\n      size = 0;\n      return urlInside;\n    }\n\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if ((code === codes.plusSign || code === codes.dash || code === codes.dot || asciiAlphanumeric(code)) && size++ < constants.autolinkSchemeSizeMax) {\n      effects.consume(code);\n      return schemeInsideOrEmailAtext;\n    }\n    size = 0;\n    return emailAtext(code);\n  }\n\n  /**\n   * After protocol, in URL.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function urlInside(code) {\n    if (code === codes.greaterThan) {\n      effects.exit(types.autolinkProtocol);\n      effects.enter(types.autolinkMarker);\n      effects.consume(code);\n      effects.exit(types.autolinkMarker);\n      effects.exit(types.autolink);\n      return ok;\n    }\n\n    // ASCII control, space, or `<`.\n    if (code === codes.eof || code === codes.space || code === codes.lessThan || asciiControl(code)) {\n      return nok(code);\n    }\n    effects.consume(code);\n    return urlInside;\n  }\n\n  /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtext(code) {\n    if (code === codes.atSign) {\n      effects.consume(code);\n      return emailAtSignOrDot;\n    }\n    if (asciiAtext(code)) {\n      effects.consume(code);\n      return emailAtext;\n    }\n    return nok(code);\n  }\n\n  /**\n   * In label, after at-sign or dot.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                 ^       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtSignOrDot(code) {\n    return asciiAlphanumeric(code) ? emailLabel(code) : nok(code);\n  }\n\n  /**\n   * In label, where `.` and `>` are allowed.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailLabel(code) {\n    if (code === codes.dot) {\n      effects.consume(code);\n      size = 0;\n      return emailAtSignOrDot;\n    }\n    if (code === codes.greaterThan) {\n      // Exit, then change the token type.\n      effects.exit(types.autolinkProtocol).type = types.autolinkEmail;\n      effects.enter(types.autolinkMarker);\n      effects.consume(code);\n      effects.exit(types.autolinkMarker);\n      effects.exit(types.autolink);\n      return ok;\n    }\n    return emailValue(code);\n  }\n\n  /**\n   * In label, where `.` and `>` are *not* allowed.\n   *\n   * Though, this is also used in `emailLabel` to parse other values.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailValue(code) {\n    // ASCII alphanumeric or `-`.\n    if ((code === codes.dash || asciiAlphanumeric(code)) && size++ < constants.autolinkDomainSizeMax) {\n      const next = code === codes.dash ? emailValue : emailLabel;\n      effects.consume(code);\n      return next;\n    }\n    return nok(code);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "asciiAlphanumeric", "asciiAlpha", "asciiAtext", "asciiControl", "codes", "constants", "types", "autolink", "name", "tokenize", "tokenizeAutolink", "effects", "nok", "size", "start", "code", "lessThan", "enter", "autolinkMarker", "consume", "exit", "autolinkProtocol", "open", "schemeOrEmailAtext", "atSign", "emailAtext", "plusSign", "dash", "dot", "schemeInsideOrEmailAtext", "colon", "urlInside", "autolinkSchemeSizeMax", "greaterThan", "eof", "space", "emailAtSignOrDot", "emailLabel", "type", "autolinkEmail", "emailValue", "autolinkDomainSizeMax", "next"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark-core-commonmark/dev/lib/autolink.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {\n  asciiAlphanumeric,\n  asciiAlpha,\n  asciiAtext,\n  asciiControl\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const autolink = {name: 'autolink', tokenize: tokenizeAutolink}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeAutolink(effects, ok, nok) {\n  let size = 0\n\n  return start\n\n  /**\n   * Start of an autolink.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *      ^\n   * > | a<<EMAIL>>b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.lessThan, 'expected `<`')\n    effects.enter(types.autolink)\n    effects.enter(types.autolinkMarker)\n    effects.consume(code)\n    effects.exit(types.autolinkMarker)\n    effects.enter(types.autolinkProtocol)\n    return open\n  }\n\n  /**\n   * After `<`, at protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *       ^\n   * > | a<<EMAIL>>b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return schemeOrEmailAtext\n    }\n\n    if (code === codes.atSign) {\n      return nok(code)\n    }\n\n    return emailAtext(code)\n  }\n\n  /**\n   * At second byte of protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeOrEmailAtext(code) {\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (\n      code === codes.plusSign ||\n      code === codes.dash ||\n      code === codes.dot ||\n      asciiAlphanumeric(code)\n    ) {\n      // Count the previous alphabetical from `open` too.\n      size = 1\n      return schemeInsideOrEmailAtext(code)\n    }\n\n    return emailAtext(code)\n  }\n\n  /**\n   * In ambiguous protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeInsideOrEmailAtext(code) {\n    if (code === codes.colon) {\n      effects.consume(code)\n      size = 0\n      return urlInside\n    }\n\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (\n      (code === codes.plusSign ||\n        code === codes.dash ||\n        code === codes.dot ||\n        asciiAlphanumeric(code)) &&\n      size++ < constants.autolinkSchemeSizeMax\n    ) {\n      effects.consume(code)\n      return schemeInsideOrEmailAtext\n    }\n\n    size = 0\n    return emailAtext(code)\n  }\n\n  /**\n   * After protocol, in URL.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function urlInside(code) {\n    if (code === codes.greaterThan) {\n      effects.exit(types.autolinkProtocol)\n      effects.enter(types.autolinkMarker)\n      effects.consume(code)\n      effects.exit(types.autolinkMarker)\n      effects.exit(types.autolink)\n      return ok\n    }\n\n    // ASCII control, space, or `<`.\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.lessThan ||\n      asciiControl(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return urlInside\n  }\n\n  /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtext(code) {\n    if (code === codes.atSign) {\n      effects.consume(code)\n      return emailAtSignOrDot\n    }\n\n    if (asciiAtext(code)) {\n      effects.consume(code)\n      return emailAtext\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In label, after at-sign or dot.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                 ^       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtSignOrDot(code) {\n    return asciiAlphanumeric(code) ? emailLabel(code) : nok(code)\n  }\n\n  /**\n   * In label, where `.` and `>` are allowed.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailLabel(code) {\n    if (code === codes.dot) {\n      effects.consume(code)\n      size = 0\n      return emailAtSignOrDot\n    }\n\n    if (code === codes.greaterThan) {\n      // Exit, then change the token type.\n      effects.exit(types.autolinkProtocol).type = types.autolinkEmail\n      effects.enter(types.autolinkMarker)\n      effects.consume(code)\n      effects.exit(types.autolinkMarker)\n      effects.exit(types.autolink)\n      return ok\n    }\n\n    return emailValue(code)\n  }\n\n  /**\n   * In label, where `.` and `>` are *not* allowed.\n   *\n   * Though, this is also used in `emailLabel` to parse other values.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailValue(code) {\n    // ASCII alphanumeric or `-`.\n    if (\n      (code === codes.dash || asciiAlphanumeric(code)) &&\n      size++ < constants.autolinkDomainSizeMax\n    ) {\n      const next = code === codes.dash ? emailValue : emailLabel\n      effects.consume(code)\n      return next\n    }\n\n    return nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SACEC,iBAAiB,EACjBC,UAAU,EACVC,UAAU,EACVC,YAAY,QACP,0BAA0B;AACjC,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;;AAE7D;AACA,OAAO,MAAMC,QAAQ,GAAG;EAACC,IAAI,EAAE,UAAU;EAAEC,QAAQ,EAAEC;AAAgB,CAAC;;AAEtE;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAgBA,CAACC,OAAO,EAAEb,EAAE,EAAEc,GAAG,EAAE;EAC1C,IAAIC,IAAI,GAAG,CAAC;EAEZ,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBhB,MAAM,CAACgB,IAAI,KAAKX,KAAK,CAACY,QAAQ,EAAE,cAAc,CAAC;IAC/CL,OAAO,CAACM,KAAK,CAACX,KAAK,CAACC,QAAQ,CAAC;IAC7BI,OAAO,CAACM,KAAK,CAACX,KAAK,CAACY,cAAc,CAAC;IACnCP,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;IACrBJ,OAAO,CAACS,IAAI,CAACd,KAAK,CAACY,cAAc,CAAC;IAClCP,OAAO,CAACM,KAAK,CAACX,KAAK,CAACe,gBAAgB,CAAC;IACrC,OAAOC,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,IAAIA,CAACP,IAAI,EAAE;IAClB,IAAId,UAAU,CAACc,IAAI,CAAC,EAAE;MACpBJ,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOQ,kBAAkB;IAC3B;IAEA,IAAIR,IAAI,KAAKX,KAAK,CAACoB,MAAM,EAAE;MACzB,OAAOZ,GAAG,CAACG,IAAI,CAAC;IAClB;IAEA,OAAOU,UAAU,CAACV,IAAI,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASQ,kBAAkBA,CAACR,IAAI,EAAE;IAChC;IACA,IACEA,IAAI,KAAKX,KAAK,CAACsB,QAAQ,IACvBX,IAAI,KAAKX,KAAK,CAACuB,IAAI,IACnBZ,IAAI,KAAKX,KAAK,CAACwB,GAAG,IAClB5B,iBAAiB,CAACe,IAAI,CAAC,EACvB;MACA;MACAF,IAAI,GAAG,CAAC;MACR,OAAOgB,wBAAwB,CAACd,IAAI,CAAC;IACvC;IAEA,OAAOU,UAAU,CAACV,IAAI,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASc,wBAAwBA,CAACd,IAAI,EAAE;IACtC,IAAIA,IAAI,KAAKX,KAAK,CAAC0B,KAAK,EAAE;MACxBnB,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrBF,IAAI,GAAG,CAAC;MACR,OAAOkB,SAAS;IAClB;;IAEA;IACA,IACE,CAAChB,IAAI,KAAKX,KAAK,CAACsB,QAAQ,IACtBX,IAAI,KAAKX,KAAK,CAACuB,IAAI,IACnBZ,IAAI,KAAKX,KAAK,CAACwB,GAAG,IAClB5B,iBAAiB,CAACe,IAAI,CAAC,KACzBF,IAAI,EAAE,GAAGR,SAAS,CAAC2B,qBAAqB,EACxC;MACArB,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOc,wBAAwB;IACjC;IAEAhB,IAAI,GAAG,CAAC;IACR,OAAOY,UAAU,CAACV,IAAI,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASgB,SAASA,CAAChB,IAAI,EAAE;IACvB,IAAIA,IAAI,KAAKX,KAAK,CAAC6B,WAAW,EAAE;MAC9BtB,OAAO,CAACS,IAAI,CAACd,KAAK,CAACe,gBAAgB,CAAC;MACpCV,OAAO,CAACM,KAAK,CAACX,KAAK,CAACY,cAAc,CAAC;MACnCP,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrBJ,OAAO,CAACS,IAAI,CAACd,KAAK,CAACY,cAAc,CAAC;MAClCP,OAAO,CAACS,IAAI,CAACd,KAAK,CAACC,QAAQ,CAAC;MAC5B,OAAOT,EAAE;IACX;;IAEA;IACA,IACEiB,IAAI,KAAKX,KAAK,CAAC8B,GAAG,IAClBnB,IAAI,KAAKX,KAAK,CAAC+B,KAAK,IACpBpB,IAAI,KAAKX,KAAK,CAACY,QAAQ,IACvBb,YAAY,CAACY,IAAI,CAAC,EAClB;MACA,OAAOH,GAAG,CAACG,IAAI,CAAC;IAClB;IAEAJ,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;IACrB,OAAOgB,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASN,UAAUA,CAACV,IAAI,EAAE;IACxB,IAAIA,IAAI,KAAKX,KAAK,CAACoB,MAAM,EAAE;MACzBb,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOqB,gBAAgB;IACzB;IAEA,IAAIlC,UAAU,CAACa,IAAI,CAAC,EAAE;MACpBJ,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOU,UAAU;IACnB;IAEA,OAAOb,GAAG,CAACG,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASqB,gBAAgBA,CAACrB,IAAI,EAAE;IAC9B,OAAOf,iBAAiB,CAACe,IAAI,CAAC,GAAGsB,UAAU,CAACtB,IAAI,CAAC,GAAGH,GAAG,CAACG,IAAI,CAAC;EAC/D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsB,UAAUA,CAACtB,IAAI,EAAE;IACxB,IAAIA,IAAI,KAAKX,KAAK,CAACwB,GAAG,EAAE;MACtBjB,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrBF,IAAI,GAAG,CAAC;MACR,OAAOuB,gBAAgB;IACzB;IAEA,IAAIrB,IAAI,KAAKX,KAAK,CAAC6B,WAAW,EAAE;MAC9B;MACAtB,OAAO,CAACS,IAAI,CAACd,KAAK,CAACe,gBAAgB,CAAC,CAACiB,IAAI,GAAGhC,KAAK,CAACiC,aAAa;MAC/D5B,OAAO,CAACM,KAAK,CAACX,KAAK,CAACY,cAAc,CAAC;MACnCP,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrBJ,OAAO,CAACS,IAAI,CAACd,KAAK,CAACY,cAAc,CAAC;MAClCP,OAAO,CAACS,IAAI,CAACd,KAAK,CAACC,QAAQ,CAAC;MAC5B,OAAOT,EAAE;IACX;IAEA,OAAO0C,UAAU,CAACzB,IAAI,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASyB,UAAUA,CAACzB,IAAI,EAAE;IACxB;IACA,IACE,CAACA,IAAI,KAAKX,KAAK,CAACuB,IAAI,IAAI3B,iBAAiB,CAACe,IAAI,CAAC,KAC/CF,IAAI,EAAE,GAAGR,SAAS,CAACoC,qBAAqB,EACxC;MACA,MAAMC,IAAI,GAAG3B,IAAI,KAAKX,KAAK,CAACuB,IAAI,GAAGa,UAAU,GAAGH,UAAU;MAC1D1B,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAO2B,IAAI;IACb;IAEA,OAAO9B,GAAG,CAACG,IAAI,CAAC;EAClB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}