{"ast": null, "code": "/**\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [jsx=false]\n *   Support JSX identifiers (default: `false`).\n */\n\nconst startRe = /[$_\\p{ID_Start}]/u;\nconst contRe = /[$_\\u{200C}\\u{200D}\\p{ID_Continue}]/u;\nconst contReJsx = /[-$_\\u{200C}\\u{200D}\\p{ID_Continue}]/u;\nconst nameRe = /^[$_\\p{ID_Start}][$_\\u{200C}\\u{200D}\\p{ID_Continue}]*$/u;\nconst nameReJsx = /^[$_\\p{ID_Start}][-$_\\u{200C}\\u{200D}\\p{ID_Continue}]*$/u;\n\n/** @type {Options} */\nconst emptyOptions = {};\n\n/**\n * Checks if the given code point can start an identifier.\n *\n * @param {number | undefined} code\n *   Code point to check.\n * @returns {boolean}\n *   Whether `code` can start an identifier.\n */\n// Note: `undefined` is supported so you can pass the result from `''.codePointAt`.\nexport function start(code) {\n  return code ? startRe.test(String.fromCodePoint(code)) : false;\n}\n\n/**\n * Checks if the given code point can continue an identifier.\n *\n * @param {number | undefined} code\n *   Code point to check.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {boolean}\n *   Whether `code` can continue an identifier.\n */\n// Note: `undefined` is supported so you can pass the result from `''.codePointAt`.\nexport function cont(code, options) {\n  const settings = options || emptyOptions;\n  const re = settings.jsx ? contReJsx : contRe;\n  return code ? re.test(String.fromCodePoint(code)) : false;\n}\n\n/**\n * Checks if the given value is a valid identifier name.\n *\n * @param {string} name\n *   Identifier to check.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {boolean}\n *   Whether `name` can be an identifier.\n */\nexport function name(name, options) {\n  const settings = options || emptyOptions;\n  const re = settings.jsx ? nameReJsx : nameRe;\n  return re.test(name);\n}", "map": {"version": 3, "names": ["startRe", "contRe", "contReJsx", "nameRe", "nameReJsx", "emptyOptions", "start", "code", "test", "String", "fromCodePoint", "cont", "options", "settings", "re", "jsx", "name"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/estree-util-is-identifier-name/lib/index.js"], "sourcesContent": ["/**\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [jsx=false]\n *   Support JSX identifiers (default: `false`).\n */\n\nconst startRe = /[$_\\p{ID_Start}]/u\nconst contRe = /[$_\\u{200C}\\u{200D}\\p{ID_Continue}]/u\nconst contReJsx = /[-$_\\u{200C}\\u{200D}\\p{ID_Continue}]/u\nconst nameRe = /^[$_\\p{ID_Start}][$_\\u{200C}\\u{200D}\\p{ID_Continue}]*$/u\nconst nameReJsx = /^[$_\\p{ID_Start}][-$_\\u{200C}\\u{200D}\\p{ID_Continue}]*$/u\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Checks if the given code point can start an identifier.\n *\n * @param {number | undefined} code\n *   Code point to check.\n * @returns {boolean}\n *   Whether `code` can start an identifier.\n */\n// Note: `undefined` is supported so you can pass the result from `''.codePointAt`.\nexport function start(code) {\n  return code ? startRe.test(String.fromCodePoint(code)) : false\n}\n\n/**\n * Checks if the given code point can continue an identifier.\n *\n * @param {number | undefined} code\n *   Code point to check.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {boolean}\n *   Whether `code` can continue an identifier.\n */\n// Note: `undefined` is supported so you can pass the result from `''.codePointAt`.\nexport function cont(code, options) {\n  const settings = options || emptyOptions\n  const re = settings.jsx ? contReJsx : contRe\n  return code ? re.test(String.fromCodePoint(code)) : false\n}\n\n/**\n * Checks if the given value is a valid identifier name.\n *\n * @param {string} name\n *   Identifier to check.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {boolean}\n *   Whether `name` can be an identifier.\n */\nexport function name(name, options) {\n  const settings = options || emptyOptions\n  const re = settings.jsx ? nameReJsx : nameRe\n  return re.test(name)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,OAAO,GAAG,mBAAmB;AACnC,MAAMC,MAAM,GAAG,sCAAsC;AACrD,MAAMC,SAAS,GAAG,uCAAuC;AACzD,MAAMC,MAAM,GAAG,yDAAyD;AACxE,MAAMC,SAAS,GAAG,0DAA0D;;AAE5E;AACA,MAAMC,YAAY,GAAG,CAAC,CAAC;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAE;EAC1B,OAAOA,IAAI,GAAGP,OAAO,CAACQ,IAAI,CAACC,MAAM,CAACC,aAAa,CAACH,IAAI,CAAC,CAAC,GAAG,KAAK;AAChE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,IAAIA,CAACJ,IAAI,EAAEK,OAAO,EAAE;EAClC,MAAMC,QAAQ,GAAGD,OAAO,IAAIP,YAAY;EACxC,MAAMS,EAAE,GAAGD,QAAQ,CAACE,GAAG,GAAGb,SAAS,GAAGD,MAAM;EAC5C,OAAOM,IAAI,GAAGO,EAAE,CAACN,IAAI,CAACC,MAAM,CAACC,aAAa,CAACH,IAAI,CAAC,CAAC,GAAG,KAAK;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,IAAIA,CAACA,IAAI,EAAEJ,OAAO,EAAE;EAClC,MAAMC,QAAQ,GAAGD,OAAO,IAAIP,YAAY;EACxC,MAAMS,EAAE,GAAGD,QAAQ,CAACE,GAAG,GAAGX,SAAS,GAAGD,MAAM;EAC5C,OAAOW,EAAE,CAACN,IAAI,CAACQ,IAAI,CAAC;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}