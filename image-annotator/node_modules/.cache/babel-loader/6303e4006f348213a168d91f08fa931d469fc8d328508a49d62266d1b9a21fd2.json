{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').List} List\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n'';\n\n/**\n * Turn an mdast `list` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {List} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function list(state, node) {\n  /** @type {Properties} */\n  const properties = {};\n  const results = state.all(node);\n  let index = -1;\n  if (typeof node.start === 'number' && node.start !== 1) {\n    properties.start = node.start;\n  }\n\n  // Like GitHub, add a class for custom styling.\n  while (++index < results.length) {\n    const child = results[index];\n    if (child.type === 'element' && child.tagName === 'li' && child.properties && Array.isArray(child.properties.className) && child.properties.className.includes('task-list-item')) {\n      properties.className = ['contains-task-list'];\n      break;\n    }\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: node.ordered ? 'ol' : 'ul',\n    properties,\n    children: state.wrap(results, true)\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["list", "state", "node", "properties", "results", "all", "index", "start", "length", "child", "type", "tagName", "Array", "isArray", "className", "includes", "result", "ordered", "children", "wrap", "patch", "applyData"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/mdast-util-to-hast/lib/handlers/list.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').List} List\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `list` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {List} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function list(state, node) {\n  /** @type {Properties} */\n  const properties = {}\n  const results = state.all(node)\n  let index = -1\n\n  if (typeof node.start === 'number' && node.start !== 1) {\n    properties.start = node.start\n  }\n\n  // Like GitHub, add a class for custom styling.\n  while (++index < results.length) {\n    const child = results[index]\n\n    if (\n      child.type === 'element' &&\n      child.tagName === 'li' &&\n      child.properties &&\n      Array.isArray(child.properties.className) &&\n      child.properties.className.includes('task-list-item')\n    ) {\n      properties.className = ['contains-task-list']\n      break\n    }\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: node.ordered ? 'ol' : 'ul',\n    properties,\n    children: state.wrap(results, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,IAAIA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAChC;EACA,MAAMC,UAAU,GAAG,CAAC,CAAC;EACrB,MAAMC,OAAO,GAAGH,KAAK,CAACI,GAAG,CAACH,IAAI,CAAC;EAC/B,IAAII,KAAK,GAAG,CAAC,CAAC;EAEd,IAAI,OAAOJ,IAAI,CAACK,KAAK,KAAK,QAAQ,IAAIL,IAAI,CAACK,KAAK,KAAK,CAAC,EAAE;IACtDJ,UAAU,CAACI,KAAK,GAAGL,IAAI,CAACK,KAAK;EAC/B;;EAEA;EACA,OAAO,EAAED,KAAK,GAAGF,OAAO,CAACI,MAAM,EAAE;IAC/B,MAAMC,KAAK,GAAGL,OAAO,CAACE,KAAK,CAAC;IAE5B,IACEG,KAAK,CAACC,IAAI,KAAK,SAAS,IACxBD,KAAK,CAACE,OAAO,KAAK,IAAI,IACtBF,KAAK,CAACN,UAAU,IAChBS,KAAK,CAACC,OAAO,CAACJ,KAAK,CAACN,UAAU,CAACW,SAAS,CAAC,IACzCL,KAAK,CAACN,UAAU,CAACW,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EACrD;MACAZ,UAAU,CAACW,SAAS,GAAG,CAAC,oBAAoB,CAAC;MAC7C;IACF;EACF;;EAEA;EACA,MAAME,MAAM,GAAG;IACbN,IAAI,EAAE,SAAS;IACfC,OAAO,EAAET,IAAI,CAACe,OAAO,GAAG,IAAI,GAAG,IAAI;IACnCd,UAAU;IACVe,QAAQ,EAAEjB,KAAK,CAACkB,IAAI,CAACf,OAAO,EAAE,IAAI;EACpC,CAAC;EACDH,KAAK,CAACmB,KAAK,CAAClB,IAAI,EAAEc,MAAM,CAAC;EACzB,OAAOf,KAAK,CAACoB,SAAS,CAACnB,IAAI,EAAEc,MAAM,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}