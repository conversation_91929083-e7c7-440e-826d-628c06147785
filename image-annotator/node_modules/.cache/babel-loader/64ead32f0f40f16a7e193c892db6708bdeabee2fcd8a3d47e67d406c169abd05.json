{"ast": null, "code": "// Note: types exposed from `index.d.ts`.\nexport { toJsxRuntime } from './lib/index.js';", "map": {"version": 3, "names": ["toJsxRuntime"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/hast-util-to-jsx-runtime/index.js"], "sourcesContent": ["// Note: types exposed from `index.d.ts`.\nexport {toJsxRuntime} from './lib/index.js'\n"], "mappings": "AAAA;AACA,SAAQA,YAAY,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}