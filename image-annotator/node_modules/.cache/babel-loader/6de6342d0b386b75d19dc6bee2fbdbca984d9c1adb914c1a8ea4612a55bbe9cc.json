{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/AnnotationEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport ReactMarkdown from 'react-markdown';\nimport './AnnotationEditor.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnnotationEditor = ({\n  annotation,\n  onSave,\n  onDelete,\n  onClose\n}) => {\n  _s();\n  const [content, setContent] = useState(annotation.content);\n  const [title, setTitle] = useState(annotation.title || '');\n  const [isPreview, setIsPreview] = useState(false);\n  useEffect(() => {\n    setContent(annotation.content);\n    setTitle(annotation.title || '');\n  }, [annotation]);\n  const handleSave = () => {\n    onSave(content, title);\n  };\n  const handleKeyDown = e => {\n    if (e.ctrlKey && e.key === 's') {\n      e.preventDefault();\n      handleSave();\n    }\n    if (e.key === 'Escape') {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"annotation-editor-overlay\",\n    onClick: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"annotation-editor\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Ti\\xEAu \\u0111\\u1EC1 ghi ch\\xFA\",\n          value: title,\n          onChange: e => setTitle(e.target.value),\n          className: \"title-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"editor-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `preview-btn ${isPreview ? 'active' : ''}`,\n            onClick: () => setIsPreview(!isPreview),\n            children: isPreview ? 'Chỉnh sửa' : 'Xem trước'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"save-btn\",\n            onClick: handleSave,\n            children: \"L\\u01B0u (Ctrl+S)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"delete-btn\",\n            onClick: onDelete,\n            children: \"X\\xF3a\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-btn\",\n            onClick: onClose,\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-content\",\n        children: isPreview ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-pane\",\n          children: /*#__PURE__*/_jsxDEV(ReactMarkdown, {\n            children: content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: content,\n          onChange: e => setContent(e.target.value),\n          onKeyDown: handleKeyDown,\n          placeholder: \"Nh\\u1EADp n\\u1ED9i dung markdown t\\u1EA1i \\u0111\\xE2y... V\\xED d\\u1EE5:\\n# Ti\\xEAu \\u0111\\u1EC1\\n## Ti\\xEAu \\u0111\\u1EC1 ph\\u1EE5 **Ch\\u1EEF \\u0111\\u1EADm** v\\xE0 *ch\\u1EEF nghi\\xEAng* - Danh s\\xE1ch\\n- M\\u1EE5c 2 ```code\\nKh\\u1ED1i code\\n``` > Tr\\xEDch d\\u1EABn\",\n          className: \"content-textarea\",\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"markdown-help\",\n          children: /*#__PURE__*/_jsxDEV(\"small\", {\n            children: \"H\\u1ED7 tr\\u1EE3 Markdown: **\\u0111\\u1EADm**, *nghi\\xEAng*, # ti\\xEAu \\u0111\\u1EC1, - danh s\\xE1ch, ```code```, > tr\\xEDch d\\u1EABn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_s(AnnotationEditor, \"k4Lx5qENam6VQ+raN6ZfW82npUQ=\");\n_c = AnnotationEditor;\nexport default AnnotationEditor;\nvar _c;\n$RefreshReg$(_c, \"AnnotationEditor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ReactMarkdown", "jsxDEV", "_jsxDEV", "AnnotationEditor", "annotation", "onSave", "onDelete", "onClose", "_s", "content", "<PERSON><PERSON><PERSON><PERSON>", "title", "setTitle", "isPreview", "setIsPreview", "handleSave", "handleKeyDown", "e", "ctrl<PERSON>ey", "key", "preventDefault", "className", "onClick", "children", "stopPropagation", "type", "placeholder", "value", "onChange", "target", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onKeyDown", "autoFocus", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/src/components/AnnotationEditor.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport ReactMarkdown from 'react-markdown';\nimport { Annotation } from '../types';\nimport './AnnotationEditor.css';\n\ninterface AnnotationEditorProps {\n  annotation: Annotation;\n  onSave: (content: string, title?: string) => void;\n  onDelete: () => void;\n  onClose: () => void;\n}\n\nconst AnnotationEditor: React.FC<AnnotationEditorProps> = ({ \n  annotation, \n  onSave, \n  onDelete, \n  onClose \n}) => {\n  const [content, setContent] = useState(annotation.content);\n  const [title, setTitle] = useState(annotation.title || '');\n  const [isPreview, setIsPreview] = useState(false);\n\n  useEffect(() => {\n    setContent(annotation.content);\n    setTitle(annotation.title || '');\n  }, [annotation]);\n\n  const handleSave = () => {\n    onSave(content, title);\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.ctrlKey && e.key === 's') {\n      e.preventDefault();\n      handleSave();\n    }\n    if (e.key === 'Escape') {\n      onClose();\n    }\n  };\n\n  return (\n    <div className=\"annotation-editor-overlay\" onClick={onClose}>\n      <div className=\"annotation-editor\" onClick={(e) => e.stopPropagation()}>\n        <div className=\"editor-header\">\n          <input\n            type=\"text\"\n            placeholder=\"Tiêu đề ghi chú\"\n            value={title}\n            onChange={(e) => setTitle(e.target.value)}\n            className=\"title-input\"\n          />\n          <div className=\"editor-controls\">\n            <button \n              className={`preview-btn ${isPreview ? 'active' : ''}`}\n              onClick={() => setIsPreview(!isPreview)}\n            >\n              {isPreview ? 'Chỉnh sửa' : 'Xem trước'}\n            </button>\n            <button className=\"save-btn\" onClick={handleSave}>\n              Lưu (Ctrl+S)\n            </button>\n            <button className=\"delete-btn\" onClick={onDelete}>\n              Xóa\n            </button>\n            <button className=\"close-btn\" onClick={onClose}>\n              ✕\n            </button>\n          </div>\n        </div>\n\n        <div className=\"editor-content\">\n          {isPreview ? (\n            <div className=\"preview-pane\">\n              <ReactMarkdown>{content}</ReactMarkdown>\n            </div>\n          ) : (\n            <textarea\n              value={content}\n              onChange={(e) => setContent(e.target.value)}\n              onKeyDown={handleKeyDown}\n              placeholder=\"Nhập nội dung markdown tại đây...\n\nVí dụ:\n# Tiêu đề\n## Tiêu đề phụ\n\n**Chữ đậm** và *chữ nghiêng*\n\n- Danh sách\n- Mục 2\n\n```code\nKhối code\n```\n\n> Trích dẫn\"\n              className=\"content-textarea\"\n              autoFocus\n            />\n          )}\n        </div>\n\n        <div className=\"editor-footer\">\n          <div className=\"markdown-help\">\n            <small>\n              Hỗ trợ Markdown: **đậm**, *nghiêng*, # tiêu đề, - danh sách, ```code```, &gt; trích dẫn\n            </small>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AnnotationEditor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,gBAAgB;AAE1C,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAShC,MAAMC,gBAAiD,GAAGA,CAAC;EACzDC,UAAU;EACVC,MAAM;EACNC,QAAQ;EACRC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAACM,UAAU,CAACK,OAAO,CAAC;EAC1D,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAACM,UAAU,CAACO,KAAK,IAAI,EAAE,CAAC;EAC1D,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACdW,UAAU,CAACN,UAAU,CAACK,OAAO,CAAC;IAC9BG,QAAQ,CAACR,UAAU,CAACO,KAAK,IAAI,EAAE,CAAC;EAClC,CAAC,EAAE,CAACP,UAAU,CAAC,CAAC;EAEhB,MAAMW,UAAU,GAAGA,CAAA,KAAM;IACvBV,MAAM,CAACI,OAAO,EAAEE,KAAK,CAAC;EACxB,CAAC;EAED,MAAMK,aAAa,GAAIC,CAAsB,IAAK;IAChD,IAAIA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;MAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBL,UAAU,CAAC,CAAC;IACd;IACA,IAAIE,CAAC,CAACE,GAAG,KAAK,QAAQ,EAAE;MACtBZ,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,oBACEL,OAAA;IAAKmB,SAAS,EAAC,2BAA2B;IAACC,OAAO,EAAEf,OAAQ;IAAAgB,QAAA,eAC1DrB,OAAA;MAAKmB,SAAS,EAAC,mBAAmB;MAACC,OAAO,EAAGL,CAAC,IAAKA,CAAC,CAACO,eAAe,CAAC,CAAE;MAAAD,QAAA,gBACrErB,OAAA;QAAKmB,SAAS,EAAC,eAAe;QAAAE,QAAA,gBAC5BrB,OAAA;UACEuB,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,iCAAiB;UAC7BC,KAAK,EAAEhB,KAAM;UACbiB,QAAQ,EAAGX,CAAC,IAAKL,QAAQ,CAACK,CAAC,CAACY,MAAM,CAACF,KAAK,CAAE;UAC1CN,SAAS,EAAC;QAAa;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACF/B,OAAA;UAAKmB,SAAS,EAAC,iBAAiB;UAAAE,QAAA,gBAC9BrB,OAAA;YACEmB,SAAS,EAAE,eAAeR,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;YACtDS,OAAO,EAAEA,CAAA,KAAMR,YAAY,CAAC,CAACD,SAAS,CAAE;YAAAU,QAAA,EAEvCV,SAAS,GAAG,WAAW,GAAG;UAAW;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACT/B,OAAA;YAAQmB,SAAS,EAAC,UAAU;YAACC,OAAO,EAAEP,UAAW;YAAAQ,QAAA,EAAC;UAElD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/B,OAAA;YAAQmB,SAAS,EAAC,YAAY;YAACC,OAAO,EAAEhB,QAAS;YAAAiB,QAAA,EAAC;UAElD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/B,OAAA;YAAQmB,SAAS,EAAC,WAAW;YAACC,OAAO,EAAEf,OAAQ;YAAAgB,QAAA,EAAC;UAEhD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/B,OAAA;QAAKmB,SAAS,EAAC,gBAAgB;QAAAE,QAAA,EAC5BV,SAAS,gBACRX,OAAA;UAAKmB,SAAS,EAAC,cAAc;UAAAE,QAAA,eAC3BrB,OAAA,CAACF,aAAa;YAAAuB,QAAA,EAAEd;UAAO;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,gBAEN/B,OAAA;UACEyB,KAAK,EAAElB,OAAQ;UACfmB,QAAQ,EAAGX,CAAC,IAAKP,UAAU,CAACO,CAAC,CAACY,MAAM,CAACF,KAAK,CAAE;UAC5CO,SAAS,EAAElB,aAAc;UACzBU,WAAW,EAAC,wQAed;UACEL,SAAS,EAAC,kBAAkB;UAC5Bc,SAAS;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/B,OAAA;QAAKmB,SAAS,EAAC,eAAe;QAAAE,QAAA,eAC5BrB,OAAA;UAAKmB,SAAS,EAAC,eAAe;UAAAE,QAAA,eAC5BrB,OAAA;YAAAqB,QAAA,EAAO;UAEP;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CArGIL,gBAAiD;AAAAiC,EAAA,GAAjDjC,gBAAiD;AAuGvD,eAAeA,gBAAgB;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}