{"ast": null, "code": "/**\n * Throw a given error.\n *\n * @param {Error|null|undefined} [error]\n *   Maybe error.\n * @returns {asserts error is null|undefined}\n */\nexport function bail(error) {\n  if (error) {\n    throw error;\n  }\n}", "map": {"version": 3, "names": ["bail", "error"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/bail/index.js"], "sourcesContent": ["/**\n * Throw a given error.\n *\n * @param {Error|null|undefined} [error]\n *   Maybe error.\n * @returns {asserts error is null|undefined}\n */\nexport function bail(error) {\n  if (error) {\n    throw error\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,IAAIA,CAACC,KAAK,EAAE;EAC1B,IAAIA,KAAK,EAAE;IACT,MAAMA,KAAK;EACb;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}