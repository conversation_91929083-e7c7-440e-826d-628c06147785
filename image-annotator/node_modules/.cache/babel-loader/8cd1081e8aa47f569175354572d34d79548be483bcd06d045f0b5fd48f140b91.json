{"ast": null, "code": "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { factoryDestination } from 'micromark-factory-destination';\nimport { factoryLabel } from 'micromark-factory-label';\nimport { factorySpace } from 'micromark-factory-space';\nimport { factoryTitle } from 'micromark-factory-title';\nimport { factoryWhitespace } from 'micromark-factory-whitespace';\nimport { markdownLineEndingOrSpace, markdownLineEnding, markdownSpace } from 'micromark-util-character';\nimport { normalizeIdentifier } from 'micromark-util-normalize-identifier';\nimport { codes, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const definition = {\n  name: 'definition',\n  tokenize: tokenizeDefinition\n};\n\n/** @type {Construct} */\nconst titleBefore = {\n  partial: true,\n  tokenize: tokenizeTitleBefore\n};\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeDefinition(effects, ok, nok) {\n  const self = this;\n  /** @type {string} */\n  let identifier;\n  return start;\n\n  /**\n   * At start of a definition.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // Do not interrupt paragraphs (but do follow definitions).\n    // To do: do `interrupt` the way `markdown-rs` does.\n    // To do: parse whitespace the way `markdown-rs` does.\n    effects.enter(types.definition);\n    return before(code);\n  }\n\n  /**\n   * After optional whitespace, at `[`.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    // To do: parse whitespace the way `markdown-rs` does.\n    assert(code === codes.leftSquareBracket, 'expected `[`');\n    return factoryLabel.call(self, effects, labelAfter,\n    // Note: we don’t need to reset the way `markdown-rs` does.\n    nok, types.definitionLabel, types.definitionLabelMarker, types.definitionLabelString)(code);\n  }\n\n  /**\n   * After label.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAfter(code) {\n    identifier = normalizeIdentifier(self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1));\n    if (code === codes.colon) {\n      effects.enter(types.definitionMarker);\n      effects.consume(code);\n      effects.exit(types.definitionMarker);\n      return markerAfter;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After marker.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function markerAfter(code) {\n    // Note: whitespace is optional.\n    return markdownLineEndingOrSpace(code) ? factoryWhitespace(effects, destinationBefore)(code) : destinationBefore(code);\n  }\n\n  /**\n   * Before destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationBefore(code) {\n    return factoryDestination(effects, destinationAfter,\n    // Note: we don’t need to reset the way `markdown-rs` does.\n    nok, types.definitionDestination, types.definitionDestinationLiteral, types.definitionDestinationLiteralMarker, types.definitionDestinationRaw, types.definitionDestinationString)(code);\n  }\n\n  /**\n   * After destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationAfter(code) {\n    return effects.attempt(titleBefore, after, after)(code);\n  }\n\n  /**\n   * After definition.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return markdownSpace(code) ? factorySpace(effects, afterWhitespace, types.whitespace)(code) : afterWhitespace(code);\n  }\n\n  /**\n   * After definition, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterWhitespace(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.definition);\n\n      // Note: we don’t care about uniqueness.\n      // It’s likely that that doesn’t happen very frequently.\n      // It is more likely that it wastes precious time.\n      self.parser.defined.push(identifier);\n\n      // To do: `markdown-rs` interrupt.\n      // // You’d be interrupting.\n      // tokenizer.interrupt = true\n      return ok(code);\n    }\n    return nok(code);\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeTitleBefore(effects, ok, nok) {\n  return titleBefore;\n\n  /**\n   * After destination, at whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleBefore(code) {\n    return markdownLineEndingOrSpace(code) ? factoryWhitespace(effects, beforeMarker)(code) : nok(code);\n  }\n\n  /**\n   * At title.\n   *\n   * ```markdown\n   *   | [a]: b\n   * > | \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeMarker(code) {\n    return factoryTitle(effects, titleAfter, nok, types.definitionTitle, types.definitionTitleMarker, types.definitionTitleString)(code);\n  }\n\n  /**\n   * After title.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfter(code) {\n    return markdownSpace(code) ? factorySpace(effects, titleAfterOptionalWhitespace, types.whitespace)(code) : titleAfterOptionalWhitespace(code);\n  }\n\n  /**\n   * After title, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfterOptionalWhitespace(code) {\n    return code === codes.eof || markdownLineEnding(code) ? ok(code) : nok(code);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "factoryDestination", "factoryLabel", "factorySpace", "factoryTitle", "factoryWhitespace", "markdownLineEndingOrSpace", "markdownLineEnding", "markdownSpace", "normalizeIdentifier", "codes", "types", "definition", "name", "tokenize", "tokenizeDefinition", "titleBefore", "partial", "tokenizeTitleBefore", "effects", "nok", "self", "identifier", "start", "code", "enter", "before", "leftSquareBracket", "call", "labelAfter", "definitionLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "definitionLabelString", "sliceSerialize", "events", "length", "slice", "colon", "definitionMarker", "consume", "exit", "markerAfter", "destinationBefore", "destinationAfter", "definitionDestination", "definitionDestinationLiteral", "definitionDestinationLiteralMarker", "definitionDestinationRaw", "definitionDestinationString", "attempt", "after", "afterWhitespace", "whitespace", "eof", "parser", "defined", "push", "<PERSON><PERSON><PERSON><PERSON>", "titleAfter", "definitionTitle", "definitionTitleMarker", "definitionTitleString", "titleAfterOptionalWhitespace"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark-core-commonmark/dev/lib/definition.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factoryDestination} from 'micromark-factory-destination'\nimport {factoryLabel} from 'micromark-factory-label'\nimport {factorySpace} from 'micromark-factory-space'\nimport {factoryTitle} from 'micromark-factory-title'\nimport {factoryWhitespace} from 'micromark-factory-whitespace'\nimport {\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const definition = {name: 'definition', tokenize: tokenizeDefinition}\n\n/** @type {Construct} */\nconst titleBefore = {partial: true, tokenize: tokenizeTitleBefore}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeDefinition(effects, ok, nok) {\n  const self = this\n  /** @type {string} */\n  let identifier\n\n  return start\n\n  /**\n   * At start of a definition.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // Do not interrupt paragraphs (but do follow definitions).\n    // To do: do `interrupt` the way `markdown-rs` does.\n    // To do: parse whitespace the way `markdown-rs` does.\n    effects.enter(types.definition)\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at `[`.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    // To do: parse whitespace the way `markdown-rs` does.\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    return factoryLabel.call(\n      self,\n      effects,\n      labelAfter,\n      // Note: we don’t need to reset the way `markdown-rs` does.\n      nok,\n      types.definitionLabel,\n      types.definitionLabelMarker,\n      types.definitionLabelString\n    )(code)\n  }\n\n  /**\n   * After label.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAfter(code) {\n    identifier = normalizeIdentifier(\n      self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n    )\n\n    if (code === codes.colon) {\n      effects.enter(types.definitionMarker)\n      effects.consume(code)\n      effects.exit(types.definitionMarker)\n      return markerAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After marker.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function markerAfter(code) {\n    // Note: whitespace is optional.\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, destinationBefore)(code)\n      : destinationBefore(code)\n  }\n\n  /**\n   * Before destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationBefore(code) {\n    return factoryDestination(\n      effects,\n      destinationAfter,\n      // Note: we don’t need to reset the way `markdown-rs` does.\n      nok,\n      types.definitionDestination,\n      types.definitionDestinationLiteral,\n      types.definitionDestinationLiteralMarker,\n      types.definitionDestinationRaw,\n      types.definitionDestinationString\n    )(code)\n  }\n\n  /**\n   * After destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationAfter(code) {\n    return effects.attempt(titleBefore, after, after)(code)\n  }\n\n  /**\n   * After definition.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return markdownSpace(code)\n      ? factorySpace(effects, afterWhitespace, types.whitespace)(code)\n      : afterWhitespace(code)\n  }\n\n  /**\n   * After definition, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterWhitespace(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.definition)\n\n      // Note: we don’t care about uniqueness.\n      // It’s likely that that doesn’t happen very frequently.\n      // It is more likely that it wastes precious time.\n      self.parser.defined.push(identifier)\n\n      // To do: `markdown-rs` interrupt.\n      // // You’d be interrupting.\n      // tokenizer.interrupt = true\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeTitleBefore(effects, ok, nok) {\n  return titleBefore\n\n  /**\n   * After destination, at whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleBefore(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, beforeMarker)(code)\n      : nok(code)\n  }\n\n  /**\n   * At title.\n   *\n   * ```markdown\n   *   | [a]: b\n   * > | \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeMarker(code) {\n    return factoryTitle(\n      effects,\n      titleAfter,\n      nok,\n      types.definitionTitle,\n      types.definitionTitleMarker,\n      types.definitionTitleString\n    )(code)\n  }\n\n  /**\n   * After title.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfter(code) {\n    return markdownSpace(code)\n      ? factorySpace(\n          effects,\n          titleAfterOptionalWhitespace,\n          types.whitespace\n        )(code)\n      : titleAfterOptionalWhitespace(code)\n  }\n\n  /**\n   * After title, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfterOptionalWhitespace(code) {\n    return code === codes.eof || markdownLineEnding(code) ? ok(code) : nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,kBAAkB,QAAO,+BAA+B;AAChE,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,iBAAiB,QAAO,8BAA8B;AAC9D,SACEC,yBAAyB,EACzBC,kBAAkB,EAClBC,aAAa,QACR,0BAA0B;AACjC,SAAQC,mBAAmB,QAAO,qCAAqC;AACvE,SAAQC,KAAK,EAAEC,KAAK,QAAO,uBAAuB;;AAElD;AACA,OAAO,MAAMC,UAAU,GAAG;EAACC,IAAI,EAAE,YAAY;EAAEC,QAAQ,EAAEC;AAAkB,CAAC;;AAE5E;AACA,MAAMC,WAAW,GAAG;EAACC,OAAO,EAAE,IAAI;EAAEH,QAAQ,EAAEI;AAAmB,CAAC;;AAElE;AACA;AACA;AACA;AACA;AACA,SAASH,kBAAkBA,CAACI,OAAO,EAAEpB,EAAE,EAAEqB,GAAG,EAAE;EAC5C,MAAMC,IAAI,GAAG,IAAI;EACjB;EACA,IAAIC,UAAU;EAEd,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB;IACA;IACA;IACAL,OAAO,CAACM,KAAK,CAACd,KAAK,CAACC,UAAU,CAAC;IAC/B,OAAOc,MAAM,CAACF,IAAI,CAAC;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,MAAMA,CAACF,IAAI,EAAE;IACpB;IACAxB,MAAM,CAACwB,IAAI,KAAKd,KAAK,CAACiB,iBAAiB,EAAE,cAAc,CAAC;IACxD,OAAOzB,YAAY,CAAC0B,IAAI,CACtBP,IAAI,EACJF,OAAO,EACPU,UAAU;IACV;IACAT,GAAG,EACHT,KAAK,CAACmB,eAAe,EACrBnB,KAAK,CAACoB,qBAAqB,EAC3BpB,KAAK,CAACqB,qBACR,CAAC,CAACR,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASK,UAAUA,CAACL,IAAI,EAAE;IACxBF,UAAU,GAAGb,mBAAmB,CAC9BY,IAAI,CAACY,cAAc,CAACZ,IAAI,CAACa,MAAM,CAACb,IAAI,CAACa,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACzE,CAAC;IAED,IAAIZ,IAAI,KAAKd,KAAK,CAAC2B,KAAK,EAAE;MACxBlB,OAAO,CAACM,KAAK,CAACd,KAAK,CAAC2B,gBAAgB,CAAC;MACrCnB,OAAO,CAACoB,OAAO,CAACf,IAAI,CAAC;MACrBL,OAAO,CAACqB,IAAI,CAAC7B,KAAK,CAAC2B,gBAAgB,CAAC;MACpC,OAAOG,WAAW;IACpB;IAEA,OAAOrB,GAAG,CAACI,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiB,WAAWA,CAACjB,IAAI,EAAE;IACzB;IACA,OAAOlB,yBAAyB,CAACkB,IAAI,CAAC,GAClCnB,iBAAiB,CAACc,OAAO,EAAEuB,iBAAiB,CAAC,CAAClB,IAAI,CAAC,GACnDkB,iBAAiB,CAAClB,IAAI,CAAC;EAC7B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASkB,iBAAiBA,CAAClB,IAAI,EAAE;IAC/B,OAAOvB,kBAAkB,CACvBkB,OAAO,EACPwB,gBAAgB;IAChB;IACAvB,GAAG,EACHT,KAAK,CAACiC,qBAAqB,EAC3BjC,KAAK,CAACkC,4BAA4B,EAClClC,KAAK,CAACmC,kCAAkC,EACxCnC,KAAK,CAACoC,wBAAwB,EAC9BpC,KAAK,CAACqC,2BACR,CAAC,CAACxB,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASmB,gBAAgBA,CAACnB,IAAI,EAAE;IAC9B,OAAOL,OAAO,CAAC8B,OAAO,CAACjC,WAAW,EAAEkC,KAAK,EAAEA,KAAK,CAAC,CAAC1B,IAAI,CAAC;EACzD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS0B,KAAKA,CAAC1B,IAAI,EAAE;IACnB,OAAOhB,aAAa,CAACgB,IAAI,CAAC,GACtBrB,YAAY,CAACgB,OAAO,EAAEgC,eAAe,EAAExC,KAAK,CAACyC,UAAU,CAAC,CAAC5B,IAAI,CAAC,GAC9D2B,eAAe,CAAC3B,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS2B,eAAeA,CAAC3B,IAAI,EAAE;IAC7B,IAAIA,IAAI,KAAKd,KAAK,CAAC2C,GAAG,IAAI9C,kBAAkB,CAACiB,IAAI,CAAC,EAAE;MAClDL,OAAO,CAACqB,IAAI,CAAC7B,KAAK,CAACC,UAAU,CAAC;;MAE9B;MACA;MACA;MACAS,IAAI,CAACiC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAClC,UAAU,CAAC;;MAEpC;MACA;MACA;MACA,OAAOvB,EAAE,CAACyB,IAAI,CAAC;IACjB;IAEA,OAAOJ,GAAG,CAACI,IAAI,CAAC;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASN,mBAAmBA,CAACC,OAAO,EAAEpB,EAAE,EAAEqB,GAAG,EAAE;EAC7C,OAAOJ,WAAW;;EAElB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,WAAWA,CAACQ,IAAI,EAAE;IACzB,OAAOlB,yBAAyB,CAACkB,IAAI,CAAC,GAClCnB,iBAAiB,CAACc,OAAO,EAAEsC,YAAY,CAAC,CAACjC,IAAI,CAAC,GAC9CJ,GAAG,CAACI,IAAI,CAAC;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiC,YAAYA,CAACjC,IAAI,EAAE;IAC1B,OAAOpB,YAAY,CACjBe,OAAO,EACPuC,UAAU,EACVtC,GAAG,EACHT,KAAK,CAACgD,eAAe,EACrBhD,KAAK,CAACiD,qBAAqB,EAC3BjD,KAAK,CAACkD,qBACR,CAAC,CAACrC,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASkC,UAAUA,CAAClC,IAAI,EAAE;IACxB,OAAOhB,aAAa,CAACgB,IAAI,CAAC,GACtBrB,YAAY,CACVgB,OAAO,EACP2C,4BAA4B,EAC5BnD,KAAK,CAACyC,UACR,CAAC,CAAC5B,IAAI,CAAC,GACPsC,4BAA4B,CAACtC,IAAI,CAAC;EACxC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsC,4BAA4BA,CAACtC,IAAI,EAAE;IAC1C,OAAOA,IAAI,KAAKd,KAAK,CAAC2C,GAAG,IAAI9C,kBAAkB,CAACiB,IAAI,CAAC,GAAGzB,EAAE,CAACyB,IAAI,CAAC,GAAGJ,GAAG,CAACI,IAAI,CAAC;EAC9E;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}