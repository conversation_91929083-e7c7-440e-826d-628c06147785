{"ast": null, "code": "/**\n * @import {Chunk, Code, Encoding, Value} from 'micromark-util-types'\n */\n\n/**\n * @callback Preprocessor\n *   Preprocess a value.\n * @param {Value} value\n *   Value.\n * @param {Encoding | null | undefined} [encoding]\n *   Encoding when `value` is a typed array (optional).\n * @param {boolean | null | undefined} [end=false]\n *   Whether this is the last chunk (default: `false`).\n * @returns {Array<Chunk>}\n *   Chunks.\n */\n\nimport { codes, constants } from 'micromark-util-symbol';\nconst search = /[\\0\\t\\n\\r]/g;\n\n/**\n * @returns {Preprocessor}\n *   Preprocess a value.\n */\nexport function preprocess() {\n  let column = 1;\n  let buffer = '';\n  /** @type {boolean | undefined} */\n  let start = true;\n  /** @type {boolean | undefined} */\n  let atCarriageReturn;\n  return preprocessor;\n\n  /** @type {Preprocessor} */\n  // eslint-disable-next-line complexity\n  function preprocessor(value, encoding, end) {\n    /** @type {Array<Chunk>} */\n    const chunks = [];\n    /** @type {RegExpMatchArray | null} */\n    let match;\n    /** @type {number} */\n    let next;\n    /** @type {number} */\n    let startPosition;\n    /** @type {number} */\n    let endPosition;\n    /** @type {Code} */\n    let code;\n    value = buffer + (typeof value === 'string' ? value.toString() : new TextDecoder(encoding || undefined).decode(value));\n    startPosition = 0;\n    buffer = '';\n    if (start) {\n      // To do: `markdown-rs` actually parses BOMs (byte order mark).\n      if (value.charCodeAt(0) === codes.byteOrderMarker) {\n        startPosition++;\n      }\n      start = undefined;\n    }\n    while (startPosition < value.length) {\n      search.lastIndex = startPosition;\n      match = search.exec(value);\n      endPosition = match && match.index !== undefined ? match.index : value.length;\n      code = value.charCodeAt(endPosition);\n      if (!match) {\n        buffer = value.slice(startPosition);\n        break;\n      }\n      if (code === codes.lf && startPosition === endPosition && atCarriageReturn) {\n        chunks.push(codes.carriageReturnLineFeed);\n        atCarriageReturn = undefined;\n      } else {\n        if (atCarriageReturn) {\n          chunks.push(codes.carriageReturn);\n          atCarriageReturn = undefined;\n        }\n        if (startPosition < endPosition) {\n          chunks.push(value.slice(startPosition, endPosition));\n          column += endPosition - startPosition;\n        }\n        switch (code) {\n          case codes.nul:\n            {\n              chunks.push(codes.replacementCharacter);\n              column++;\n              break;\n            }\n          case codes.ht:\n            {\n              next = Math.ceil(column / constants.tabSize) * constants.tabSize;\n              chunks.push(codes.horizontalTab);\n              while (column++ < next) chunks.push(codes.virtualSpace);\n              break;\n            }\n          case codes.lf:\n            {\n              chunks.push(codes.lineFeed);\n              column = 1;\n              break;\n            }\n          default:\n            {\n              atCarriageReturn = true;\n              column = 1;\n            }\n        }\n      }\n      startPosition = endPosition + 1;\n    }\n    if (end) {\n      if (atCarriageReturn) chunks.push(codes.carriageReturn);\n      if (buffer) chunks.push(buffer);\n      chunks.push(codes.eof);\n    }\n    return chunks;\n  }\n}", "map": {"version": 3, "names": ["codes", "constants", "search", "preprocess", "column", "buffer", "start", "atCarriageReturn", "preprocessor", "value", "encoding", "end", "chunks", "match", "next", "startPosition", "endPosition", "code", "toString", "TextDecoder", "undefined", "decode", "charCodeAt", "byteOrderMarker", "length", "lastIndex", "exec", "index", "slice", "lf", "push", "carriageReturnLineFeed", "carriageReturn", "nul", "replacementCharacter", "ht", "Math", "ceil", "tabSize", "horizontalTab", "virtualSpace", "lineFeed", "eof"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/micromark/dev/lib/preprocess.js"], "sourcesContent": ["/**\n * @import {Chunk, Code, Encoding, Value} from 'micromark-util-types'\n */\n\n/**\n * @callback Preprocessor\n *   Preprocess a value.\n * @param {Value} value\n *   Value.\n * @param {Encoding | null | undefined} [encoding]\n *   Encoding when `value` is a typed array (optional).\n * @param {boolean | null | undefined} [end=false]\n *   Whether this is the last chunk (default: `false`).\n * @returns {Array<Chunk>}\n *   Chunks.\n */\n\nimport {codes, constants} from 'micromark-util-symbol'\n\nconst search = /[\\0\\t\\n\\r]/g\n\n/**\n * @returns {Preprocessor}\n *   Preprocess a value.\n */\nexport function preprocess() {\n  let column = 1\n  let buffer = ''\n  /** @type {boolean | undefined} */\n  let start = true\n  /** @type {boolean | undefined} */\n  let atCarriageReturn\n\n  return preprocessor\n\n  /** @type {Preprocessor} */\n  // eslint-disable-next-line complexity\n  function preprocessor(value, encoding, end) {\n    /** @type {Array<Chunk>} */\n    const chunks = []\n    /** @type {RegExpMatchArray | null} */\n    let match\n    /** @type {number} */\n    let next\n    /** @type {number} */\n    let startPosition\n    /** @type {number} */\n    let endPosition\n    /** @type {Code} */\n    let code\n\n    value =\n      buffer +\n      (typeof value === 'string'\n        ? value.toString()\n        : new TextDecoder(encoding || undefined).decode(value))\n\n    startPosition = 0\n    buffer = ''\n\n    if (start) {\n      // To do: `markdown-rs` actually parses BOMs (byte order mark).\n      if (value.charCodeAt(0) === codes.byteOrderMarker) {\n        startPosition++\n      }\n\n      start = undefined\n    }\n\n    while (startPosition < value.length) {\n      search.lastIndex = startPosition\n      match = search.exec(value)\n      endPosition =\n        match && match.index !== undefined ? match.index : value.length\n      code = value.charCodeAt(endPosition)\n\n      if (!match) {\n        buffer = value.slice(startPosition)\n        break\n      }\n\n      if (\n        code === codes.lf &&\n        startPosition === endPosition &&\n        atCarriageReturn\n      ) {\n        chunks.push(codes.carriageReturnLineFeed)\n        atCarriageReturn = undefined\n      } else {\n        if (atCarriageReturn) {\n          chunks.push(codes.carriageReturn)\n          atCarriageReturn = undefined\n        }\n\n        if (startPosition < endPosition) {\n          chunks.push(value.slice(startPosition, endPosition))\n          column += endPosition - startPosition\n        }\n\n        switch (code) {\n          case codes.nul: {\n            chunks.push(codes.replacementCharacter)\n            column++\n\n            break\n          }\n\n          case codes.ht: {\n            next = Math.ceil(column / constants.tabSize) * constants.tabSize\n            chunks.push(codes.horizontalTab)\n            while (column++ < next) chunks.push(codes.virtualSpace)\n\n            break\n          }\n\n          case codes.lf: {\n            chunks.push(codes.lineFeed)\n            column = 1\n\n            break\n          }\n\n          default: {\n            atCarriageReturn = true\n            column = 1\n          }\n        }\n      }\n\n      startPosition = endPosition + 1\n    }\n\n    if (end) {\n      if (atCarriageReturn) chunks.push(codes.carriageReturn)\n      if (buffer) chunks.push(buffer)\n      chunks.push(codes.eof)\n    }\n\n    return chunks\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,KAAK,EAAEC,SAAS,QAAO,uBAAuB;AAEtD,MAAMC,MAAM,GAAG,aAAa;;AAE5B;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAAA,EAAG;EAC3B,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,EAAE;EACf;EACA,IAAIC,KAAK,GAAG,IAAI;EAChB;EACA,IAAIC,gBAAgB;EAEpB,OAAOC,YAAY;;EAEnB;EACA;EACA,SAASA,YAAYA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,GAAG,EAAE;IAC1C;IACA,MAAMC,MAAM,GAAG,EAAE;IACjB;IACA,IAAIC,KAAK;IACT;IACA,IAAIC,IAAI;IACR;IACA,IAAIC,aAAa;IACjB;IACA,IAAIC,WAAW;IACf;IACA,IAAIC,IAAI;IAERR,KAAK,GACHJ,MAAM,IACL,OAAOI,KAAK,KAAK,QAAQ,GACtBA,KAAK,CAACS,QAAQ,CAAC,CAAC,GAChB,IAAIC,WAAW,CAACT,QAAQ,IAAIU,SAAS,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAC,CAAC;IAE3DM,aAAa,GAAG,CAAC;IACjBV,MAAM,GAAG,EAAE;IAEX,IAAIC,KAAK,EAAE;MACT;MACA,IAAIG,KAAK,CAACa,UAAU,CAAC,CAAC,CAAC,KAAKtB,KAAK,CAACuB,eAAe,EAAE;QACjDR,aAAa,EAAE;MACjB;MAEAT,KAAK,GAAGc,SAAS;IACnB;IAEA,OAAOL,aAAa,GAAGN,KAAK,CAACe,MAAM,EAAE;MACnCtB,MAAM,CAACuB,SAAS,GAAGV,aAAa;MAChCF,KAAK,GAAGX,MAAM,CAACwB,IAAI,CAACjB,KAAK,CAAC;MAC1BO,WAAW,GACTH,KAAK,IAAIA,KAAK,CAACc,KAAK,KAAKP,SAAS,GAAGP,KAAK,CAACc,KAAK,GAAGlB,KAAK,CAACe,MAAM;MACjEP,IAAI,GAAGR,KAAK,CAACa,UAAU,CAACN,WAAW,CAAC;MAEpC,IAAI,CAACH,KAAK,EAAE;QACVR,MAAM,GAAGI,KAAK,CAACmB,KAAK,CAACb,aAAa,CAAC;QACnC;MACF;MAEA,IACEE,IAAI,KAAKjB,KAAK,CAAC6B,EAAE,IACjBd,aAAa,KAAKC,WAAW,IAC7BT,gBAAgB,EAChB;QACAK,MAAM,CAACkB,IAAI,CAAC9B,KAAK,CAAC+B,sBAAsB,CAAC;QACzCxB,gBAAgB,GAAGa,SAAS;MAC9B,CAAC,MAAM;QACL,IAAIb,gBAAgB,EAAE;UACpBK,MAAM,CAACkB,IAAI,CAAC9B,KAAK,CAACgC,cAAc,CAAC;UACjCzB,gBAAgB,GAAGa,SAAS;QAC9B;QAEA,IAAIL,aAAa,GAAGC,WAAW,EAAE;UAC/BJ,MAAM,CAACkB,IAAI,CAACrB,KAAK,CAACmB,KAAK,CAACb,aAAa,EAAEC,WAAW,CAAC,CAAC;UACpDZ,MAAM,IAAIY,WAAW,GAAGD,aAAa;QACvC;QAEA,QAAQE,IAAI;UACV,KAAKjB,KAAK,CAACiC,GAAG;YAAE;cACdrB,MAAM,CAACkB,IAAI,CAAC9B,KAAK,CAACkC,oBAAoB,CAAC;cACvC9B,MAAM,EAAE;cAER;YACF;UAEA,KAAKJ,KAAK,CAACmC,EAAE;YAAE;cACbrB,IAAI,GAAGsB,IAAI,CAACC,IAAI,CAACjC,MAAM,GAAGH,SAAS,CAACqC,OAAO,CAAC,GAAGrC,SAAS,CAACqC,OAAO;cAChE1B,MAAM,CAACkB,IAAI,CAAC9B,KAAK,CAACuC,aAAa,CAAC;cAChC,OAAOnC,MAAM,EAAE,GAAGU,IAAI,EAAEF,MAAM,CAACkB,IAAI,CAAC9B,KAAK,CAACwC,YAAY,CAAC;cAEvD;YACF;UAEA,KAAKxC,KAAK,CAAC6B,EAAE;YAAE;cACbjB,MAAM,CAACkB,IAAI,CAAC9B,KAAK,CAACyC,QAAQ,CAAC;cAC3BrC,MAAM,GAAG,CAAC;cAEV;YACF;UAEA;YAAS;cACPG,gBAAgB,GAAG,IAAI;cACvBH,MAAM,GAAG,CAAC;YACZ;QACF;MACF;MAEAW,aAAa,GAAGC,WAAW,GAAG,CAAC;IACjC;IAEA,IAAIL,GAAG,EAAE;MACP,IAAIJ,gBAAgB,EAAEK,MAAM,CAACkB,IAAI,CAAC9B,KAAK,CAACgC,cAAc,CAAC;MACvD,IAAI3B,MAAM,EAAEO,MAAM,CAACkB,IAAI,CAACzB,MAAM,CAAC;MAC/BO,MAAM,CAACkB,IAAI,CAAC9B,KAAK,CAAC0C,GAAG,CAAC;IACxB;IAEA,OAAO9B,MAAM;EACf;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}