{"ast": null, "code": "// Note: types exported from `index.d.ts`\nexport { CONTINUE, EXIT, SKIP, visitParents } from './lib/index.js';", "map": {"version": 3, "names": ["CONTINUE", "EXIT", "SKIP", "visitParents"], "sources": ["/home/<USER>/Desktop/project_local/like_rplace/image-annotator/node_modules/unist-util-visit-parents/index.js"], "sourcesContent": ["// Note: types exported from `index.d.ts`\nexport {CONTINUE, EXIT, SKIP, visitParents} from './lib/index.js'\n"], "mappings": "AAAA;AACA,SAAQA,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,YAAY,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}