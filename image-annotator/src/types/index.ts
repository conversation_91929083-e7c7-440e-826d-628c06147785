export interface Annotation {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  content: string; // Markdown content
  title?: string;
}

export interface ImageData {
  id: string;
  name: string;
  url: string;
  annotations: Annotation[];
  collectionId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Collection {
  id: string;
  name: string;
  parentId?: string;
  children: Collection[];
  images: ImageData[];
  createdAt: Date;
}

export interface Point {
  x: number;
  y: number;
}

export interface Rectangle {
  x: number;
  y: number;
  width: number;
  height: number;
}
