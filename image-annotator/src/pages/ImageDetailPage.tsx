import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useImageContext } from '../contexts/ImageContext';
import AnnotationViewer from '../components/AnnotationViewer';
import AnnotationEditor from '../components/AnnotationEditor';
import { Annotation, Point } from '../types';
import './ImageDetailPage.css';

const ImageDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getImageById, addAnnotation, updateAnnotation, deleteAnnotation, deleteImage } = useImageContext();
  const [isCreatingAnnotation, setIsCreatingAnnotation] = useState(false);
  const [selectedAnnotation, setSelectedAnnotation] = useState<Annotation | null>(null);
  const [hoveredAnnotation, setHoveredAnnotation] = useState<Annotation | null>(null);
  const [startPoint, setStartPoint] = useState<Point | null>(null);
  const [currentRect, setCurrentRect] = useState<{ x: number; y: number; width: number; height: number } | null>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const image = id ? getImageById(id) : undefined;

  useEffect(() => {
    if (!image) {
      navigate('/');
    }
  }, [image, navigate]);

  if (!image) {
    return <div>Không tìm thấy ảnh</div>;
  }

  const getImageCoordinates = (clientX: number, clientY: number) => {
    if (!imageRef.current || !containerRef.current) return null;

    const imageRect = imageRef.current.getBoundingClientRect();
    const containerRect = containerRef.current.getBoundingClientRect();

    // Calculate coordinates relative to the image element
    const x = clientX - imageRect.left;
    const y = clientY - imageRect.top;

    // Convert to relative coordinates (0-1) based on actual image dimensions
    const relativeX = x / imageRect.width;
    const relativeY = y / imageRect.height;

    // Ensure coordinates are within bounds
    const clampedX = Math.max(0, Math.min(1, relativeX));
    const clampedY = Math.max(0, Math.min(1, relativeY));

    return { x: clampedX, y: clampedY };
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!isCreatingAnnotation) return;
    
    const coords = getImageCoordinates(e.clientX, e.clientY);
    if (coords) {
      setStartPoint(coords);
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isCreatingAnnotation || !startPoint) return;
    
    const coords = getImageCoordinates(e.clientX, e.clientY);
    if (coords) {
      const width = Math.abs(coords.x - startPoint.x);
      const height = Math.abs(coords.y - startPoint.y);
      const x = Math.min(startPoint.x, coords.x);
      const y = Math.min(startPoint.y, coords.y);
      
      setCurrentRect({ x, y, width, height });
    }
  };

  const handleMouseUp = (e: React.MouseEvent) => {
    if (!isCreatingAnnotation || !startPoint || !currentRect) return;
    
    // Only create annotation if rectangle is large enough
    if (currentRect.width > 0.01 && currentRect.height > 0.01) {
      const newAnnotation = {
        x: currentRect.x,
        y: currentRect.y,
        width: currentRect.width,
        height: currentRect.height,
        content: '# Ghi chú mới\n\nNhập nội dung markdown tại đây...',
        title: 'Ghi chú mới'
      };
      
      addAnnotation(image.id, newAnnotation);
    }
    
    setIsCreatingAnnotation(false);
    setStartPoint(null);
    setCurrentRect(null);
  };

  const handleAnnotationClick = (annotation: Annotation) => {
    setSelectedAnnotation(annotation);
  };

  const handleAnnotationSave = (content: string, title?: string) => {
    if (selectedAnnotation) {
      updateAnnotation(image.id, selectedAnnotation.id, { content, title });
      setSelectedAnnotation(null);
    }
  };

  const handleAnnotationDelete = () => {
    if (selectedAnnotation) {
      deleteAnnotation(image.id, selectedAnnotation.id);
      setSelectedAnnotation(null);
    }
  };

  const handleImageDelete = () => {
    if (window.confirm('Bạn có chắc chắn muốn xóa ảnh này? Tất cả ghi chú sẽ bị mất.')) {
      deleteImage(image.id);
      navigate('/');
    }
  };

  return (
    <div className="image-detail-page">
      <header className="detail-header">
        <button onClick={() => navigate('/')} className="back-button">
          ← Quay lại
        </button>
        <h1>{image.name}</h1>
        <div className="toolbar">
          <button
            className={`create-annotation-btn ${isCreatingAnnotation ? 'active' : ''}`}
            onClick={() => setIsCreatingAnnotation(!isCreatingAnnotation)}
          >
            {isCreatingAnnotation ? 'Hủy tạo vùng' : 'Tạo vùng ghi chú'}
          </button>
          <button
            className="delete-image-btn"
            onClick={handleImageDelete}
          >
            Xóa ảnh
          </button>
        </div>
      </header>

      <div className="detail-content">
        <div className="image-container" ref={containerRef}>
          <div className="image-wrapper">
            <img
              ref={imageRef}
              src={image.url}
              alt={image.name}
              className={`main-image ${isCreatingAnnotation ? 'creating' : ''}`}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              draggable={false}
            />

            {/* Render existing annotations */}
            {image.annotations.map(annotation => (
              <div
                key={annotation.id}
                className="annotation-overlay"
                style={{
                  left: `${annotation.x * 100}%`,
                  top: `${annotation.y * 100}%`,
                  width: `${annotation.width * 100}%`,
                  height: `${annotation.height * 100}%`,
                }}
                onClick={() => handleAnnotationClick(annotation)}
                onMouseEnter={() => setHoveredAnnotation(annotation)}
                onMouseLeave={() => setHoveredAnnotation(null)}
              />
            ))}

            {/* Render current drawing rectangle */}
            {currentRect && (
              <div
                className="annotation-overlay creating"
                style={{
                  left: `${currentRect.x * 100}%`,
                  top: `${currentRect.y * 100}%`,
                  width: `${currentRect.width * 100}%`,
                  height: `${currentRect.height * 100}%`,
                }}
              />
            )}
          </div>
        </div>

        {/* Annotation viewer for hover */}
        {hoveredAnnotation && !selectedAnnotation && (
          <AnnotationViewer 
            annotation={hoveredAnnotation}
            position="left"
          />
        )}

        {/* Annotation editor */}
        {selectedAnnotation && (
          <AnnotationEditor
            annotation={selectedAnnotation}
            onSave={handleAnnotationSave}
            onDelete={handleAnnotationDelete}
            onClose={() => setSelectedAnnotation(null)}
          />
        )}
      </div>
    </div>
  );
};

export default ImageDetailPage;
