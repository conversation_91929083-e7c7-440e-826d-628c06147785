import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useImageContext } from '../contexts/ImageContext';
import CollectionTree from '../components/CollectionTree';
import ImageUpload from '../components/ImageUpload';
import './HomePage.css';

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const { collections, images, deleteImage } = useImageContext();
  const [selectedCollectionId, setSelectedCollectionId] = useState<string>('root');

  const handleImageClick = (imageId: string) => {
    navigate(`/image/${imageId}`);
  };

  const handleDeleteImage = (e: React.MouseEvent, imageId: string) => {
    e.stopPropagation(); // Prevent navigation when clicking delete
    if (window.confirm('Bạn có chắc chắn muốn xóa ảnh này?')) {
      deleteImage(imageId);
    }
  };

  const getImagesInCollection = (collectionId: string) => {
    return images.filter(img => img.collectionId === collectionId);
  };

  return (
    <div className="home-page">
      <header className="home-header">
        <h1>Image Annotator</h1>
        <p>Tạo ghi chú chi tiết cho từng vùng nhỏ trên ảnh</p>
      </header>

      <div className="home-content">
        <div className="sidebar">
          <div className="collection-section">
            <h3>Bộ sưu tập</h3>
            <CollectionTree 
              collections={collections}
              selectedId={selectedCollectionId}
              onSelect={setSelectedCollectionId}
            />
          </div>
          
          <div className="upload-section">
            <h3>Tải ảnh lên</h3>
            <ImageUpload collectionId={selectedCollectionId} />
          </div>
        </div>

        <div className="main-content">
          <div className="images-grid">
            <h3>Ảnh trong bộ sưu tập</h3>
            <div className="image-grid">
              {getImagesInCollection(selectedCollectionId).map(image => (
                <div
                  key={image.id}
                  className="image-card"
                  onClick={() => handleImageClick(image.id)}
                >
                  <button
                    className="delete-image-btn"
                    onClick={(e) => handleDeleteImage(e, image.id)}
                    title="Xóa ảnh"
                  >
                    ✕
                  </button>
                  <img src={image.url} alt={image.name} />
                  <div className="image-info">
                    <h4>{image.name}</h4>
                    <p>{image.annotations.length} ghi chú</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
