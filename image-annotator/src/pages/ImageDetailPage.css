.image-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.detail-header {
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.back-button:hover {
  background: #5a6fd8;
}

.detail-header h1 {
  flex: 1;
  margin: 0;
  color: #333;
}

.toolbar {
  display: flex;
  gap: 1rem;
}

.create-annotation-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.create-annotation-btn:hover {
  background: #218838;
}

.create-annotation-btn.active {
  background: #dc3545;
}

.create-annotation-btn.active:hover {
  background: #c82333;
}

.delete-image-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.delete-image-btn:hover {
  background: #c82333;
}

.detail-content {
  display: flex;
  height: calc(100vh - 80px);
  position: relative;
}

.image-container {
  flex: 1;
  position: relative;
  overflow: auto;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-wrapper {
  position: relative;
  display: inline-block;
}

.main-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  position: relative;
  z-index: 1;
}

.main-image.creating {
  cursor: crosshair;
}

.annotation-overlay {
  position: absolute;
  border: 2px solid #667eea;
  background: rgba(102, 126, 234, 0.2);
  cursor: pointer;
  z-index: 2;
  transition: all 0.2s;
}

.annotation-overlay:hover {
  border-color: #5a6fd8;
  background: rgba(102, 126, 234, 0.3);
  box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

.annotation-overlay.creating {
  border-color: #28a745;
  background: rgba(40, 167, 69, 0.2);
  cursor: crosshair;
}

/* Responsive design */
@media (max-width: 768px) {
  .detail-header {
    padding: 1rem;
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .detail-header h1 {
    text-align: center;
    font-size: 1.5rem;
  }
  
  .toolbar {
    justify-content: center;
  }
  
  .detail-content {
    flex-direction: column;
    height: auto;
  }
}
