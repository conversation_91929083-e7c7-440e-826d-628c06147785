import React, { createContext, useContext, useState, useEffect } from 'react';
import { ImageData, Collection, Annotation } from '../types';
import { v4 as uuidv4 } from 'uuid';

interface ImageContextType {
  collections: Collection[];
  images: ImageData[];
  addCollection: (name: string, parentId?: string) => void;
  addImage: (file: File, collectionId: string) => Promise<void>;
  addAnnotation: (imageId: string, annotation: Omit<Annotation, 'id'>) => void;
  updateAnnotation: (imageId: string, annotationId: string, updates: Partial<Annotation>) => void;
  deleteAnnotation: (imageId: string, annotationId: string) => void;
  getImageById: (id: string) => ImageData | undefined;
  getCollectionById: (id: string) => Collection | undefined;
}

const ImageContext = createContext<ImageContextType | undefined>(undefined);

export const useImageContext = () => {
  const context = useContext(ImageContext);
  if (!context) {
    throw new Error('useImageContext must be used within an ImageProvider');
  }
  return context;
};

export const ImageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [collections, setCollections] = useState<Collection[]>([]);
  const [images, setImages] = useState<ImageData[]>([]);

  // Load data from localStorage on mount
  useEffect(() => {
    const savedCollections = localStorage.getItem('image-annotator-collections');
    const savedImages = localStorage.getItem('image-annotator-images');
    
    if (savedCollections) {
      setCollections(JSON.parse(savedCollections));
    } else {
      // Create default root collection
      const rootCollection: Collection = {
        id: 'root',
        name: 'Root',
        children: [],
        images: [],
        createdAt: new Date(),
      };
      setCollections([rootCollection]);
    }
    
    if (savedImages) {
      setImages(JSON.parse(savedImages));
    }
  }, []);

  // Save to localStorage whenever data changes
  useEffect(() => {
    localStorage.setItem('image-annotator-collections', JSON.stringify(collections));
  }, [collections]);

  useEffect(() => {
    localStorage.setItem('image-annotator-images', JSON.stringify(images));
  }, [images]);

  const addCollection = (name: string, parentId?: string) => {
    const newCollection: Collection = {
      id: uuidv4(),
      name,
      parentId,
      children: [],
      images: [],
      createdAt: new Date(),
    };

    setCollections(prev => {
      if (parentId) {
        return updateCollectionInTree(prev, parentId, (collection) => ({
          ...collection,
          children: [...collection.children, newCollection]
        }));
      } else {
        return [...prev, newCollection];
      }
    });
  };

  const addImage = async (file: File, collectionId: string) => {
    return new Promise<void>((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const newImage: ImageData = {
          id: uuidv4(),
          name: file.name,
          url: e.target?.result as string,
          annotations: [],
          collectionId,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        setImages(prev => [...prev, newImage]);
        resolve();
      };
      reader.readAsDataURL(file);
    });
  };

  const addAnnotation = (imageId: string, annotation: Omit<Annotation, 'id'>) => {
    const newAnnotation: Annotation = {
      ...annotation,
      id: uuidv4(),
    };

    setImages(prev => prev.map(img => 
      img.id === imageId 
        ? { ...img, annotations: [...img.annotations, newAnnotation], updatedAt: new Date() }
        : img
    ));
  };

  const updateAnnotation = (imageId: string, annotationId: string, updates: Partial<Annotation>) => {
    setImages(prev => prev.map(img => 
      img.id === imageId 
        ? { 
            ...img, 
            annotations: img.annotations.map(ann => 
              ann.id === annotationId ? { ...ann, ...updates } : ann
            ),
            updatedAt: new Date()
          }
        : img
    ));
  };

  const deleteAnnotation = (imageId: string, annotationId: string) => {
    setImages(prev => prev.map(img => 
      img.id === imageId 
        ? { 
            ...img, 
            annotations: img.annotations.filter(ann => ann.id !== annotationId),
            updatedAt: new Date()
          }
        : img
    ));
  };

  const getImageById = (id: string) => {
    return images.find(img => img.id === id);
  };

  const getCollectionById = (id: string) => {
    const findInTree = (collections: Collection[]): Collection | undefined => {
      for (const collection of collections) {
        if (collection.id === id) return collection;
        const found = findInTree(collection.children);
        if (found) return found;
      }
      return undefined;
    };
    return findInTree(collections);
  };

  // Helper function to update collection in tree
  const updateCollectionInTree = (
    collections: Collection[], 
    targetId: string, 
    updater: (collection: Collection) => Collection
  ): Collection[] => {
    return collections.map(collection => {
      if (collection.id === targetId) {
        return updater(collection);
      }
      if (collection.children.length > 0) {
        return {
          ...collection,
          children: updateCollectionInTree(collection.children, targetId, updater)
        };
      }
      return collection;
    });
  };

  const value: ImageContextType = {
    collections,
    images,
    addCollection,
    addImage,
    addAnnotation,
    updateAnnotation,
    deleteAnnotation,
    getImageById,
    getCollectionById,
  };

  return (
    <ImageContext.Provider value={value}>
      {children}
    </ImageContext.Provider>
  );
};
