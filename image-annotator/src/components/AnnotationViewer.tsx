import React from 'react';
import ReactMarkdown from 'react-markdown';
import { Annotation } from '../types';
import './AnnotationViewer.css';

interface AnnotationViewerProps {
  annotation: Annotation;
  position: 'left' | 'right';
}

const AnnotationViewer: React.FC<AnnotationViewerProps> = ({ annotation, position }) => {
  return (
    <div className={`annotation-viewer ${position}`}>
      <div className="annotation-header">
        <h3>{annotation.title || 'Ghi chú'}</h3>
      </div>
      <div className="annotation-content">
        <ReactMarkdown>{annotation.content}</ReactMarkdown>
      </div>
    </div>
  );
};

export default AnnotationViewer;
