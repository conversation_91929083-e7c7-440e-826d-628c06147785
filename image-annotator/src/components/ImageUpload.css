.image-upload {
  width: 100%;
}

.upload-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.upload-area:hover {
  border-color: #667eea;
  background-color: #f0f4ff;
}

.upload-area.dragging {
  border-color: #667eea;
  background-color: #e8f0ff;
  transform: scale(1.02);
}

.upload-area.uploading {
  border-color: #28a745;
  background-color: #f0fff4;
  cursor: not-allowed;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.upload-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.upload-content p {
  margin: 0;
  color: #333;
  font-weight: 500;
}

.upload-content small {
  color: #666;
  font-size: 0.8rem;
}

.upload-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.upload-status p {
  margin: 0;
  color: #28a745;
  font-weight: 500;
}
