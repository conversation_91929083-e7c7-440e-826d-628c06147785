.annotation-editor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 2rem;
}

.annotation-editor {
  background: white;
  border-radius: 8px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.editor-header {
  padding: 1rem;
  border-bottom: 1px solid #ddd;
  display: flex;
  gap: 1rem;
  align-items: center;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.title-input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 600;
}

.editor-controls {
  display: flex;
  gap: 0.5rem;
}

.editor-controls button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.preview-btn {
  background: #6c757d;
  color: white;
}

.preview-btn:hover {
  background: #5a6268;
}

.preview-btn.active {
  background: #667eea;
}

.save-btn {
  background: #28a745;
  color: white;
}

.save-btn:hover {
  background: #218838;
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover {
  background: #c82333;
}

.close-btn {
  background: #6c757d;
  color: white;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.close-btn:hover {
  background: #5a6268;
}

.editor-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.content-textarea {
  flex: 1;
  padding: 1rem;
  border: none;
  resize: none;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  outline: none;
}

.preview-pane {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.preview-pane h1,
.preview-pane h2,
.preview-pane h3,
.preview-pane h4,
.preview-pane h5,
.preview-pane h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #333;
}

.preview-pane p {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: #555;
}

.preview-pane ul,
.preview-pane ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.preview-pane li {
  margin-bottom: 0.25rem;
  line-height: 1.5;
}

.preview-pane code {
  background: #f4f4f4;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.preview-pane pre {
  background: #f4f4f4;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  margin-bottom: 1rem;
}

.preview-pane pre code {
  background: none;
  padding: 0;
}

.preview-pane blockquote {
  border-left: 4px solid #667eea;
  margin: 1rem 0;
  padding-left: 1rem;
  color: #666;
  font-style: italic;
}

.editor-footer {
  padding: 0.5rem 1rem;
  border-top: 1px solid #ddd;
  background: #f8f9fa;
  border-radius: 0 0 8px 8px;
}

.markdown-help small {
  color: #666;
  font-size: 0.8rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .annotation-editor-overlay {
    padding: 1rem;
  }
  
  .editor-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .editor-controls {
    justify-content: space-between;
  }
  
  .editor-controls button {
    flex: 1;
    padding: 0.4rem 0.5rem;
    font-size: 0.8rem;
  }
}
