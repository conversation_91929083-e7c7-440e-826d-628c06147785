import React, { useState } from 'react';
import { useImageContext } from '../contexts/ImageContext';
import { Collection } from '../types';
import './CollectionTree.css';

interface CollectionTreeProps {
  collections: Collection[];
  selectedId: string;
  onSelect: (id: string) => void;
}

const CollectionTree: React.FC<CollectionTreeProps> = ({ collections, selectedId, onSelect }) => {
  const { addCollection } = useImageContext();
  const [expandedIds, setExpandedIds] = useState<Set<string>>(new Set(['root']));
  const [newCollectionName, setNewCollectionName] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [parentId, setParentId] = useState<string>('');

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedIds);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedIds(newExpanded);
  };

  const handleAddCollection = () => {
    if (newCollectionName.trim()) {
      addCollection(newCollectionName.trim(), parentId || undefined);
      setNewCollectionName('');
      setShowAddForm(false);
      setParentId('');
    }
  };

  const renderCollection = (collection: Collection, level: number = 0) => {
    const hasChildren = collection.children.length > 0;
    const isExpanded = expandedIds.has(collection.id);
    const isSelected = selectedId === collection.id;

    return (
      <div key={collection.id} className="collection-item">
        <div 
          className={`collection-header ${isSelected ? 'selected' : ''}`}
          style={{ paddingLeft: `${level * 20}px` }}
        >
          {hasChildren && (
            <button 
              className="expand-button"
              onClick={() => toggleExpanded(collection.id)}
            >
              {isExpanded ? '▼' : '▶'}
            </button>
          )}
          <span 
            className="collection-name"
            onClick={() => onSelect(collection.id)}
          >
            📁 {collection.name}
          </span>
          <button 
            className="add-subcollection-btn"
            onClick={() => {
              setParentId(collection.id);
              setShowAddForm(true);
            }}
            title="Thêm thư mục con"
          >
            +
          </button>
        </div>
        
        {hasChildren && isExpanded && (
          <div className="collection-children">
            {collection.children.map(child => renderCollection(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="collection-tree">
      {collections.map(collection => renderCollection(collection))}
      
      <div className="add-collection-section">
        {!showAddForm ? (
          <button 
            className="add-collection-btn"
            onClick={() => setShowAddForm(true)}
          >
            + Thêm bộ sưu tập
          </button>
        ) : (
          <div className="add-collection-form">
            <input
              type="text"
              placeholder="Tên bộ sưu tập"
              value={newCollectionName}
              onChange={(e) => setNewCollectionName(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleAddCollection()}
              autoFocus
            />
            <div className="form-buttons">
              <button onClick={handleAddCollection}>Thêm</button>
              <button onClick={() => {
                setShowAddForm(false);
                setNewCollectionName('');
                setParentId('');
              }}>
                Hủy
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CollectionTree;
