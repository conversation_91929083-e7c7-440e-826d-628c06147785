.collection-tree {
  width: 100%;
}

.collection-item {
  margin-bottom: 2px;
}

.collection-header {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.collection-header:hover {
  background-color: #f0f0f0;
}

.collection-header.selected {
  background-color: #667eea;
  color: white;
}

.expand-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-right: 0.5rem;
  font-size: 0.8rem;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collection-name {
  flex: 1;
  font-size: 0.9rem;
}

.add-subcollection-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.2rem 0.4rem;
  border-radius: 2px;
  font-size: 0.8rem;
  opacity: 0;
  transition: opacity 0.2s;
}

.collection-header:hover .add-subcollection-btn {
  opacity: 1;
}

.collection-header.selected .add-subcollection-btn {
  color: white;
}

.add-subcollection-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.collection-children {
  margin-left: 0;
}

.add-collection-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.add-collection-btn {
  width: 100%;
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.add-collection-btn:hover {
  background: #5a6fd8;
}

.add-collection-form {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.add-collection-form input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.form-buttons {
  display: flex;
  gap: 0.5rem;
}

.form-buttons button {
  flex: 1;
  padding: 0.4rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.form-buttons button:first-child {
  background: #28a745;
  color: white;
}

.form-buttons button:first-child:hover {
  background: #218838;
}

.form-buttons button:last-child {
  background: #6c757d;
  color: white;
}

.form-buttons button:last-child:hover {
  background: #5a6268;
}
