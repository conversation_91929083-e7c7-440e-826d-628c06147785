.annotation-viewer {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  width: 400px;
  max-height: 80vh;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  overflow: hidden;
  border: 1px solid #ddd;
}

.annotation-viewer.left {
  left: 20px;
}

.annotation-viewer.right {
  right: 20px;
}

.annotation-header {
  background: #667eea;
  color: white;
  padding: 1rem;
  border-bottom: 1px solid #ddd;
}

.annotation-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.annotation-content {
  padding: 1rem;
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

.annotation-content h1,
.annotation-content h2,
.annotation-content h3,
.annotation-content h4,
.annotation-content h5,
.annotation-content h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #333;
}

.annotation-content p {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: #555;
}

.annotation-content ul,
.annotation-content ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.annotation-content li {
  margin-bottom: 0.25rem;
  line-height: 1.5;
}

.annotation-content code {
  background: #f4f4f4;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.annotation-content pre {
  background: #f4f4f4;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  margin-bottom: 1rem;
}

.annotation-content pre code {
  background: none;
  padding: 0;
}

.annotation-content blockquote {
  border-left: 4px solid #667eea;
  margin: 1rem 0;
  padding-left: 1rem;
  color: #666;
  font-style: italic;
}

.annotation-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.annotation-content th,
.annotation-content td {
  border: 1px solid #ddd;
  padding: 0.5rem;
  text-align: left;
}

.annotation-content th {
  background: #f8f9fa;
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .annotation-viewer {
    width: calc(100vw - 40px);
    left: 20px !important;
    right: 20px !important;
  }
}
